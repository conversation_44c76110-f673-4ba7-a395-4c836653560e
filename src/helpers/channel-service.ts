import {
  commandsChatHttpClient,
  HttpClient,
  V3Channel,
  V3CreateChannelRequest,
} from '../../utils/http-client/commands-chat-client';
import { V3SendDMMessageRequest } from '../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import { NULL_UNDEFINED_ERROR } from '../tests/error-message';
import channelViewClient from './channel-view-service';
import invitationClient from './invitation-service';
import memberClient from './member-service';
import messageService from './message-service';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const channelClient = new commandsChatHttpClient(http).channel;

export default channelClient;

/**
 * @param request
 * @param members The first position is the owner
 * @param admins
 */
export const mockChannelData = async (
  request: V3CreateChannelRequest,
  members: V3MockedUser[],
  admins: V3MockedUser[] = [],
): Promise<V3Channel> => {
  const [owner, ...memberList] = members;

  const createChannelRes = await getResponseSuccess(
    request,
    channelClient.createChannel,
    createHeaders(owner.token as string),
  );

  let channel = createChannelRes?.data?.channel;
  const inviteLink = channel?.invitationLink as string;

  for (const user of memberList) {
    const acceptInvitationRes = await getResponseSuccess(
      { invitationLink: inviteLink },
      invitationClient.acceptInvitation,
      createHeaders(user.token as string),
    );

    channel = acceptInvitationRes.data?.channel;
  }

  for (const user of admins) {
    await getResponseSuccess(
      {
        userId: user.userId as string,
        channelId: channel?.channelId as string,
        workspaceId: channel?.workspaceId as string,
      },
      memberClient.assignAsAdmin,
      createHeaders(owner.token as string),
    );
  }

  return channel as V3Channel;
};

export const getDMAfterSendMsg = async (
  sender: V3MockedUser,
  receiver: V3MockedUser,
): Promise<V3Channel> => {
  const headers = createHeaders(sender.token as string);
  const sendDmMessageRequest: V3SendDMMessageRequest = {
    ref: 'ref',
    userId: receiver.userId as string,
    content: 'hello',
  };

  await getResponseSuccess(
    sendDmMessageRequest,
    messageService.sendDmMessage,
    headers,
  );

  const dmChannel = await getResponseSuccess(
    { userId: receiver.userId },
    channelViewClient.getDmChannel,
    headers,
  );
  if (!dmChannel.data?.channel) throw new Error(NULL_UNDEFINED_ERROR);
  return dmChannel.data?.channel;
};
