import { V3Message } from '../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import {
  HttpClient,
  viewsMessageHttpClient,
} from '../../utils/http-client/views-message-client';
import { ZIICHAT_BOT_USERID } from '../tests/const';
import { NULL_UNDEFINED_ERROR } from '../tests/error-message';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const messageViewClient = new viewsMessageHttpClient(http).messageView;

export default messageViewClient;

export const getLastMsgInBot = async (
  requester: V3MockedUser,
): Promise<V3Message> => {
  const listMsg = await getResponseSuccess(
    { userId: ZIICHAT_BOT_USERID },
    messageViewClient.listDmMessages,
    createHeaders(requester.token as string),
  );

  if (!listMsg.data) throw new Error(NULL_UNDEFINED_ERROR);
  return listMsg.data[0].message as V3Message;
};
