import {
  HttpClient,
  V3AvatarFrameData,
  V3ListAvatarFrameCollectionResponse,
  viewsChatHttpClient,
} from '../../utils/http-client/views-chat-client';
import { HEADERS } from '../tests/const';
import { getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
export const avatarFrameViewClient = new viewsChatHttpClient(http).avatarFrame;

export const getRandomAvatarFrame = async (
  headers: HEADERS,
): Promise<V3AvatarFrameData> => {
  const listAvtFrame: V3ListAvatarFrameCollectionResponse =
    await getResponseSuccess(
      {},
      avatarFrameViewClient.listAvatarFrameCollection,
      headers,
    );

  const avatarFrameList = listAvtFrame.data?.flatMap(
    (item) => item.avatarFrames,
  ) as V3AvatarFrameData[];

  const randomIndex = Math.floor(Math.random() * avatarFrameList.length);

  return avatarFrameList[randomIndex] as V3AvatarFrameData;
};
