import { getConfig } from '../../../utils/get-config';
import { Environment } from '../../tests/const';

export const getEnvUrl = (): string => {
  if (getEnvCurrent() == Environment.LIVE) {
    return getConfig<string>('httpGlobal.urlLive');
  }
  return getConfig<string>('httpGlobal.url');
};

export const getEnvCurrent = (): string => {
  return getConfig('envCurrent');
};

const url = getEnvUrl();
export default url;
