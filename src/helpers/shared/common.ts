import { faker } from '@faker-js/faker';
import { StatusCodes } from 'http-status-codes';
import { isArray, isObject, map, omit } from 'lodash';
import * as request from 'supertest';
import { StringUtils } from 'turbocommons-ts';

import { V3DataInclude } from '../../../utils/http-client/commands-chat-client';
import {
  V3ErrorResponse,
  V3Paging,
} from '../../../utils/http-client/suggestion-client';
import { V3DataIncludeResponse } from '../../../utils/http-client/views-message-client';
import {
  HEADERS,
  PREFIX_HOST,
  PREFIX_SHORT_LINK,
  REDIRECTION_MESSAGES,
} from '../../tests/const';

export interface BaseResponse {
  ok?: boolean | null;
  data?: object;
  error?: V3ErrorResponse;
  includes?: V3DataIncludeResponse | V3DataInclude;
}

export type ClientMethod<TReq, TRes> = {
  (request: TReq, headers?: HEADERS): Promise<{
    status: number;
    data: TRes;
    error: object;
  }>;
};

export const getResponseSuccess = async <TReq, TRes>(
  request: TReq,
  method: ClientMethod<TReq, TRes>,
  headers?: HEADERS,
): Promise<TRes> => {
  const response = headers
    ? await method(request, headers)
    : await method(request);
  const { data, status } = response;

  if (status !== StatusCodes.OK) throw response.error;
  expect(status).toEqual(StatusCodes.OK);

  if (!(data as BaseResponse).ok)
    throw new Error(`${JSON.stringify(response, null, 2)} ${method.name}`);
  expect((data as BaseResponse).ok).toBeTruthy();
  expect((data as BaseResponse).error).toBeUndefined();
  return data;
};

export const createHeaders = (token: string): HEADERS => ({
  headers: { 'x-session-token': token },
});

export const assertIsDate = (value: string): void => {
  expect(value).toBeDefined();
  const date = {
    date: value,
  };
  const isoPatternDate = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
  expect(date).toEqual(
    expect.objectContaining({ date: expect.stringMatching(isoPatternDate) }),
  );
};

export const assertIsString = (value: string): void => {
  expect(value).toBeDefined();
  expect(typeof value).toBe('string');
  expect(value.length).toBeGreaterThan(0);
};

export const checkURLExist = async (
  host: string,
  path: string,
  statusCode: StatusCodes | string = StatusCodes.OK,
): Promise<boolean> => {
  if (path.includes(PREFIX_SHORT_LINK)) {
    path = path.replace(PREFIX_SHORT_LINK, '');
  } else {
    path = path.replace(PREFIX_HOST, '');
  }
  const hostIsUrl = StringUtils.isUrl(host);

  if (!hostIsUrl) return false;

  const status = await request(host)
    .get(path)
    .then(function ({ status }) {
      return status;
    })
    .catch((error) => {
      return error;
    });

  if (statusCode == REDIRECTION_MESSAGES) {
    return status >= 300 && status < 400;
  }
  return status == statusCode;
};

export const wait = async (time = 2000): Promise<void> => {
  await new Promise((res) => setTimeout(res, time));
};

export const expectGreaterThanTime = (
  createTime: string,
  updateTime: string,
): void => {
  assertIsDate(createTime);
  assertIsDate(updateTime);
  const createDate = new Date(createTime);
  const updateDate = new Date(updateTime);
  expect(updateDate > createDate).toBeTruthy();
};

function utf8ByteLength(emoji: string): number {
  return new TextEncoder().encode(emoji).length;
}

export const randomEmojis = (length: number, numByte?: number): string => {
  if (!numByte)
    return Array.from({ length: length }, () => faker.internet.emoji()).join(
      '',
    );

  const emojis: string[] = [];

  while (emojis.length < length) {
    const emoji = faker.internet.emoji();
    if (utf8ByteLength(emoji) == numByte) {
      emojis.push(emoji);
    }
  }

  return emojis.join('');
};

export const getEnumValues = (
  enumObj: object,
  exclude: number[] = [],
): number[] => {
  const values: number[] = [];
  for (const key in enumObj) {
    if (!isNaN(Number(key))) {
      // Skip numeric keys
      continue;
    }
    const val = enumObj[key];
    if (!exclude.includes(val)) {
      values.push(enumObj[key]);
    }
  }
  return values;
};

export function deletePresenceDataObj(obj: unknown): unknown {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(deletePresenceDataObj);
  }

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (key === 'presenceData') {
        delete obj[key];
      } else {
        obj[key] = deletePresenceDataObj(obj[key]);
      }
    }
  }
  return obj;
}

export const removeFields = (obj: object, fields: string[]): object => {
  if (!isObject(obj) && !isArray(obj)) {
    return obj;
  }

  for (const fieldPath of fields) {
    const isPath = fieldPath.includes('.');
    const [currentKey, ...remainingPath] = fieldPath
      .split(/[.[\]]/)
      .filter(Boolean);

    if (isPath) {
      if (isArray(obj)) {
        obj = map(obj, (item) =>
          isObject(item) && currentKey in item
            ? {
                ...item,
                [currentKey]: removeFields(item[currentKey], [
                  remainingPath.join('.'),
                ]),
              }
            : item,
        );
      } else if (isObject(obj) && currentKey in obj) {
        obj = {
          ...obj,
          [currentKey]: removeFields(obj[currentKey], [
            remainingPath.join('.'),
          ]),
        };
      }
    } else {
      if (isArray(obj)) {
        obj = map(obj, (item) => removeFields(item, [fieldPath]));
      } else if (isObject(obj)) {
        const updatedObj = omit(obj, [fieldPath]);
        for (const key in updatedObj) {
          if (isObject(updatedObj[key]) || isArray(updatedObj[key])) {
            updatedObj[key] = removeFields(updatedObj[key], [fieldPath]);
          }
        }
        obj = updatedObj;
      }
    }
  }

  return obj;
};

export const getRequireMsgError = (field: string[]): string[] => {
  const detailsMsg: string[] = [];
  for (const value of field) {
    detailsMsg.push(`must have required property ${value}`);
  }
  return detailsMsg;
};

export const getPreviousTimeWithSeconds = (
  time: string,
  milliSecond: number,
): string => {
  const date = new Date(time);
  return new Date(date.getTime() - milliSecond).toISOString();
};

export const getNextTimeWithSeconds = (
  time: string,
  milliSecond: number,
): string => {
  const date = new Date(time);
  return new Date(date.getTime() + milliSecond).toISOString();
};

export const expectOnlyOnePageResponse = (paging: V3Paging): void => {
  expect(paging.hasNext).toBeFalsy();
  expect(paging.hasPrev).toBeFalsy();
  expect(paging.nextPageToken).toBeUndefined();
  expect(paging.prevPageToken).toBeUndefined();
};
