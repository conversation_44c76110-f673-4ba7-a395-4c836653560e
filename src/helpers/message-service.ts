import { faker } from '@faker-js/faker';
import { ulid } from 'ulid';

import { V3Channel } from '../../utils/http-client/cloudevent-client';
import {
  commandsMessageHttpClient,
  HttpClient,
  V3SendMessageRequest,
} from '../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import { V3Message } from '../../utils/http-client/views-chat-client';
import { HEADERS } from '../tests/const';
import { NULL_UNDEFINED_ERROR } from '../tests/error-message';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const messageClient = new commandsMessageHttpClient(http).message;
export default messageClient;

export const mockDmMessageData = async (
  actorHeaders: HEADERS,
  recipient: V3MockedUser,
  content: string,
): Promise<V3Message> => {
  const mockedMessage = await getResponseSuccess(
    {
      userId: recipient.userId as string,
      ref: 'ref',
      content: content,
    },
    messageClient.sendDmMessage,
    actorHeaders,
  );
  if (!mockedMessage.data || !mockedMessage.data?.message)
    throw new Error(NULL_UNDEFINED_ERROR);
  return mockedMessage.data.message;
};

export const mockMessageData = async (
  chanel: V3Channel,
  sender: V3MockedUser,
  content: string = faker.string.alphanumeric(5),
): Promise<V3Message> => {
  const headers = createHeaders(sender.token as string);

  const sendMessageParam: V3SendMessageRequest = {
    workspaceId: chanel.workspaceId as string,
    channelId: chanel.channelId as string,
    ref: ulid(),
    content: content,
  };

  const mockedMessage = await getResponseSuccess(
    sendMessageParam,
    messageClient.sendMessage,
    headers,
  );
  return mockedMessage.data?.message as V3Message;
};
