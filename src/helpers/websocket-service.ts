import * as WebSocket from 'ws';

import {
  V3DataInclude,
  V3Friend,
} from '../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import {
  HttpClient,
  websocketManagerHttpClient,
} from '../../utils/http-client/websocket-manager-client';
import { cloudEventType, HEADERS } from '../tests/const';
import { NULL_UNDEFINED_ERROR } from '../tests/error-message';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });

export const wsClient = new websocketManagerHttpClient(http).websocketManager;

export interface cloudEvent {
  id: string;
  time: string;
  type: string;
  source: string;
  specversion: string;
  data: object;
  version?: string;
}

export interface UserWS {
  user: V3MockedUser;
  events: cloudEvent[];
}

export interface FriendDataWS {
  friendRequest: V3Friend;
  includes: V3DataInclude;
}

export const openConnectionOfUser = async (
  headers: HEADERS,
): Promise<string> => {
  const connection = await getResponseSuccess(
    {},
    wsClient.openConnection,
    headers,
  );
  if (!connection.connectParams || !connection.connectParams.url)
    throw new Error(NULL_UNDEFINED_ERROR);
  expect(connection.ok).toBeTruthy();
  expect(connection.error).toBeUndefined();
  expect(connection.connectParams.url).toBeDefined();
  return connection.connectParams.url;
};

export const receiveWebSocketEvent = async (
  webSocket: WebSocket,
  listEvent: cloudEvent[],
): Promise<void> => {
  await new Promise<void>((resolve, reject) => {
    webSocket.on('message', (binaryData) => {
      try {
        listEvent.push(JSON.parse(binaryData));
      } catch (error) {
        reject(error);
      }
      resolve(binaryData);
    });
    webSocket.on('error', (error) => {
      console.error('Error in receiveWebSocketEvent:', error);
      reject(new Error('Could not connect to ws'));
    });
  });
};

export const connectWebsocket = async (
  paramsConnect: UserWS[],
): Promise<WebSocket[]> => {
  const wsList: WebSocket[] = [];
  for (const { user, events } of paramsConnect) {
    const ws = new WebSocket(
      await openConnectionOfUser(createHeaders(user.token as string)),
    );
    await receiveWebSocketEvent(ws, events);
    wsList.push(ws);
  }
  return wsList;
};

export const disconnectWS = async (
  webSocketList: WebSocket[],
): Promise<void> => {
  for (const ws of webSocketList) {
    ws.close();
  }
};

export const getActorSource = (events: cloudEvent[]): string => {
  const gatewayConnectedEvent = events.find((event) => {
    return event.type == cloudEventType.GATEWAY_CONNECTED_EVENT;
  });
  if (!gatewayConnectedEvent) throw new Error(NULL_UNDEFINED_ERROR);
  return gatewayConnectedEvent.source;
};

export const resumeWebSocketByTime = async (
  websocket: WebSocket,
  lastEvent: cloudEvent,
): Promise<void> => {
  await new Promise<void>((resolve, reject) => {
    const messageSendServer = `{
          "id": "",
          "time": "",
          "type": "${cloudEventType.RECONNECT_STARTED_EVENT}",
          "source": "",
          "specversion": "${lastEvent.specversion}",
          "data": {
              "token": "${lastEvent.time}"
          }
        }`;
    try {
      websocket.send(messageSendServer);
    } catch (error) {
      reject(error);
    }
    resolve();
  });
};

export const resumeWebSocketById = async (
  websocket: WebSocket,
  lastEvent: cloudEvent,
): Promise<void> => {
  await new Promise<void>((resolve, reject) => {
    const messageSendServer = `{
          "id": "",
          "time": "",
          "type": "${cloudEventType.RECONNECT_STARTED_EVENT}",
          "source": "",
          "specversion": "${lastEvent.specversion}",
          "data": {
              "token": "${lastEvent.id}"
          }
        }`;
    try {
      websocket.send(messageSendServer);
    } catch (error) {
      reject(error);
    }
    resolve();
  });
};
