import {
  fakerHttpClient,
  HttpClient,
  V3MockedUser,
  V3MockUsersRequest,
  V3UserBadgeTypeEnum,
} from '../../utils/http-client/faker-client';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const fakerClient = new fakerHttpClient(http).internalFaker;

export default fakerClient;

export const mockUsersData = async (
  prefix: string,
  quantity: number,
  badge = V3UserBadgeTypeEnum.USER_BADGE_TYPE_DEFAULT,
): Promise<V3MockedUser[]> => {
  const request: V3MockUsersRequest = { prefix, quantity, badge };
  const { data } = await fakerClient.mockUsers(request);
  return data.data as V3MockedUser[];
};
