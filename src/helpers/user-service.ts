import {
  commandsUserDataHttpClient,
  HttpClient,
} from '../../utils/http-client/commands-user-data-client';
import { HEADERS } from '../tests/const';
import { getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });

export const userProfileClient = new commandsUserDataHttpClient(http)
  .userProfile;
export const userSettingClient = new commandsUserDataHttpClient(http)
  .userSetting;
export const userReportClient = new commandsUserDataHttpClient(http).userReport;

export const deleteStatus = async (metadataList: HEADERS[]): Promise<void> => {
  for (const metadata of metadataList) {
    await getResponseSuccess({}, userProfileClient.deleteUserStatus, metadata);
  }
};
