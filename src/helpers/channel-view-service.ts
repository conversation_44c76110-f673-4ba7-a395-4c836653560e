import { V3Channel } from '../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import {
  HttpClient,
  viewsChatHttpClient,
} from '../../utils/http-client/views-chat-client';
import { ZIICHAT_BOT_USERID } from '../tests/const';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const channelViewClient = new viewsChatHttpClient(http).channelView;

export default channelViewClient;

export const getChannelBotData = async (
  user: V3MockedUser,
): Promise<V3Channel> => {
  const channelRes = await getResponseSuccess(
    { userId: ZIICHAT_BOT_USERID },
    channelViewClient.getDmChannel,
    createHeaders(user.token as string),
  );

  return channelRes?.data?.channel as V3Channel;
};
