import { V3MockedUser } from '../../utils/http-client/faker-client';
import {
  HttpClient,
  viewsChatHttpClient,
} from '../../utils/http-client/views-chat-client';
import { NULL_UNDEFINED_ERROR } from '../tests/error-message';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';
import { FriendDataWS } from './websocket-service';

const http = new HttpClient({ baseUrl: url });
const friendViewClient = new viewsChatHttpClient(http).friendView;

export default friendViewClient;

export const getFriendDataForExpectWS = async (
  requestedUser: V3MockedUser,
  requester: V3MockedUser,
): Promise<FriendDataWS> => {
  const getFriendRes = await getResponseSuccess(
    { userId: requestedUser.userId },
    friendViewClient.getFriend,
    createHeaders(requester.token as string),
  );

  const { data, includes } = getFriendRes;
  if (!includes) throw new Error(NULL_UNDEFINED_ERROR);
  if (!data) throw new Error(NULL_UNDEFINED_ERROR);
  if (!data.friend) throw new Error(NULL_UNDEFINED_ERROR);

  return { friendRequest: data.friend, includes };
};
