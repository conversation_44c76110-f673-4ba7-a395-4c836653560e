import {
  commandsChatHttpClient,
  HttpClient,
  V3FriendData,
} from '../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import friendViewClient from './friend-view-service';
import { getResponseSuccess, wait } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });
const friendClient = new commandsChatHttpClient(http).friend;

export default friendClient;

export const createAddAndAcceptFriends = async (
  userSendFriendReq: V3MockedUser,
  userAcceptFriendReqList: V3MockedUser[],
): Promise<V3FriendData[]> => {
  const metaDataAddFriend = {
    headers: { 'x-session-token': userSendFriendReq.token },
  };

  for (const user of userAcceptFriendReqList) {
    await getResponseSuccess(
      {
        userId: user.userId,
      },
      friendClient.addFriend,
      metaDataAddFriend,
    );
    await wait();
    const metaDataAccept = {
      headers: { 'x-session-token': user.token },
    };

    await getResponseSuccess(
      {
        userId: userSendFriendReq.userId,
      },
      friendClient.acceptFriendRequest,
      metaDataAccept,
    );
  }

  const mainUserFriendListResponse = await getResponseSuccess(
    {},
    friendViewClient.listFriends,
    metaDataAddFriend,
  );

  return mainUserFriendListResponse.data as V3FriendData[];
};
