import { getPrefixMockUser } from '../../jest-e2e';
import { V3MockedUser } from '../../utils/http-client/faker-client';
import {
  HttpClient,
  V3UserView,
  viewsChatHttpClient,
} from '../../utils/http-client/views-chat-client';
import { mockUsersData } from './internal-faker-service';
import { createHeaders, getResponseSuccess } from './shared/common';
import url from './shared/url';

const http = new HttpClient({ baseUrl: url });

export const userViewClient = new viewsChatHttpClient(http).userView;

export const getUserProfile = async (
  user: V3MockedUser,
): Promise<V3UserView> => {
  const [mockUser] = await mockUsersData(getPrefixMockUser(), 2);
  const metadata = createHeaders(mockUser.token as string);

  const getUserRes = await getResponseSuccess(
    { userId: user.userId },
    userViewClient.getUser,
    metadata,
  );
  delete getUserRes.data?.presenceData;
  return getUserRes.data as V3UserView;
};
