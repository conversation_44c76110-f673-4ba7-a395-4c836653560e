import { ulid } from 'ulid';
import * as WebSocket from 'ws';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import {
  expectEventsCreateChannel,
  expectResumeWS,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../../helpers/websocket-service';
import {
  CONFIG_FILE_STORE_HOST,
  Methods,
  WORKSPACE_ID,
} from '../../../../const';

describe(`WebSocket ${Methods.CREATE_CHANNEL}`, () => {
  const prefix = getPrefixMockUser();
  let userDefault: V3MockedUser;
  let userBlue: V3MockedUser;
  let recipient: V3MockedUser;

  let websocketList: WebSocket[];
  let paramConnect: UserWS[] = [];

  let userDefaultEvents: cloudEvent[] = [];
  let userBlueEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    [userDefault, recipient] = await mockUsersData(prefix, 2);
    [userBlue] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );
  });

  afterEach(async () => {
    await disconnectWS(websocketList);
    userDefaultEvents = [];
    userBlueEvents = [];
    recipientEvents = [];
  });

  describe('Channel 1-n', () => {
    beforeEach(async () => {
      paramConnect = [
        {
          user: userDefault,
          events: userDefaultEvents,
        },
        {
          user: recipient,
          events: recipientEvents,
        },
      ];
      websocketList = await connectWebsocket(paramConnect);
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (no userIds, no avatar)', async () => {
      await expectEventsCreateChannel(createChannelReq, paramConnect);
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (has userIds, no avatar)', async () => {
      await expectEventsCreateChannel(
        { ...createChannelReq, userIds: [recipient.userId] },
        paramConnect,
        true,
      );
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (all params)', async () => {
      await expectEventsCreateChannel(
        {
          ...createChannelReq,
          userIds: [recipient.userId],
          avatarPath: `${CONFIG_FILE_STORE_HOST}abc.png`,
        },
        paramConnect,
        true,
      );

      await expectResumeWS(paramConnect);
    });
  });

  describe('Broadcast Channel', () => {
    beforeEach(async () => {
      paramConnect = [
        {
          user: userBlue,
          events: userBlueEvents,
        },
        {
          user: recipient,
          events: recipientEvents,
        },
      ];
      websocketList = await connectWebsocket(paramConnect);
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (no userIds, no avatar)', async () => {
      await expectEventsCreateChannel(
        {
          ...createChannelReq,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
        },
        paramConnect,
      );
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (has userIds, no avatar)', async () => {
      await expectEventsCreateChannel(
        {
          ...createChannelReq,
          userIds: [recipient.userId],
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
        },
        paramConnect,
        true,
      );
    });

    it('should actor receive ChannelCreatedEvent, MessageCreatedEvent (all params)', async () => {
      await expectEventsCreateChannel(
        {
          ...createChannelReq,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
          userIds: [recipient.userId],
          avatarPath: `${CONFIG_FILE_STORE_HOST}abc.png`,
        },
        paramConnect,
        true,
      );
      await expectResumeWS(paramConnect);
    });
  });
});
