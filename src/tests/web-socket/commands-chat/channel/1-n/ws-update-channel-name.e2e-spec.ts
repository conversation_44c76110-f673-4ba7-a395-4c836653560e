import { faker } from '@faker-js/faker';
import { ulid } from 'ulid';
import * as WebSocket from 'ws';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Channel,
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
  V3UpdateChannelNameRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import {
  expectResumeWS,
  expectUpdateChannelNameEvents,
} from '../../../../../expected-results/realtime/websocket';
import { mockChannelData } from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../../helpers/websocket-service';
import { MaxLength, Methods, MinLength, WORKSPACE_ID } from '../../../../const';

describe(`WebSocket ${Methods.UPDATE_CHANNEL_NAME}`, () => {
  const prefix = getPrefixMockUser();
  let userDefault: V3MockedUser;
  let userBlue: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;

  let channel: V3Channel;
  let broadcastChannel: V3Channel;

  let websocketList: WebSocket[];
  let paramConnect: UserWS[] = [];

  let userDefaultEvents: cloudEvent[] = [];
  let userBlueEvents: cloudEvent[] = [];

  let adminEvents: cloudEvent[] = [];
  let memberEvents: cloudEvent[] = [];

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  let updateChannelNameReq: V3UpdateChannelNameRequest;
  let updateBrChannelNameReq: V3UpdateChannelNameRequest;
  const name = faker.string.alpha(MaxLength.CHANNEL_NAME);

  beforeAll(async () => {
    [userDefault, admin, member] = await mockUsersData(prefix, 3);
    [userBlue] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );

    channel = await mockChannelData(
      createChannelReq,
      [userDefault, admin, member],
      [admin],
    );
    updateChannelNameReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
      name,
    };

    broadcastChannel = await mockChannelData(
      {
        ...createChannelReq,
        channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
      },
      [userBlue, admin, member],
      [admin],
    );

    updateBrChannelNameReq = {
      channelId: broadcastChannel.channelId as string,
      workspaceId: broadcastChannel.workspaceId as string,
      name,
    };
  });

  afterEach(async () => {
    await disconnectWS(websocketList);

    userDefaultEvents = [];
    userBlueEvents = [];
    adminEvents = [];
    memberEvents = [];
  });

  describe('Channel 1-n', () => {
    beforeEach(async () => {
      paramConnect = [
        { user: userDefault, events: userDefaultEvents },
        { user: admin, events: adminEvents },
        { user: member, events: memberEvents },
      ];
      websocketList = await connectWebsocket(paramConnect);
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when owner update channel name', async () => {
      await expectUpdateChannelNameEvents(updateChannelNameReq, paramConnect);
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when admin update channel name', async () => {
      await expectUpdateChannelNameEvents(
        {
          ...updateChannelNameReq,
          name: faker.string.alpha(MinLength.CHANNEL_NAME),
        },
        [
          { user: admin, events: adminEvents }, // actor
          { user: userDefault, events: userDefaultEvents },
          { user: member, events: memberEvents },
        ],
      );
      await expectResumeWS(paramConnect);
    });
  });

  describe('Broadcast Channel ', () => {
    beforeEach(async () => {
      paramConnect = [
        { user: userBlue, events: userBlueEvents },
        { user: admin, events: adminEvents },
        { user: member, events: memberEvents },
      ];
      websocketList = await connectWebsocket(paramConnect);
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when owner update channel name', async () => {
      await expectUpdateChannelNameEvents(
        {
          ...updateBrChannelNameReq,
          name: faker.string.alpha(MinLength.CHANNEL_NAME),
        },
        paramConnect,
      );
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when admin update channel name', async () => {
      await expectUpdateChannelNameEvents(updateBrChannelNameReq, [
        { user: admin, events: adminEvents }, // actor
        { user: userBlue, events: userBlueEvents },
        { user: member, events: memberEvents },
      ]);
      await expectResumeWS(paramConnect);
    });
  });
});
