import { ulid } from 'ulid';
import * as WebSocket from 'ws';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Channel,
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
  V3DeleteChannelAvatarRequest,
  V3UpdateChannelAvatarRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import {
  expectDeleteChannelAvatar,
  expectResumeWS,
} from '../../../../../expected-results/realtime/websocket';
import channelClient, {
  mockChannelData,
} from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../../helpers/websocket-service';
import {
  CONFIG_FILE_STORE_HOST,
  Methods,
  WORKSPACE_ID,
} from '../../../../const';

describe(`WebSocket ${Methods.DELETE_CHANNEL_AVATAR}`, () => {
  const prefix = getPrefixMockUser();
  let userDefault: V3MockedUser;
  let userBlue: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;

  let channel: V3Channel;
  let broadcastChannel: V3Channel;

  let websocketList: WebSocket[];
  let paramConnect: UserWS[] = [];

  let userDefaultEvents: cloudEvent[] = [];
  let userBlueEvents: cloudEvent[] = [];

  let adminEvents: cloudEvent[] = [];
  let memberEvents: cloudEvent[] = [];

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  let updateChannelAvtReq: V3UpdateChannelAvatarRequest;
  let updateBrChannelAvtReq: V3UpdateChannelAvatarRequest;
  const avatarPath = `${CONFIG_FILE_STORE_HOST}${ulid()}/a.jpeg`;

  let deleteChannelAvtReq: V3DeleteChannelAvatarRequest;
  let deleteBrChannelAvtReq: V3DeleteChannelAvatarRequest;

  beforeAll(async () => {
    [userDefault, admin, member] = await mockUsersData(prefix, 3);
    [userBlue] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );

    channel = await mockChannelData(
      createChannelReq,
      [userDefault, admin, member],
      [admin],
    );
    updateChannelAvtReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
      avatarPath,
    };
    deleteChannelAvtReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
    };

    broadcastChannel = await mockChannelData(
      {
        ...createChannelReq,
        channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
      },
      [userBlue, admin, member],
      [admin],
    );

    updateBrChannelAvtReq = {
      channelId: broadcastChannel.channelId as string,
      workspaceId: broadcastChannel.workspaceId as string,
      avatarPath,
    };
    deleteBrChannelAvtReq = {
      channelId: broadcastChannel.channelId as string,
      workspaceId: broadcastChannel.workspaceId as string,
    };
  });

  afterEach(async () => {
    await disconnectWS(websocketList);

    userDefaultEvents = [];
    userBlueEvents = [];
    adminEvents = [];
    memberEvents = [];
  });

  describe('Channel 1-n', () => {
    beforeEach(async () => {
      paramConnect = [
        { user: userDefault, events: userDefaultEvents },
        { user: admin, events: adminEvents },
        { user: member, events: memberEvents },
      ];

      await getResponseSuccess(
        updateChannelAvtReq,
        channelClient.updateChannelAvatar,
        createHeaders(userDefault.token as string),
      );

      websocketList = await connectWebsocket(paramConnect);
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when owner delete channel avatar', async () => {
      await expectDeleteChannelAvatar(deleteChannelAvtReq, paramConnect);
      await expectResumeWS(paramConnect);
    });

    // TODO https://github.com/halonext/ziichat-issues/issues/21062
    it.skip('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when admin delete channel avatar', async () => {
      await expectDeleteChannelAvatar(deleteChannelAvtReq, [
        { user: admin, events: adminEvents }, // actor
        { user: userDefault, events: userDefaultEvents },
        { user: member, events: memberEvents },
      ]);
    });
  });

  describe('Broadcast Channel ', () => {
    beforeEach(async () => {
      paramConnect = [
        { user: userBlue, events: userBlueEvents },
        { user: admin, events: adminEvents },
        { user: member, events: memberEvents },
      ];
      await getResponseSuccess(
        updateBrChannelAvtReq,
        channelClient.updateChannelAvatar,
        createHeaders(admin.token as string),
      );

      websocketList = await connectWebsocket(paramConnect);
    });

    it('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when owner delete channel avatar', async () => {
      await expectDeleteChannelAvatar(deleteBrChannelAvtReq, paramConnect);
      await expectResumeWS(paramConnect);
    });

    // TODO https://github.com/halonext/ziichat-issues/issues/21062
    it.skip('should all users receive ChannelUpdatedEventData, MessageCreatedEvent when admin delete channel avatar', async () => {
      await expectDeleteChannelAvatar(deleteBrChannelAvtReq, [
        { user: admin, events: adminEvents }, // actor
        { user: userBlue, events: userBlueEvents },
        { user: member, events: memberEvents },
      ]);
    });
  });
});
