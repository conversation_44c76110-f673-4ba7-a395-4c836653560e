import { ulid } from 'ulid';
import * as WebSocket from 'ws';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3SendDMMessageRequest } from '../../../../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { V3Message } from '../../../../../../utils/http-client/views-message-client';
import { expectRejectMessageRequestEvents } from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../../helpers/websocket-service';
import { Methods } from '../../../../const';

describe(`WebSocket ${Methods.REJECT_MESSAGE_REQUEST}`, () => {
  const prefix = getPrefixMockUser();

  let actor: V3MockedUser;
  let recipient: V3MockedUser;

  let websocketList: WebSocket[];

  const actorEvents: cloudEvent[] = [];
  const recipientEvents: cloudEvent[] = [];

  let paramConnect: UserWS[] = [];
  let msgSend: V3Message;

  beforeAll(async () => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    paramConnect = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];

    const sendDMMsgReq: V3SendDMMessageRequest = {
      userId: actor.userId as string,
      content: ulid(),
      ref: ulid(),
    };

    const msgRes = await getResponseSuccess(
      sendDMMsgReq,
      messageService.sendDmMessage,
      createHeaders(recipient.token as string),
    );
    msgSend = msgRes?.data?.message as V3Message;
    websocketList = await connectWebsocket(paramConnect);
  });

  afterAll(async () => {
    await disconnectWS(websocketList);
  });

  it('should actor receives IncomingMessageRequestAcceptedEvent and recipient receives OutgoingMessageRequestAcceptedEvent when actor accept message request', async () => {
    await expectRejectMessageRequestEvents(paramConnect, msgSend);
    // TODO https://github.com/halonext/ziichat-issues/issues/21952
    // await expectResumeWS(paramConnect);
  });
});
