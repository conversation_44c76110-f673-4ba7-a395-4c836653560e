import { ulid } from 'ulid';
import * as WebSocket from 'ws';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3SendDMMessageRequest } from '../../../../../../utils/http-client/commands-message-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import {
  expectAcceptMessageRequestEvents,
  expectResumeWS,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../../helpers/websocket-service';
import { Methods } from '../../../../const';

describe(`WebSocket ${Methods.ACCEPT_MESSAGE_REQUEST}`, () => {
  const prefix = getPrefixMockUser();

  let actor: V3MockedUser;
  let recipient: V3MockedUser;

  let websocketList: WebSocket[];

  const actorEvents: cloudEvent[] = [];
  const recipientEvents: cloudEvent[] = [];

  let paramConnect: UserWS[] = [];

  beforeAll(async () => {
    [actor, recipient] = await mockUsersData(
      prefix,
      2,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );

    paramConnect = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];

    const sendDMMsgReq: V3SendDMMessageRequest = {
      userId: actor.userId as string,
      content: ulid(),
      ref: ulid(),
    };

    await getResponseSuccess(
      sendDMMsgReq,
      messageService.sendDmMessage,
      createHeaders(recipient.token as string),
    );
    websocketList = await connectWebsocket(paramConnect);
  });

  afterAll(async () => {
    await disconnectWS(websocketList);
  });

  it('should actor receives IncomingMessageRequestAcceptedEvent and recipient receives OutgoingMessageRequestAcceptedEvent when actor accept message request', async () => {
    await expectAcceptMessageRequestEvents(paramConnect);
    await expectResumeWS(paramConnect);
  });
});
