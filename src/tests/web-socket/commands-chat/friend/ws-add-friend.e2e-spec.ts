import { getPrefixMockUser } from '../../../../../jest-e2e';
import { expectAddFriendEvents } from '../../../../expected-results/commands-chat/friend';
import friendClient from '../../../../helpers/friend-service';
import { getFriendDataForExpectWS } from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import {
  cloudEvent,
  connectWebsocket,
  getActorSource,
  UserWS,
} from '../../../../helpers/websocket-service';
import { cloudEventType, HEADERS, Methods } from '../../../const';

describe('WebSocket ' + Methods.ADD_FRIEND, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.ADD_FRIEND;

  let actor;
  let recipient;

  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  let actorHeaders: HEADERS;

  let actorSource;

  let webSocketList;
  let paramConnectWS: UserWS[];

  const expectedActorEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.OUTGOING_FRIEND_REQUEST_CREATED_EVENT,
  ];
  const expectedRecipientEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.INCOMING_FRIEND_REQUEST_CREATED_EVENT,
  ];

  const expectedActorEventsTwice = [
    ...expectedActorEvents,
    cloudEventType.OUTGOING_FRIEND_REQUEST_CANCELED_EVENT,
    cloudEventType.OUTGOING_FRIEND_REQUEST_CREATED_EVENT,
  ];
  const expectedRecipientEventsTwice = [
    ...expectedRecipientEvents,
    cloudEventType.INCOMING_FRIEND_REQUEST_CANCELED_EVENT,
    cloudEventType.INCOMING_FRIEND_REQUEST_CREATED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);

    paramConnectWS = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];
    webSocketList = await connectWebsocket(paramConnectWS);
  });

  afterEach(async () => {
    for (const ws of webSocketList) {
      await ws.close();
    }

    actorEvents = [];
    recipientEvents = [];
  });

  it(`should receive OutgoingFriendRequestCreatedEvent, IncomingFriendRequestCreatedEvent when actor add friend recipient`, async () => {
    await getResponseSuccess(
      { userId: recipient.userId },
      friendClient.addFriend,
      actorHeaders,
    );

    await wait();

    const actorGetFriend = await getFriendDataForExpectWS(recipient, actor);
    const recipientGetFriend = await getFriendDataForExpectWS(actor, recipient);

    recipientGetFriend.friendRequest.participantIds?.reverse();

    actorSource = getActorSource(actorEvents);

    await expectAddFriendEvents(
      { user: actor, events: actorEvents },
      actorSource,
      [actorGetFriend],
      expectedActorEvents,
    );

    await expectAddFriendEvents(
      { user: recipient, events: recipientEvents },
      actorSource,
      [recipientGetFriend],
      expectedRecipientEvents,
    );
  });

  it(`should receive OutgoingFriendRequestCreatedEvent, IncomingFriendRequestCreatedEvent when actor add friend recipient after cancel request`, async () => {
    // First time Add Friend
    await getResponseSuccess(
      { userId: recipient.userId },
      friendClient.addFriend,
      actorHeaders,
    );

    await wait(3000);

    const actorGetFriend = await getFriendDataForExpectWS(recipient, actor);
    const recipientGetFriend = await getFriendDataForExpectWS(actor, recipient);

    recipientGetFriend.friendRequest.participantIds?.reverse();

    // Cancel FriendRequest
    await getResponseSuccess(
      { userId: recipient.userId },
      friendClient.cancelFriendRequest,
      actorHeaders,
    );

    // Second time Add Friend
    await getResponseSuccess(
      { userId: recipient.userId },
      friendClient.addFriend,
      actorHeaders,
    );

    await wait();

    const actorGetFriendTwice = await getFriendDataForExpectWS(
      recipient,
      actor,
    );

    const recipientGetFriendTwice = await getFriendDataForExpectWS(
      actor,
      recipient,
    );
    recipientGetFriendTwice.friendRequest.participantIds?.reverse();

    actorSource = getActorSource(actorEvents);

    await expectAddFriendEvents(
      { user: actor, events: actorEvents },
      actorSource,
      [actorGetFriend, actorGetFriendTwice],
      expectedActorEventsTwice,
    );

    await expectAddFriendEvents(
      { user: recipient, events: recipientEvents },
      actorSource,
      [recipientGetFriend, recipientGetFriendTwice],
      expectedRecipientEventsTwice,
    );
  });
});
