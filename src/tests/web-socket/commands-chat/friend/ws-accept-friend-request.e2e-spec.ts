import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectAcceptFriendRequestEvents } from '../../../../expected-results/commands-chat/friend';
import friendClient from '../../../../helpers/friend-service';
import { getFriendDataForExpectWS } from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import {
  cloudEvent,
  connectWebsocket,
  getActorSource,
  UserWS,
} from '../../../../helpers/websocket-service';
import { cloudEventType, HEADERS, Methods } from '../../../const';

describe('WebSocket ' + Methods.ACCEPT_FRIEND_REQUEST, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.ACCEPT_FRIEND_REQUEST;

  let actor: V3MockedUser;
  let recipient: V3MockedUser;

  let actorHeaders: HEADERS;
  let recipientHeaders: HEADERS;

  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];
  let actorSource;

  let websocketList;
  let paramConnect: UserWS[] = [];

  const expectedActorEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.INCOMING_FRIEND_REQUEST_ACCEPTED_EVENT,
  ];
  const expectedRecipientEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.OUTGOING_FRIEND_REQUEST_ACCEPTED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);
    actorHeaders = createHeaders(actor.token as string);
    recipientHeaders = createHeaders(recipient.token as string);

    await getResponseSuccess(
      { userId: actor.userId },
      friendClient.addFriend,
      recipientHeaders,
    );
    await wait();
    paramConnect = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];
    websocketList = await connectWebsocket(paramConnect);
  });

  afterEach(async () => {
    for (const ws of websocketList) {
      await ws.close();
    }
    actorEvents = [];
    recipientEvents = [];
  });

  it(`should actor receive IncomingFriendRequestAcceptedEvent and recipient receive OutgoingFriendRequestAcceptedEvent when actor accept friend request`, async () => {
    await getResponseSuccess(
      { userId: recipient.userId },
      friendClient.acceptFriendRequest,
      actorHeaders,
    );

    await wait(3000);
    actorSource = getActorSource(actorEvents);

    const actorGetFriend = await getFriendDataForExpectWS(recipient, actor);
    const recipientGetFriend = await getFriendDataForExpectWS(actor, recipient);

    actorGetFriend.friendRequest.participantIds?.reverse();

    await expectAcceptFriendRequestEvents(
      { user: actor, events: actorEvents },
      actorSource,
      actorGetFriend,
      expectedActorEvents,
    );
    await expectAcceptFriendRequestEvents(
      { user: recipient, events: recipientEvents },
      actorSource,
      recipientGetFriend,
      expectedRecipientEvents,
    );
  });
});
