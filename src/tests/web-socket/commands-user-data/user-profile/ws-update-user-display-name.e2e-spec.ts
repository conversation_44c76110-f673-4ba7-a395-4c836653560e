import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  expectGatewayConnectedEvent,
  expectResumeWS,
  expectSourceFromUser,
  expectUserDisplayNameUpdatedEventData,
} from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../helpers/websocket-service';
import { cloudEventType, HEADERS, Methods, WS_VERSION } from '../../../const';

describe('WebSocket ' + Methods.UPDATE_USER_DISPLAY_NAME, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UPDATE_USER_DISPLAY_NAME;
  let actor;
  let actorHeaders: HEADERS;

  let actorWs;
  let actorSource = '';
  let actorEvents: cloudEvent[] = [];

  const displayName = 'Test';

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.PROFILE_DISPLAY_NAME_UPDATED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);

    [actorWs] = await connectWebsocket([{ user: actor, events: actorEvents }]);
  });

  afterEach(async () => {
    await actorWs.close();
    actorEvents = [];
  });

  const expectUpdateDisplayNameEventData = async (
    displayNames: string[],
    expectedEvents = expectedUserEvents,
  ) => {
    await wait();

    expect(actorEvents).toHaveLength(expectedEvents.length);
    for (const [index, event] of actorEvents.entries()) {
      expect(event.type).toEqual(expectedEvents[index]);
      expect(event.version).toEqual(WS_VERSION);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          actorSource = event.source;
          await expectGatewayConnectedEvent(actor, event.data);
          break;
        case cloudEventType.PROFILE_DISPLAY_NAME_UPDATED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectUserDisplayNameUpdatedEventData(
            actor,
            displayNames[index - 1],
            event.data,
          );
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }

    // Resume
    await expectResumeWS([{ user: actor, events: actorEvents }]);
  };

  describe('Business logic', () => {
    it('should create display name updated event when update display name', async () => {
      await getResponseSuccess(
        { displayName: displayName },
        userProfileClient.updateUserDisplayName,
        actorHeaders,
      );
      await expectUpdateDisplayNameEventData([displayName]);
    });

    it('should create display name updated event when update display name twice', async () => {
      const expectedUserEvents = [
        cloudEventType.GATEWAY_CONNECTED_EVENT,
        cloudEventType.PROFILE_DISPLAY_NAME_UPDATED_EVENT,
        cloudEventType.PROFILE_DISPLAY_NAME_UPDATED_EVENT,
      ];

      await getResponseSuccess(
        { displayName: displayName },
        userProfileClient.updateUserDisplayName,
        actorHeaders,
      );

      const displayName2 = 'Test1';
      await getResponseSuccess(
        { displayName: displayName2 },
        userProfileClient.updateUserDisplayName,
        actorHeaders,
      );

      await expectUpdateDisplayNameEventData(
        [displayName, displayName2],
        expectedUserEvents,
      );
    });
  });
});
