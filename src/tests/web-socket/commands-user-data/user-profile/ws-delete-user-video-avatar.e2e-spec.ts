import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectDeleteUserAvatarEvents } from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../helpers/websocket-service';
import { CONFIG_FILE_STORE_HOST, HEADERS, Methods } from '../../../const';

describe('WS ' + Methods.DELETE_USER_VIDEO_AVATAR, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.DELETE_USER_VIDEO_AVATAR;

  let actor: V3MockedUser;
  let actorHeaders: HEADERS;

  let actorEvents: cloudEvent[] = [];

  let webSocketList;
  let paramConnect: UserWS[];

  beforeAll(async () => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);
  });

  beforeEach(async () => {
    await getResponseSuccess(
      {
        avatarPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/videoAvatar.jpeg`,
      },
      userProfileClient.updateUserVideoAvatar,
      actorHeaders,
    );

    paramConnect = [{ events: actorEvents, user: actor }];

    webSocketList = await connectWebsocket(paramConnect);
  });

  afterEach(async () => {
    await disconnectWS(webSocketList);

    actorEvents = [];
  });

  it('should return UserAvatarDeletedEvent when user delete video avatar', async () => {
    await getResponseSuccess(
      {},
      userProfileClient.deleteUserVideoAvatar,
      actorHeaders,
    );

    await expectDeleteUserAvatarEvents(paramConnect[0]);
  });
});
