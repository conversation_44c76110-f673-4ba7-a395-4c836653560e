import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserAvatarTypeEnum } from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectUpdateUserAvatarEvents } from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../helpers/websocket-service';
import {
  cloudEventType,
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  Methods,
} from '../../../const';

describe('WS ' + Methods.UPDATE_USER_VIDEO_AVATAR, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UPDATE_USER_VIDEO_AVATAR;

  let actor: V3MockedUser;
  let actorHeaders: HEADERS;

  let actorEvents: cloudEvent[] = [];

  let webSocketList: WebSocket[];
  let paramConnect: UserWS[];

  let expectedAvatar;

  beforeEach(async () => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);

    paramConnect = [{ events: actorEvents, user: actor }];
    webSocketList = await connectWebsocket(paramConnect);
  });

  afterEach(async () => {
    await disconnectWS(webSocketList);
    actorEvents = [];
  });

  const updateVideoAvatar = async () => {
    const response = await getResponseSuccess(
      {
        avatarPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/videoAvatar.jpeg`,
      },
      userProfileClient.updateUserVideoAvatar,
      actorHeaders,
    );
    return response.data?.avatar;
  };

  it('should return UserAvatarUpdatedEvent when user update video avatar', async () => {
    const response = await getResponseSuccess(
      {
        avatarPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/videoAvatar.jpeg`,
      },
      userProfileClient.updateUserVideoAvatar,
      actorHeaders,
    );
    expectedAvatar = response.data?.avatar;
    await expectUpdateUserAvatarEvents(
      paramConnect[0],
      [expectedAvatar],
      V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_VIDEO,
    );
  });

  it('should return UserAvatarUpdatedEvent when user update video avatar twice', async () => {
    const expectedUserEvents = [
      cloudEventType.GATEWAY_CONNECTED_EVENT,
      cloudEventType.USER_AVATAR_UPDATED_EVENT,
      cloudEventType.USER_AVATAR_UPDATED_EVENT,
    ];

    const expectedAvatarPath1 = await updateVideoAvatar();
    const expectedAvatarPath2 = await updateVideoAvatar();

    await expectUpdateUserAvatarEvents(
      paramConnect[0],
      [expectedAvatarPath1 as string, expectedAvatarPath2 as string],
      V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_VIDEO,
      expectedUserEvents,
    );
  });
});
