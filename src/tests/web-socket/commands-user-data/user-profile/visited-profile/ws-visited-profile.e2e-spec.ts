import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3UserView,
  V3UserVisitedProfileEventData,
} from '../../../../../../utils/http-client/cloudevent-client';
import {
  expectGatewayConnectedEvent,
  expectResumeWS,
  expectSourceFromUser,
  expectVisitedProfileEventData,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../../helpers/user-view-service';
import {
  cloudEvent,
  connectWebsocket,
  getActorSource,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  HEADERS,
  Methods,
  WS_VERSION,
} from '../../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../../error-message';

describe('WebSocket ' + Methods.VISITED_PROFILE, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.VISITED_PROFILE;
  let actor;
  let recipient;

  let actorHeaders: HEADERS;
  let recipientHeaders: HEADERS;

  let actorWs;
  let recipientWs;

  let source = '';
  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.VISITED_PROFILE_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);
    recipientHeaders = createHeaders(recipient.token as string);

    // Connect WS
    [actorWs, recipientWs] = await connectWebsocket([
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ]);
  });

  afterEach(async () => {
    await actorWs.close();
    await recipientWs.close();
    actorEvents = [];
    recipientEvents = [];
  });

  it('should create VISITED_PROFILE_EVENT when visited profile user', async () => {
    await getResponseSuccess(
      {
        userId: recipient.userId,
      },
      userProfileClient.visitedProfile,
      actorHeaders,
    );
    await wait();

    const userData = await getUserProfile(actor);

    const listVisited = await getResponseSuccess(
      {},
      userViewClient.listUserVisitedProfile,
      recipientHeaders,
    );
    if (!listVisited.data) throw new Error(NULL_UNDEFINED_ERROR);

    const expectData: V3UserVisitedProfileEventData = {
      userId: recipient.userId,
      userData: userData as V3UserView,
      createTime: listVisited.data[0].visitedTime,
      updateTime: listVisited.data[0].visitedTime,
    };

    //Expect actor events
    expect(actorEvents.length).toEqual(1);
    await expectGatewayConnectedEvent(actor, actorEvents[0].data);
    source = getActorSource(actorEvents);

    //Expect recipient events
    expect(recipientEvents).toHaveLength(expectedUserEvents.length);
    for (const [index, event] of recipientEvents.entries()) {
      expect(event.type).toEqual(expectedUserEvents[index]);
      expect(event.version).toEqual(WS_VERSION);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(recipient, event.data);
          break;
        case cloudEventType.VISITED_PROFILE_EVENT:
          expectSourceFromUser(source, event.source);
          expectVisitedProfileEventData(expectData, event.data);
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }

    //Expect resume ws
    await expectResumeWS([{ user: recipient, events: recipientEvents }]);
  });
});
