import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3DeleteUserVisitedProfileEventData } from '../../../../../../utils/http-client/cloudevent-client';
import {
  expectDeleteUserVisitedProfileEventData,
  expectGatewayConnectedEvent,
  expectResumeWS,
  expectSourceFromUser,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  HEADERS,
  Methods,
  WS_VERSION,
} from '../../../../const';

describe('WebSocket ' + Methods.DELETE_USER_VISITED_PROFILE, () => {
  const prefix =
    getPrefixMockUser() + 'WS_' + Methods.DELETE_USER_VISITED_PROFILE;
  let actor;
  let recipient;

  let actorHeaders: HEADERS;
  let recipientHeaders: HEADERS;

  let actorWs;
  let recipientWs;

  let actorSource = '';
  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.VISITED_PROFILE_DELETED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);
    recipientHeaders = createHeaders(recipient.token as string);

    await getResponseSuccess(
      {
        userId: actor.userId,
      },
      userProfileClient.visitedProfile,
      recipientHeaders,
    );

    // Connect WS
    [actorWs, recipientWs] = await connectWebsocket([
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ]);
  });

  afterEach(async () => {
    await actorWs.close();
    await recipientWs.close();
    actorEvents = [];
    recipientEvents = [];
  });

  it('should create VISITED_PROFILE_DELETED_EVENT event when delete user visited profile', async () => {
    await getResponseSuccess(
      {
        userId: recipient.userId,
      },
      userProfileClient.deleteUserVisitedProfile,
      actorHeaders,
    );
    await wait();
    const expectData: V3DeleteUserVisitedProfileEventData = {
      userId: recipient.userId,
      actorId: actor.userId,
    };

    //Expect actor events
    expect(actorEvents).toHaveLength(expectedUserEvents.length);
    for (const [index, event] of actorEvents.entries()) {
      expect(event.type).toEqual(expectedUserEvents[index]);
      expect(event.version).toEqual(WS_VERSION);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          actorSource = event.source;
          await expectGatewayConnectedEvent(actor, event.data);
          break;
        case cloudEventType.VISITED_PROFILE_DELETED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectDeleteUserVisitedProfileEventData(expectData, event.data);
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }

    //Expect recipient events
    expect(recipientEvents.length).toEqual(1);
    await expectGatewayConnectedEvent(recipient, recipientEvents[0].data);

    //Expect resume ws
    await expectResumeWS([{ user: actor, events: actorEvents }]);
  });
});
