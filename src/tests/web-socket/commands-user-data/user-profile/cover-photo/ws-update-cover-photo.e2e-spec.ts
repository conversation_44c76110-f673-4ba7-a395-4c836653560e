import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  expectCoverPhotoEventData,
  expectGatewayConnectedEvent,
  expectResumeWS,
  expectSourceFromUser,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  Methods,
  WS_VERSION,
} from '../../../../const';

describe('WebSocket ' + Methods.UPDATE_COVER_PHOTO, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UPDATE_COVER_PHOTO;
  let actor;
  let actorHeaders: HEADERS;
  let actorWs;

  let actorSource = '';
  let actorEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.COVER_UPDATED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 2);
    actorHeaders = createHeaders(actor.token as string);

    await getResponseSuccess(
      {
        coverPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/cover.png`,
      },
      userProfileClient.addCoverPhoto,
      actorHeaders,
    );

    [actorWs] = await connectWebsocket([{ user: actor, events: actorEvents }]);
  });

  afterEach(async () => {
    await actorWs.close();

    actorEvents = [];
  });

  describe('Business logic', () => {
    it('should create COVER_UPDATED_EVENT when update cover photo', async () => {
      const response = await getResponseSuccess(
        {
          coverPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/updateCover.png`,
        },
        userProfileClient.updateCoverPhoto,
        actorHeaders,
      );
      await wait();

      for (const [index, event] of actorEvents.entries()) {
        expect(event.type).toEqual(expectedUserEvents[index]);
        expect(event.version).toEqual(WS_VERSION);
        switch (event.type) {
          case cloudEventType.GATEWAY_CONNECTED_EVENT:
            actorSource = event.source;
            await expectGatewayConnectedEvent(actor, event.data);
            break;
          case cloudEventType.COVER_UPDATED_EVENT:
            expectSourceFromUser(actorSource, event.source);
            expectCoverPhotoEventData(event.data, {
              userId: actor.userId,
              cover: response.data?.cover,
            });
            break;
          default:
            throw new Error('Event have not been handled yet: ' + event.type);
        }
      }

      //Expect resume ws
      await expectResumeWS([{ user: actor, events: actorEvents }]);
    });
  });
});
