import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserAvatarTypeEnum } from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectUpdateUserAvatarEvents } from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../helpers/websocket-service';
import {
  cloudEventType,
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  Methods,
} from '../../../const';

describe('WebSocket ' + Methods.UPDATE_USER_AVATAR, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UPDATE_USER_AVATAR;
  let actor: V3MockedUser;
  let actorHeaders: HEADERS;
  let ws;
  let userEvents: cloudEvent[] = [];

  let expectedAvatarPath;

  let paramsConnect;

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);

    paramsConnect = [{ user: actor, events: userEvents }];
    [ws] = await connectWebsocket(paramsConnect);
  });

  afterEach(async () => {
    await ws.close();
    userEvents = [];
  });

  const updateAvatar = async () => {
    const response = await getResponseSuccess(
      {
        avatarPath: `${CONFIG_FILE_STORE_HOST}` + ulid() + `/avatar.jpeg`,
      },
      userProfileClient.updateUserAvatar,
      actorHeaders,
    );
    return response.data?.avatar;
  };

  it(`should receive UserAvatarUpdatedEvent when update user avatar`, async () => {
    expectedAvatarPath = await updateAvatar();

    await expectUpdateUserAvatarEvents(paramsConnect[0], [expectedAvatarPath]);
  });

  it(`should receive enough event when update user avatar twice`, async () => {
    const expectedUserEvents = [
      cloudEventType.GATEWAY_CONNECTED_EVENT,
      cloudEventType.USER_AVATAR_UPDATED_EVENT,
      cloudEventType.USER_AVATAR_UPDATED_EVENT,
    ];

    const expectedAvatarPath1 = await updateAvatar();
    const expectedAvatarPath2 = await updateAvatar();

    await expectUpdateUserAvatarEvents(
      paramsConnect[0],
      [expectedAvatarPath1 as string, expectedAvatarPath2 as string],
      V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
      expectedUserEvents,
    );
  });
});
