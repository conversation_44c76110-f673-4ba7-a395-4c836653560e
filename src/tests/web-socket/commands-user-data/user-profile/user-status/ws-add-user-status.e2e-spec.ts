import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3UserStatusCreatedEventData,
  V3UserStatusExpireAfterTimeEnum,
} from '../../../../../../utils/http-client/cloudevent-client';
import {
  expectGatewayConnectedEvent,
  expectSourceFromUser,
  expectUserStatusEventData,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  HEADERS,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../../const';

describe('WebSocket ' + Methods.ADD_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.ADD_USER_STATUS;
  let actor;
  let actorHeaders: HEADERS;

  let actorWs;
  let actorSource = '';
  let actorEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.PROFILE_USER_STATUS_CREATED,
  ];

  const testCase = [
    {
      title: 'all params',
      request: {
        content: faker.string.alpha(10),
        status: STATUS_EMOJI_LIST[0],
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
      },
    },
    {
      title: 'only content',
      request: {
        content: faker.string.alpha(10),
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER4HOUR,
      },
    },
    {
      title: 'only status',
      request: {
        status: faker.internet.emoji(),
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER1HOUR,
      },
    },
  ];

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 1);

    actorHeaders = createHeaders(actor.token as string);

    [actorWs] = await connectWebsocket([{ user: actor, events: actorEvents }]);
  });

  afterEach(async () => {
    await actorWs.close();
    actorEvents = [];
  });

  for (const cases of testCase) {
    it(
      'should create PROFILE_USER_STATUS_CREATED event when add user status: ' +
        cases.title,
      async () => {
        const response = await getResponseSuccess(
          cases.request,
          userProfileClient.addUserStatus,
          actorHeaders,
        );
        const expectData: V3UserStatusCreatedEventData = {
          userId: actor.userId,
          statusData: response.data,
        };

        for (const [index, event] of actorEvents.entries()) {
          expect(event.type).toEqual(expectedUserEvents[index]);
          switch (event.type) {
            case cloudEventType.GATEWAY_CONNECTED_EVENT:
              actorSource = event.source;
              await expectGatewayConnectedEvent(actor, event.data);
              break;
            case cloudEventType.PROFILE_USER_STATUS_CREATED:
              expectSourceFromUser(actorSource, event.source);
              expectUserStatusEventData(event.data, expectData);
              break;
            default:
              throw new Error('Event have not been handled yet: ' + event.type);
          }
        }
      },
    );
  }
});
