import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3UserStatusExpireAfterTimeEnum,
  V3UserStatusUpdatedEventData,
} from '../../../../../../utils/http-client/cloudevent-client';
import {
  expectGatewayConnectedEvent,
  expectSourceFromUser,
  expectUserStatusEventData,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  HEADERS,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../../const';

describe('WebSocket ' + Methods.UPDATE_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UPDATE_USER_STATUS;
  let actor;
  let actorHeaders: HEADERS;

  let actorWs;
  let actorSource = '';
  let actorEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.PROFILE_USER_STATUS_UPDATED,
  ];

  const testCase = [
    {
      title: 'all params',
      request: {
        content: faker.string.alpha(10),
        status: STATUS_EMOJI_LIST[0],
      },
    },
    {
      title: 'only content',
      request: {
        content: faker.string.alpha(10),
      },
    },
    {
      title: 'only status',
      request: {
        status: faker.internet.emoji(),
      },
    },
  ];

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);
  });

  afterEach(async () => {
    await actorWs.close();
    actorEvents = [];
  });

  describe('Update status', () => {
    beforeEach(async (): Promise<void> => {
      await getResponseSuccess(
        {
          content: faker.string.alpha(10),
          status: STATUS_EMOJI_LIST[0],
          expireAfterTime:
            V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
        },
        userProfileClient.addUserStatus,
        actorHeaders,
      );
      await wait();

      [actorWs] = await connectWebsocket([
        { user: actor, events: actorEvents },
      ]);
    });
    for (const cases of testCase) {
      it(
        'should create PROFILE_USER_STATUS_UPDATED event when update user status: ' +
          cases.title,
        async () => {
          const response = await getResponseSuccess(
            cases.request,
            userProfileClient.updateUserStatus,
            actorHeaders,
          );

          const expectData: V3UserStatusUpdatedEventData = {
            userId: actor.userId,
            statusData: response.data,
          };

          for (const [index, event] of actorEvents.entries()) {
            expect(event.type).toEqual(expectedUserEvents[index]);
            switch (event.type) {
              case cloudEventType.GATEWAY_CONNECTED_EVENT:
                actorSource = event.source;
                await expectGatewayConnectedEvent(actor, event.data);
                break;
              case cloudEventType.PROFILE_USER_STATUS_UPDATED:
                expectSourceFromUser(actorSource, event.source);
                expectUserStatusEventData(event.data, expectData);
                break;
              default:
                throw new Error(
                  'Event have not been handled yet: ' + event.type,
                );
            }
          }
        },
      );
    }
  });

  for (const cases of testCase) {
    describe('Delete status', () => {
      beforeEach(async (): Promise<void> => {
        await getResponseSuccess(
          {
            ...cases.request,
            expireAfterTime:
              V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
          },
          userProfileClient.addUserStatus,
          actorHeaders,
        );
        await wait();

        [actorWs] = await connectWebsocket([
          { user: actor, events: actorEvents },
        ]);
      });

      it('should return when update ' + cases.title, async () => {
        const expectedUserEvents = [
          cloudEventType.GATEWAY_CONNECTED_EVENT,
          cloudEventType.PROFILE_USER_STATUS_DELETED,
        ];

        const request = {};
        for (const key in cases.request) {
          request[key] = '';
        }

        await getResponseSuccess(
          request,
          userProfileClient.updateUserStatus,
          actorHeaders,
        );

        for (const [index, event] of actorEvents.entries()) {
          expect(event.type).toEqual(expectedUserEvents[index]);
          switch (event.type) {
            case cloudEventType.GATEWAY_CONNECTED_EVENT:
              actorSource = event.source;
              await expectGatewayConnectedEvent(actor, event.data);
              break;
            case cloudEventType.PROFILE_USER_STATUS_DELETED:
              expectSourceFromUser(actorSource, event.source);
              expectUserStatusEventData(event.data, { userId: actor.userId });
              break;
            default:
              throw new Error('Event have not been handled yet: ' + event.type);
          }
        }
      });
    });
  }
});
