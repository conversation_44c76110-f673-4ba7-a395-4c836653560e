import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../../utils/http-client/cloudevent-client';
import {
  expectGatewayConnectedEvent,
  expectSourceFromUser,
  expectUserStatusEventData,
} from '../../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
} from '../../../../../helpers/websocket-service';
import {
  cloudEventType,
  HEADERS,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../../const';

describe('WebSocket ' + Methods.DELETE_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.DELETE_USER_STATUS;
  let actor;
  let actorHeaders: HEADERS;

  let actorWs;
  let actorSource = '';
  let actorEvents: cloudEvent[] = [];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.PROFILE_USER_STATUS_DELETED,
  ];

  beforeEach(async (): Promise<void> => {
    [actor] = await mockUsersData(prefix, 1);
    actorHeaders = createHeaders(actor.token as string);

    await getResponseSuccess(
      {
        content: faker.string.alpha(10),
        status: STATUS_EMOJI_LIST[0],
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
      },
      userProfileClient.addUserStatus,
      actorHeaders,
    );

    [actorWs] = await connectWebsocket([{ user: actor, events: actorEvents }]);
  });

  afterEach(async () => {
    await actorWs.close();
    actorEvents = [];
  });

  describe('Business logic', () => {
    it('should create PROFILE_USER_STATUS_DELETED event when deleted user status', async () => {
      await getResponseSuccess(
        {},
        userProfileClient.deleteUserStatus,
        actorHeaders,
      );

      for (const [index, event] of actorEvents.entries()) {
        expect(event.type).toEqual(expectedUserEvents[index]);
        switch (event.type) {
          case cloudEventType.GATEWAY_CONNECTED_EVENT:
            actorSource = event.source;
            await expectGatewayConnectedEvent(actor, event.data);
            break;
          case cloudEventType.PROFILE_USER_STATUS_DELETED:
            expectSourceFromUser(actorSource, event.source);
            expectUserStatusEventData(event.data, {
              userId: actor.userId,
            });
            break;
          default:
            throw new Error('Event have not been handled yet: ' + event.type);
        }
      }
    });
  });
});
