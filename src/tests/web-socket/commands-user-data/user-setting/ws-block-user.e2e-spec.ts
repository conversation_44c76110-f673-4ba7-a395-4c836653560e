import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserBlockedEventData } from '../../../../../utils/http-client/cloudevent-client';
import { expectEventsForUserBlockAndUnblockEventData } from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import { userSettingClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../helpers/websocket-service';
import { cloudEventType, HEADERS, Methods } from '../../../const';

describe('WebSocket ' + Methods.BLOCK_USER, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.BLOCK_USER;

  let actor;
  let recipient;

  let actorHeaders: HEADERS;

  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  let ws;
  let paramConnect: UserWS[];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.USER_BLOCKED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);

    paramConnect = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];
    ws = await connectWebsocket(paramConnect);
  });

  afterEach(async () => {
    await disconnectWS(ws);

    actorEvents = [];
    recipientEvents = [];
  });

  it('should receive UserBlockedEvent when actor block recipient', async () => {
    await getResponseSuccess(
      { targetUserId: recipient.userId },
      userSettingClient.blockUser,
      actorHeaders,
    );
    await wait();
    const expectedData: V3UserBlockedEventData = {
      actorId: actor.userId as string,
      targetUserId: recipient.userId as string,
    };
    await expectEventsForUserBlockAndUnblockEventData(
      paramConnect,
      expectedData,
      expectedUserEvents,
    );
  });
});
