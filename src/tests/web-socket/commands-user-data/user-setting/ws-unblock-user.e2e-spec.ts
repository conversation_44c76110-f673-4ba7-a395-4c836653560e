import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserUnblockedEventData } from '../../../../../utils/http-client/cloudevent-client';
import { expectEventsForUserBlockAndUnblockEventData } from '../../../../expected-results/realtime/websocket';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import { userSettingClient } from '../../../../helpers/user-service';
import {
  cloudEvent,
  connectWebsocket,
  disconnectWS,
  UserWS,
} from '../../../../helpers/websocket-service';
import { cloudEventType, HEADERS, Methods } from '../../../const';

describe('WebSocket ' + Methods.UNBLOCK_USER, () => {
  const prefix = getPrefixMockUser() + 'WS_' + Methods.UNBLOCK_USER;

  let actor;
  let recipient;

  let actorHeaders: HEADERS;

  let actorEvents: cloudEvent[] = [];
  let recipientEvents: cloudEvent[] = [];

  let ws;
  let paramConnect: UserWS[];

  const expectedUserEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.USER_UNBLOCKED_EVENT,
  ];

  beforeEach(async (): Promise<void> => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);

    await getResponseSuccess(
      { targetUserId: recipient.userId },
      userSettingClient.blockUser,
      actorHeaders,
    );
    await wait();

    paramConnect = [
      { user: actor, events: actorEvents },
      { user: recipient, events: recipientEvents },
    ];
    ws = await connectWebsocket(paramConnect);
  });

  afterEach(async () => {
    await disconnectWS(ws);

    actorEvents = [];
    recipientEvents = [];
  });

  it('should receive UserUnblockedEvent when actor unblock recipient', async () => {
    await getResponseSuccess(
      { targetUserId: recipient.userId },
      userSettingClient.unblockUser,
      actorHeaders,
    );
    await wait();
    const expectedData: V3UserUnblockedEventData = {
      actorId: actor.userId as string,
      targetUserId: recipient.userId as string,
    };
    await expectEventsForUserBlockAndUnblockEventData(
      paramConnect,
      expectedData,
      expectedUserEvents,
    );
  });
});
