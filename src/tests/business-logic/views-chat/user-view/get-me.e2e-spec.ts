import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserBadgeTypeEnum } from '../../../../../utils/http-client/faker-client';
import { expectGetMeData } from '../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { getEnumValues } from '../../../../helpers/shared/common';
import { Methods } from '../../../const';

describe(Methods.GET_ME, () => {
  const prefix = getPrefixMockUser() + Methods.GET_ME;

  const userBadgeValues = getEnumValues(V3UserBadgeTypeEnum);

  for (const value of userBadgeValues) {
    it(`should return data when userBadge is ${V3UserBadgeTypeEnum[value]}`, async () => {
      const [user] = await mockUsersData(prefix, 1, value);

      await expectGetMeData(user);
    });
  }
});
