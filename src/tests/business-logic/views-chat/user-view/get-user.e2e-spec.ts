import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import {
  expectGetUserBot,
  expectGetUserData,
} from '../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getEnumValues,
} from '../../../../helpers/shared/common';
import { userViewClient } from '../../../../helpers/user-view-service';
import { HEADERS, Methods, ZIICHAT_BOT_USERID } from '../../../const';
import { GET_USER_DETAILS, UserError } from '../../../error-message';

describe(Methods.GET_USER, () => {
  const prefix = getPrefixMockUser() + Methods.GET_USER;

  const userBadgeValues = getEnumValues(V3UserBadgeTypeEnum);

  let user: V3MockedUser;
  let headers: HEADERS;

  beforeAll(async () => {
    [user] = await mockUsersData(prefix, 1);

    headers = createHeaders(user.token as string);
  });

  for (const value of userBadgeValues) {
    it(`should return data when userBadge is ${V3UserBadgeTypeEnum[value]}`, async () => {
      const [user] = await mockUsersData(prefix, 1, value);

      await expectGetUserData(user, { userId: user.userId as string });
    });
  }

  it('should return data when get user bot', async () => {
    await expectGetUserBot({ userId: ZIICHAT_BOT_USERID });
  });

  it('should return error when get by my userId', async () => {
    await expectForParamInvalid(
      { userId: user.userId },
      userViewClient.getUser,
      headers,
      { ...UserError.GET_USER, details: GET_USER_DETAILS[0] },
    );
  });
});
