import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3User } from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { checkPaginationUserView } from '../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
  removeFields,
} from '../../../../helpers/shared/common';
import { userSettingClient } from '../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../helpers/user-view-service';
import { HEADERS, MAX_LIMIT, Methods } from '../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../error-message';

describe(Methods.LIST_BLOCKED_USERS, () => {
  const prefix = getPrefixMockUser() + Methods.LIST_BLOCKED_USERS;

  let mainUser: V3MockedUser;
  let mainUserHeaders: HEADERS;

  let otherUsers: V3MockedUser[];
  let originalUserList: V3User[];

  const userQuantity = 15;

  beforeAll(async () => {
    const [firstMockedUser, ...otherMockedUsers] = await mockUsersData(
      prefix,
      userQuantity,
    );

    mainUser = firstMockedUser;
    otherUsers = otherMockedUsers;

    mainUserHeaders = createHeaders(mainUser.token as string);

    for (const { userId } of otherUsers) {
      await getResponseSuccess(
        { targetUserId: userId },
        userSettingClient.blockUser,
        mainUserHeaders,
      );
    }

    const result = await getResponseSuccess(
      {},
      userViewClient.listBlockedUsers,
      mainUserHeaders,
    );
    originalUserList = result.data as V3User[];
  });

  it('should return correct data with limit == data', async () => {
    const response = await getResponseSuccess(
      { limit: otherUsers.length },
      userViewClient.listBlockedUsers,
      mainUserHeaders,
    );
    expect(response.data).toHaveLength(otherUsers.length);
  });

  it('should return correct data with limit > data', async () => {
    const response = await getResponseSuccess(
      { limit: otherUsers.length + 1 },
      userViewClient.listBlockedUsers,
      mainUserHeaders,
    );
    expect(response.data).toHaveLength(otherUsers.length);
  });

  it('should return correct data with last list length === limit', async () => {
    await checkPaginationUserView(
      { quantity: userQuantity - 1, limit: 2 },
      originalUserList,
      mainUserHeaders,
      userViewClient.listBlockedUsers,
    );
  });

  it('should return correct data with last list length < limit', async () => {
    await checkPaginationUserView(
      { quantity: userQuantity - 1, limit: 3 },
      originalUserList,
      mainUserHeaders,
      userViewClient.listBlockedUsers,
    );
  });

  it('should return correct data when limit = 500', async () => {
    await Promise.all(
      Array.from({ length: 50 }, async () => {
        const users = await mockUsersData(prefix, 10);

        await Promise.all(
          users.map((user) => {
            return getResponseSuccess(
              { targetUserId: user.userId },
              userSettingClient.blockUser,
              mainUserHeaders,
            );
          }),
        );
      }),
    );

    const response = await getResponseSuccess(
      { limit: MAX_LIMIT },
      userViewClient.listBlockedUsers,
      mainUserHeaders,
    );
    expect(response.data?.length).toEqual(MAX_LIMIT);
  });

  it('should return correct data in the top of list blocked user', async () => {
    const [newUser] = await mockUsersData(prefix, 1);

    await getResponseSuccess(
      { targetUserId: newUser.userId },
      userSettingClient.blockUser,
      mainUserHeaders,
    );

    // Get user info
    const userInfo = await getUserProfile(newUser);

    // Get list blocked user
    const response = await getResponseSuccess(
      {},
      userViewClient.listBlockedUsers,
      mainUserHeaders,
    );
    if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
    expect(userInfo).toMatchObject(
      removeFields(response.data[0], ['presenceData']),
    );
  });

  it('should return empty list when not have blocked user', async () => {
    const response = await getResponseSuccess(
      {},
      userViewClient.listBlockedUsers,
      createHeaders(otherUsers[0].token as string),
    );
    expect(response.data).toEqual([]);
  });
});
