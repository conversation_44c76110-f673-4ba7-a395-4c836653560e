import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3User,
  V3UserStatusExpireAfterTimeEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3AddUserStatusRequest } from '../../../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { V3Paging } from '../../../../../utils/http-client/suggestion-client';
import { V3ListUserStatusResponse } from '../../../../../utils/http-client/views-chat-client';
import {
  checkPaginationUserView,
  expectForListUserStatus,
} from '../../../../expected-results/views-chat/user-view';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  expectOnlyOnePageResponse,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import {
  deleteStatus,
  userProfileClient,
  userSettingClient,
} from '../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../helpers/user-view-service';
import { HEADERS, MAX_LIMIT, Methods, STATUS_EMOJI_LIST } from '../../../const';

describe(Methods.LIST_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + Methods.LIST_USER_STATUS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;
  let member: V3MockedUser;
  let notFriend: V3MockedUser;

  let userList: V3MockedUser[];
  const userListInfo: V3User[] = [];

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;
  let memberHeaders: HEADERS;
  let notFriendHeaders: HEADERS;

  let senderInfo: V3User;
  let receiverInfo: V3User;
  let memberInfo: V3User;

  const addStatusReq: V3AddUserStatusRequest = {
    content: faker.string.alpha(10),
    expireAfterTime:
      V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
    status: STATUS_EMOJI_LIST[2],
  };

  const prepareData = async (HeadersList: HEADERS[]) => {
    for (const header of HeadersList) {
      await getResponseSuccess(
        addStatusReq,
        userProfileClient.addUserStatus,
        header,
      );
    }
  };

  const makeFriendAndAddStatus = async (
    user: V3MockedUser,
    userList: V3MockedUser[],
  ) => {
    await Promise.all([
      await createAddAndAcceptFriends(user, userList),

      Promise.all(
        userList.map(async (user) => {
          await getResponseSuccess(
            addStatusReq,
            userProfileClient.addUserStatus,
            createHeaders(user.token as string),
          );

          const userInfo = await getUserProfile(user);
          userListInfo.unshift(userInfo);
        }),
      ),
    ]);
  };

  beforeAll(async () => {
    [sender, receiver, member, notFriend] = await mockUsersData(prefix, 4);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
    memberHeaders = createHeaders(member.token as string);
    notFriendHeaders = createHeaders(notFriend.token as string);

    await createAddAndAcceptFriends(sender, [receiver, member]);
    await createAddAndAcceptFriends(receiver, [member]);
  });

  describe('Success cases', () => {
    it('should return empty list when no users added status', async () => {
      const listUserStatusRes: V3ListUserStatusResponse =
        await getResponseSuccess(
          {},
          userViewClient.listUserStatus,
          senderHeaders,
        );

      expect(listUserStatusRes.data).toStrictEqual([]);
      expectOnlyOnePageResponse(listUserStatusRes.paging as V3Paging);
    });

    it('should hidden user status when sender block receiver', async () => {
      await prepareData([senderHeaders, receiverHeaders]);

      senderInfo = await getUserProfile(sender);
      receiverInfo = await getUserProfile(receiver);

      await getResponseSuccess(
        { targetUserId: receiver.userId },
        userSettingClient.blockUser,
        senderHeaders,
      );

      await expectForListUserStatus(senderHeaders, [senderInfo]);
      await expectForListUserStatus(receiverHeaders, [receiverInfo]);

      await getResponseSuccess(
        { targetUserId: receiver.userId },
        userSettingClient.unblockUser,
        senderHeaders,
      );
      await deleteStatus([senderHeaders, receiverHeaders]);
    });

    describe('User request not have status', () => {
      beforeAll(async () => {
        await prepareData([receiverHeaders, memberHeaders]);

        receiverInfo = await getUserProfile(receiver);
        memberInfo = await getUserProfile(member);
      });

      afterAll(async () => {
        await deleteStatus([receiverHeaders, memberHeaders]);
      });

      it('should return data list correct when you not have status (sender)', async () => {
        const expectedData = [memberInfo, receiverInfo];
        await expectForListUserStatus(senderHeaders, expectedData);
      });

      it('should return data list correct when you not have status (receiver)', async () => {
        const expectedData = [receiverInfo, memberInfo];
        await expectForListUserStatus(receiverHeaders, expectedData);
      });

      it('should return data list correct when you not have status (member)', async () => {
        const expectedData = [memberInfo, receiverInfo];
        await expectForListUserStatus(memberHeaders, expectedData);
      });
    });

    describe('User request have status', () => {
      beforeAll(async () => {
        await prepareData([
          senderHeaders,
          receiverHeaders,
          memberHeaders,
          notFriendHeaders,
        ]);

        senderInfo = await getUserProfile(sender);
        receiverInfo = await getUserProfile(receiver);
        memberInfo = await getUserProfile(member);
      });

      afterAll(async () => {
        await deleteStatus([
          senderHeaders,
          receiverHeaders,
          memberHeaders,
          notFriendHeaders,
        ]);
      });

      it('should return data list correct when you have status (sender)', async () => {
        const expectedData = [senderInfo, memberInfo, receiverInfo];
        await expectForListUserStatus(senderHeaders, expectedData);
      });

      it('should return data list correct when you have status (receiver)', async () => {
        const expectedData = [receiverInfo, memberInfo, senderInfo];
        await expectForListUserStatus(receiverHeaders, expectedData);
      });

      it('should return data list correct when you have status (member)', async () => {
        const expectedData = [memberInfo, receiverInfo, senderInfo];
        await expectForListUserStatus(memberHeaders, expectedData);

        await getResponseSuccess(
          { userId: receiver.userId },
          friendClient.unfriend,
          memberHeaders,
        );
        await expectForListUserStatus(memberHeaders, [memberInfo, senderInfo]);
      });
    });

    describe('Pagination', () => {
      const QUANTITY = 15;
      let originalUserList;

      beforeAll(async () => {
        await prepareData([senderHeaders]);

        [...userList] = await mockUsersData(prefix, QUANTITY - 1);

        await makeFriendAndAddStatus(sender, userList);

        const response = await getResponseSuccess(
          {},
          userViewClient.listUserStatus,
          senderHeaders,
        );
        originalUserList = response.data;
      });

      it('should return true when limit = records', async () => {
        const listStatusRes = await getResponseSuccess(
          {
            limit: QUANTITY,
          },
          userViewClient.listUserStatus,
          senderHeaders,
        );
        expect(listStatusRes.data?.length).toBe(QUANTITY);
      });

      it('should return true when limit > records', async () => {
        const listStatusRes = await getResponseSuccess(
          {
            limit: QUANTITY + 1,
          },
          userViewClient.listUserStatus,
          senderHeaders,
        );
        expect(listStatusRes.data?.length).toBe(QUANTITY);
      });

      it('should return correct data quantity when limit < records (records % limit = 0)', async () => {
        await checkPaginationUserView(
          { quantity: QUANTITY, limit: 3 },
          originalUserList,
          senderHeaders,
          userViewClient.listUserStatus,
        );
      });

      it('should return correct data quantity when limit < records (records % limit != 0)', async () => {
        await checkPaginationUserView(
          { quantity: QUANTITY, limit: 4 },
          originalUserList,
          senderHeaders,
          userViewClient.listUserStatus,
        );
      });

      it.skip('should return true when limit = 500', async () => {
        await Promise.all(
          Array.from({ length: 50 }, async () => {
            const users = await mockUsersData(prefix, 10);
            await makeFriendAndAddStatus(sender, users);
          }),
        );

        const listStatusRes = await getResponseSuccess(
          {
            limit: MAX_LIMIT,
          },
          userViewClient.listUserStatus,
          senderHeaders,
        );
        expect(listStatusRes.data?.length).toBe(MAX_LIMIT);
      }, 300000);
    });
  });
});
