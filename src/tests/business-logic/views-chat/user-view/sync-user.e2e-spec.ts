import { faker } from '@faker-js/faker';
import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import {
  V3UserDeletedTypeEnum,
  V3UserIdentification,
  V3UserView,
} from '../../../../../utils/http-client/views-chat-client';
import {
  expectSyncResponseUndefined,
  expectSyncUserResponseData,
  getTimeForSync,
} from '../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  assertIsDate,
  createHeaders,
  getPreviousTimeWithSeconds,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import {
  userProfileClient,
  userSettingClient,
} from '../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../helpers/user-view-service';
import {
  HEADERS,
  ListTimeSync,
  ListTimeSyncWithoutEqual,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../error-message';

describe(Methods.SYNC_USERS, () => {
  const prefix = getPrefixMockUser() + Methods.SYNC_USERS;

  let actor: V3MockedUser = {};
  let recipient: V3MockedUser = {};

  let actorHeaders: HEADERS;
  let recipientHeaders: HEADERS;

  let syncTime: string;
  let userInfo: V3UserView;

  const userList: { user: V3MockedUser; role: string }[] = [
    { role: 'actor', user: actor },
    { role: 'recipient', user: recipient },
  ];

  const prepareData = async () => {
    [actor, recipient] = await mockUsersData(prefix, 2);

    actorHeaders = createHeaders(actor.token as string);
    recipientHeaders = createHeaders(recipient.token as string);

    //getUser
    const response = await getResponseSuccess(
      {
        userId: recipient.userId,
      },
      userViewClient.getUser,
      actorHeaders,
    );
    userInfo = response.data as V3UserView;

    const lastUpdateTime = userInfo.presenceData?.lastUpdateTime as string;

    syncTime = getPreviousTimeWithSeconds(lastUpdateTime, 1000);
  };

  type RunSyncTestCaseOptions = {
    methodName: Methods;
    performAction?: () => Promise<void>;
    returnDataByRole?: 'actor' | 'recipient';
    usersToTest?: { role: string; headers: HEADERS }[];
    expectUserDeleted?: V3UserIdentification[];
  };

  const runSyncTestCase = ({
    methodName,
    performAction = async () => {},
    expectUserDeleted = [],
  }: RunSyncTestCaseOptions) => {
    describe(methodName, () => {
      beforeAll(async () => {
        await prepareData();
        await wait(4000);
        await performAction();
        userInfo = await getUserProfile(actor);
      });

      ListTimeSync.forEach((time) => {
        describe(time, () => {
          const haveData = time.includes('<');
          const title = haveData ? '' : ' not';

          it(`should${title} return data, case: ${methodName} -> ${Methods.SYNC_USERS}, recipient request, ${time}`, async () => {
            const response = await userViewClient.syncUsers(
              {
                updateTimeAfter: await getTimeForSync(time, actor),
                userIds: [actor.userId as string],
              },
              recipientHeaders,
            );

            if (haveData) {
              expectSyncUserResponseData(
                response.data,
                userInfo,
                expectUserDeleted,
              );
            } else {
              expectSyncResponseUndefined(response.data);
            }
          });
        });
      });
    });
  };

  runSyncTestCase({
    methodName: Methods.UPDATE_USER_AVATAR,
    performAction: async () => {
      await getResponseSuccess(
        {
          avatarPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/avatar.png',
        },
        userProfileClient.updateUserAvatar,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.UPDATE_USER_VIDEO_AVATAR,
    performAction: async () => {
      await getResponseSuccess(
        {
          avatarPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/video.mp4',
        },
        userProfileClient.updateUserVideoAvatar,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.DELETE_USER_AVATAR,
    performAction: async () => {
      await getResponseSuccess(
        {},
        userProfileClient.deleteUserAvatar,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.DELETE_USER_VIDEO_AVATAR,
    performAction: async () => {
      await getResponseSuccess(
        {
          avatarPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/video.mp4',
        },
        userProfileClient.updateUserVideoAvatar,
        actorHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteUserVideoAvatar,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.UPDATE_USER_DISPLAY_NAME,
    performAction: async () => {
      await getResponseSuccess(
        {
          displayName: 'recipient',
        },
        userProfileClient.updateUserDisplayName,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.ADD_USER_STATUS,
    performAction: async () => {
      await getResponseSuccess(
        {
          content: faker.string.alpha(10),
          status: STATUS_EMOJI_LIST[0],
          expireAfterTime:
            V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
        },
        userProfileClient.addUserStatus,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.UPDATE_USER_STATUS,
    performAction: async () => {
      await getResponseSuccess(
        {
          content: faker.string.alpha(10),
          status: STATUS_EMOJI_LIST[0],
          expireAfterTime:
            V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
        },
        userProfileClient.addUserStatus,
        actorHeaders,
      );
      await getResponseSuccess(
        {
          content: faker.string.alpha(5),
        },
        userProfileClient.updateUserStatus,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.DELETE_USER_STATUS,
    performAction: async () => {
      await getResponseSuccess(
        {
          content: faker.string.alpha(10),
          status: STATUS_EMOJI_LIST[0],
          expireAfterTime:
            V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
        },
        userProfileClient.addUserStatus,
        actorHeaders,
      );
      await getResponseSuccess(
        {
          content: faker.string.alpha(5),
        },
        userProfileClient.updateUserStatus,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.ADD_COVER_PHOTO,
    performAction: async () => {
      await getResponseSuccess(
        {
          coverPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/cover.jpg',
        },
        userProfileClient.addCoverPhoto,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.UPDATE_COVER_PHOTO,
    performAction: async () => {
      await getResponseSuccess(
        {
          coverPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/cover.jpg',
        },
        userProfileClient.addCoverPhoto,
        actorHeaders,
      );

      await getResponseSuccess(
        {
          coverPath:
            'https://fs.ugc.ziicdn.net/' + ulid() + '/coverUpdated.jpg',
        },
        userProfileClient.updateCoverPhoto,
        actorHeaders,
      );
    },
  });

  runSyncTestCase({
    methodName: Methods.DELETE_COVER_PHOTO,
    performAction: async () => {
      await getResponseSuccess(
        {
          coverPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/cover.jpg',
        },
        userProfileClient.addCoverPhoto,
        actorHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteCoverPhoto,
        actorHeaders,
      );
    },
  });
  //TODO https://github.com/halonext/ziichat-issues/issues/18430
  // runSyncTestCase({
  //   methodName: Methods.UPLOAD_DECORATED_AVATAR,
  //   performAction: async () => {
  //     const avatarFrameFromList = await getRandomAvatarFrame(actorHeaders);
  //
  //     await getResponseSuccess(
  //       {
  //         avatarFrameId: avatarFrameFromList.avatarFrameId,
  //         decoratedAvatarPath:
  //           'https://fs.ugc.ziicdn.net/' + ulid() + '/cover.jpg',
  //       },
  //       avatarFrameClient.uploadDecoratedAvatar,
  //       actorHeaders,
  //     );
  //   },
  // });
  //
  // runSyncTestCase({
  //   methodName: Methods.REMOVE_DECORATED_AVATAR,
  //   performAction: async () => {
  //     const avatarFrameFromList = await getRandomAvatarFrame(actorHeaders);
  //
  //     await getResponseSuccess(
  //       {
  //         avatarFrameId: avatarFrameFromList.avatarFrameId,
  //         decoratedAvatarPath:
  //           'https://fs.ugc.ziicdn.net/' + ulid() + '/cover.jpg',
  //       },
  //       avatarFrameClient.uploadDecoratedAvatar,
  //       actorHeaders,
  //     );
  //
  //     await getResponseSuccess(
  //       {},
  //       avatarFrameClient.removeDecoratedAvatar,
  //       actorHeaders,
  //     );
  //   },
  // });

  runSyncTestCase({
    methodName: Methods.UNBLOCK_USER,
    performAction: async () => {
      await getResponseSuccess(
        {
          targetUserId: recipient.userId,
        },
        userSettingClient.blockUser,
        actorHeaders,
      );

      await getResponseSuccess(
        {
          targetUserId: recipient.userId,
        },
        userSettingClient.unblockUser,
        actorHeaders,
      );
    },
  });

  //TODO https://github.com/halonext/ziichat-issues/issues/22066
  describe.skip(Methods.BLOCK_USER, () => {
    beforeAll(async () => {
      await prepareData();
      await wait(4000);

      await getResponseSuccess(
        {
          targetUserId: recipient.userId,
        },
        userSettingClient.blockUser,
        actorHeaders,
      );
    });

    ListTimeSyncWithoutEqual.forEach((time) => {
      describe(time, () => {
        for (const user of userList) {
          const haveData = time.includes('<') && user.role == 'actor';
          const title = haveData ? '' : ' not';
          it(`should${title} return data, case: blockUser -> syncUsers, ${user.role} request, ${time}`, async () => {
            const response = await userViewClient.syncUsers(
              {
                updateTimeAfter: await getTimeForSync(time, actor),
                userIds: [recipient.userId as string],
              },
              createHeaders(user.user.token as string),
            );

            if (haveData) {
              const { data, userDeleted, syncTime } = response.data;
              if (!userDeleted) throw new Error(NULL_UNDEFINED_ERROR);
              if (!syncTime) throw new Error(NULL_UNDEFINED_ERROR);
              assertIsDate(syncTime);
              expect(data).toStrictEqual([]);
              expect(userDeleted).toHaveLength(1);
              expect(userDeleted[0].userId).toBe(recipient.userId);
              expect(userDeleted[0].type).toBe(
                V3UserDeletedTypeEnum.USER_BLOCKED,
              );
            } else {
              expectSyncResponseUndefined(response.data);
            }
          });
        }
      });
    });
  });

  it('should not return data when userId element of myself', async () => {
    await prepareData();

    const response = await userViewClient.syncUsers(
      {
        updateTimeAfter: syncTime,
        userIds: [actor.userId as string],
      },
      actorHeaders,
    );
    expectSyncResponseUndefined(response.data);
  });

  describe(Methods.MOCK_USERS, () => {
    beforeAll(async () => {
      await prepareData();
      await wait(4000);

      userInfo = await getUserProfile(actor);
    });

    ListTimeSync.forEach((time) => {
      const notData = time.includes('>');
      const title = notData ? 'not' : '';
      describe(time, () => {
        it(`should ${title} return data, case: ${Methods.MOCK_USERS} -> ${Methods.SYNC_USERS}, recipient request, ${time}`, async () => {
          const [newUser] = await mockUsersData(prefix, 1);
          const profileNewUser = await getUserProfile(newUser);

          const response = await userViewClient.syncUsers(
            {
              updateTimeAfter: await getTimeForSync(time, actor),
              userIds: [actor.userId as string, newUser.userId as string],
            },
            recipientHeaders,
          );
          const { data, userDeleted, syncTime } = response.data;
          assertIsDate(syncTime as string);
          expect(userDeleted).toStrictEqual([]);

          if (time.includes('<')) {
            expect(data).toHaveLength(2);
            expect(data).toMatchObject([userInfo, profileNewUser]);
          } else if (time.includes('=')) {
            expect(data).toHaveLength(1);
            expect(data).toMatchObject([profileNewUser]);
          } else {
            expectSyncResponseUndefined(response.data);
          }
        });
      });
    });
  });
});
