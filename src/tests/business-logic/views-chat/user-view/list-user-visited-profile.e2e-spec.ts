import { getPrefixMockUser } from '../../../../../jest-e2e';
import { checkPaginationUserView } from '../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  createHeaders,
  deletePresenceDataObj,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../helpers/user-view-service';
import { HEADERS, MAX_LIMIT, Methods } from '../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../error-message';

describe(Methods.LIST_USER_VISITED_PROFILE, () => {
  const prefix = getPrefixMockUser() + Methods.LIST_USER_VISITED_PROFILE;
  const quantity = 15;

  let mainUser;
  let otherUsers;

  let metadataMainUser: HEADERS;
  let metadataOtherUser: HEADERS;

  let originalList;

  const createUserList = async () => {
    [mainUser, ...otherUsers] = await mockUsersData(prefix, quantity + 1);

    metadataMainUser = createHeaders(mainUser.token as string);
    metadataOtherUser = createHeaders(
      otherUsers[otherUsers.length - 1].token as string,
    );
  };

  beforeAll(async () => {
    await createUserList();
    for (const user of otherUsers) {
      const metadata = createHeaders(user.token as string);

      await getResponseSuccess(
        { userId: mainUser.userId },
        userProfileClient.visitedProfile,
        metadata,
      );
    }

    originalList = await getResponseSuccess(
      {},
      userViewClient.listUserVisitedProfile,
      metadataMainUser,
    );
  });

  describe('Paging', () => {
    it('should return correct data with limit = the number of data', async () => {
      const response = await getResponseSuccess(
        { limit: quantity },
        userViewClient.listUserVisitedProfile,
        metadataMainUser,
      );
      expect(response.data?.length).toEqual(quantity);
    });

    it('should return correct data with limit > the number of data', async () => {
      const response = await getResponseSuccess(
        { limit: quantity + 1 },
        userViewClient.listUserVisitedProfile,
        metadataMainUser,
      );
      expect(response.data?.length).toEqual(quantity);
    });

    it('should return true with last list length < limit', async () => {
      await checkPaginationUserView(
        { quantity: otherUsers.length, limit: 3 },
        originalList.data,
        metadataMainUser,
        userViewClient.listUserVisitedProfile,
      );
    });

    it('should return true with last list length === limit', async () => {
      await checkPaginationUserView(
        { quantity: otherUsers.length, limit: 2 },
        originalList.data,
        metadataMainUser,
        userViewClient.listUserVisitedProfile,
      );
    });

    it('should return correct data when the number of data is equal to the maximum length (500)', async () => {
      const [newUser] = await mockUsersData(prefix, 1);

      await Promise.all(
        Array.from({ length: 50 }, async () => {
          const users = await mockUsersData(prefix, 10);

          await Promise.all(
            users.map((user) => {
              const metadata = createHeaders(user.token as string);
              return getResponseSuccess(
                { userId: newUser.userId },
                userProfileClient.visitedProfile,
                metadata,
              );
            }),
          );
        }),
      );

      const response = await getResponseSuccess(
        { limit: MAX_LIMIT },
        userViewClient.listUserVisitedProfile,
        createHeaders(newUser.token as string),
      );
      expect(response.data?.length).toEqual(MAX_LIMIT);
    }, 100000);
  });
  it('should return true when dont have user visited profile', async () => {
    const response = await getResponseSuccess(
      {},
      userViewClient.listUserVisitedProfile,
      metadataOtherUser,
    );
    expect(response.data).toEqual([]);
  });

  it('should sort the newest user at the top of the list', async () => {
    const [newUser] = await mockUsersData(prefix, 1);

    const metadata = createHeaders(newUser.token as string);

    await getResponseSuccess(
      { userId: mainUser.userId },
      userProfileClient.visitedProfile,
      metadata,
    );

    const profileViewer = await getUserProfile(newUser);

    const response = await getResponseSuccess(
      {},
      userViewClient.listUserVisitedProfile,
      metadataMainUser,
    );

    if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
    if (!response.data[0].user) throw new Error(NULL_UNDEFINED_ERROR);

    deletePresenceDataObj(response.data[0].user);

    expect(response.data[0].visitedTime).toEqual(
      response.data[0].lastVisitedTime,
    );
    expect(profileViewer).toMatchObject(response.data[0].user);
  });

  describe('Update displayName, avatar, coverPhoto', () => {
    const avatarPath =
      'https://fs.ugc.ziicdn.net/01JEZJ0GPMSHH2RVX9QTSJJNRX/heic_300kB.heic';
    beforeAll(async () => {
      await createUserList();
      for (const user of otherUsers) {
        const metadata = createHeaders(user.token as string);

        await getResponseSuccess(
          { userId: mainUser.userId },
          userProfileClient.visitedProfile,
          metadata,
        );
      }
    });

    it('should return correct data when the user has updated displayName', async () => {
      await getResponseSuccess(
        { displayName: 'new display name' },
        userProfileClient.updateUserDisplayName,
        metadataOtherUser,
      );

      await wait(2000);

      const profileViewer = await getUserProfile(
        otherUsers[otherUsers.length - 1],
      );

      const response = await getResponseSuccess(
        {},
        userViewClient.listUserVisitedProfile,
        metadataMainUser,
      );

      if (!response.data || !response.data[0].user) {
        throw new Error(NULL_UNDEFINED_ERROR);
      }

      deletePresenceDataObj(response.data[0].user);
      expect(profileViewer).toMatchObject(response.data[0].user);
    });

    it('should return correct data when the user has updated avatar', async () => {
      await getResponseSuccess(
        {
          avatarPath: avatarPath,
        },
        userProfileClient.updateUserAvatar,
        metadataOtherUser,
      );

      const profileViewer = await getUserProfile(
        otherUsers[otherUsers.length - 1],
      );

      const response = await getResponseSuccess(
        {},
        userViewClient.listUserVisitedProfile,
        metadataMainUser,
      );

      if (!response.data || !response.data[0].user) {
        throw new Error(NULL_UNDEFINED_ERROR);
      }

      deletePresenceDataObj(response.data[0].user);
      expect(profileViewer).toMatchObject(response.data[0].user);
    });

    it('should return correct data when the user has updated cover photo', async () => {
      await getResponseSuccess(
        { coverPath: avatarPath },
        userProfileClient.addCoverPhoto,
        metadataOtherUser,
      );

      await getResponseSuccess(
        { coverPath: avatarPath },
        userProfileClient.updateCoverPhoto,
        metadataOtherUser,
      );

      const profileViewer = await getUserProfile(
        otherUsers[otherUsers.length - 1],
      );

      const response = await getResponseSuccess(
        {},
        userViewClient.listUserVisitedProfile,
        metadataMainUser,
      );

      if (!response.data || !response.data[0].user) {
        throw new Error(NULL_UNDEFINED_ERROR);
      }

      deletePresenceDataObj(response.data[0].user);
      expect(profileViewer).toMatchObject(response.data[0].user);
    });
  });
});
