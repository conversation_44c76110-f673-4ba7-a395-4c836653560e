import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3DirectMessageStatusEnum } from '../../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import { expectGetDMChannelData } from '../../../../../expected-results/views-chat/channel-view';
import channelViewClient from '../../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../../helpers/message-service';
import { getLastMsgInBot } from '../../../../../helpers/message-view-service';
import { createHeaders } from '../../../../../helpers/shared/common';
import { HEADERS, Methods, ZIICHAT_BOT_USERID } from '../../../../const';
import {
  ChannelError,
  GET_DM_CHANNEL_DETAILS,
} from '../../../../error-message';

describe(Methods.GET_DM_CHANNEL, () => {
  const prefix = getPrefixMockUser() + Methods.GET_DM_CHANNEL;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  beforeEach(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  });

  it('should return true when getDMChannel (contacted)', async () => {
    await mockDmMessageData(senderHeaders, receiver, faker.string.alpha(10));
    const msgSend = await mockDmMessageData(
      receiverHeaders,
      sender,
      faker.string.alpha(10),
    );

    await expectGetDMChannelData(
      sender,
      msgSend,
      sender,
      receiver,
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
      1,
    );
    await expectGetDMChannelData(sender, msgSend, receiver, sender);
  });

  it('should return true when getDMChannel (pending)', async () => {
    const msgSend = await mockDmMessageData(
      senderHeaders,
      receiver,
      faker.string.alpha(10),
    );

    await expectGetDMChannelData(
      sender,
      msgSend,
      sender,
      receiver,
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
    );

    await expectGetDMChannelData(
      sender,
      msgSend,
      receiver,
      sender,
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
      1,
    );
  });

  it('should return true when user getDMChannel with Bot', async () => {
    const msg = await getLastMsgInBot(sender);
    await expectGetDMChannelData(
      sender,
      msg,
      sender,
      { userId: ZIICHAT_BOT_USERID },
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
      1,
    );
  });

  it('should return error when user getDMChannel (stranger - never chat)', async () => {
    await expectForParamInvalid(
      { userId: receiver.userId as string },
      channelViewClient.getDmChannel,
      senderHeaders,
      { ...ChannelError.GET_CHANNEL, details: GET_DM_CHANNEL_DETAILS.at(0) },
    );
  });

  it('should return error when user getDMChannel (friend - never chat)', async () => {
    await createAddAndAcceptFriends(sender, [receiver]);
    await expectForParamInvalid(
      { userId: receiver.userId as string },
      channelViewClient.getDmChannel,
      senderHeaders,
      { ...ChannelError.GET_CHANNEL, details: GET_DM_CHANNEL_DETAILS.at(0) },
    );
  });
});
