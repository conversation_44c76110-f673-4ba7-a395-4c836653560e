import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Channel,
  V3ChannelMetadata,
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
  V3DataInclude,
  V3Member,
  V3User,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import { V3GetChannelRequest } from '../../../../../../utils/http-client/views-chat-client';
import { expectChannelMetadata } from '../../../../../expected-results/commands-chat/channel';
import { expectForParamOnGateway } from '../../../../../expected-results/expected-errors';
import { expectMemberDataList } from '../../../../../expected-results/views-chat/member-view';
import { expectForUserDataList } from '../../../../../expected-results/views-chat/user-view';
import { mockChannelData } from '../../../../../helpers/channel-service';
import channelViewClient from '../../../../../helpers/channel-view-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageViewClient from '../../../../../helpers/message-view-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  ADMIN_PERMISSIONS,
  MEMBER_BROADCAST_PERMISSIONS,
  MEMBER_PERMISSIONS,
  Methods,
  OWNER_PERMISSIONS,
  WORKSPACE_ID,
} from '../../../../const';
import {
  NULL_UNDEFINED_ERROR,
  UNAUTHORIZED_REQUEST,
} from '../../../../error-message';

describe(Methods.GET_CHANNEL, () => {
  const prefix = getPrefixMockUser() + Methods.GET_CHANNEL;
  let userDefault: V3MockedUser;
  let userBlue: V3MockedUser;

  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let users: V3MockedUser[];

  let channel: V3Channel;
  let broadcastChannel: V3Channel;

  let getChannelReq: V3GetChannelRequest;
  let getBroadcastChannel: V3GetChannelRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: faker.string.alpha(10),
  };

  beforeAll(async () => {
    users = [userDefault, admin, member] = await mockUsersData(prefix, 3);
    [userBlue, stranger] = await mockUsersData(
      prefix,
      2,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );

    channel = await mockChannelData(createChannelReq, users, [admin]);
    getChannelReq = {
      workspaceId: channel.workspaceId as string,
      channelId: channel.channelId as string,
    };

    broadcastChannel = await mockChannelData(
      {
        ...createChannelReq,
        channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
      },
      [userBlue, admin, member],
      [admin],
    );
    getBroadcastChannel = {
      workspaceId: broadcastChannel.workspaceId as string,
      channelId: broadcastChannel.channelId as string,
    };
  });

  const expectForGetChannel = async (
    getChannelReq: V3GetChannelRequest,
    user: V3MockedUser,
    userIds: string[],
    channelExpected: V3Channel,
    permission = OWNER_PERMISSIONS,
  ) => {
    const headers = createHeaders(user.token as string);
    const getChannelRes = await getResponseSuccess(
      getChannelReq,
      channelViewClient.getChannel,
      headers,
    );

    const { data, includes } = getChannelRes;

    const { users, members, messages, channelMetadata, channels } =
      includes as V3DataInclude;
    expect(data?.channel).toEqual(channelExpected);
    expect(channels).toEqual([]);

    await expectForUserDataList(users as V3User[], userIds);
    await expectMemberDataList(user, members as V3Member[], userIds);

    if (!channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
    if (!messages) throw new Error(NULL_UNDEFINED_ERROR);
    expectChannelMetadata(
      channelMetadata[0] as V3ChannelMetadata,
      channelExpected,
      messages[0].messageId as string,
      0,
      permission,
    );

    const listMsg = await getResponseSuccess(
      getChannelReq,
      messageViewClient.listMessages,
      headers,
    );

    if (!listMsg?.data) throw new Error(NULL_UNDEFINED_ERROR);
    expect(messages[0]).toEqual(listMsg?.data[0].message);
  };

  describe('Business logic', () => {
    describe('Channel 1-n', () => {
      it('should return true when owner getChannel', async () => {
        const userIds = [userDefault.userId as string];
        await expectForGetChannel(getChannelReq, userDefault, userIds, channel);
      });

      it('should return true when admin getChannel', async () => {
        const userIds = [userDefault.userId as string];
        await expectForGetChannel(
          getChannelReq,
          admin,
          userIds,
          channel,
          ADMIN_PERMISSIONS,
        );
      });

      it('should return true when member getChannel', async () => {
        const userIds = [userDefault.userId as string];
        await expectForGetChannel(
          getChannelReq,
          member,
          userIds,
          channel,
          MEMBER_PERMISSIONS,
        );
      });

      it('should return error when stranger getChannel', async () => {
        await expectForParamOnGateway(
          getChannelReq,
          channelViewClient.getChannel,
          createHeaders(stranger.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });
    });

    describe('Broadcast Channel', () => {
      it('should return true when owner getChannel', async () => {
        const userIds = [userBlue.userId as string];
        await expectForGetChannel(
          getBroadcastChannel,
          userBlue,
          userIds,
          broadcastChannel,
        );
      });

      it('should return true when admin getChannel', async () => {
        const userIds = [userBlue.userId as string];
        await expectForGetChannel(
          getBroadcastChannel,
          admin,
          userIds,
          broadcastChannel,
          ADMIN_PERMISSIONS,
        );
      });

      it('should return true when member getChannel', async () => {
        const userIds = [userBlue.userId as string];
        await expectForGetChannel(
          getBroadcastChannel,
          member,
          userIds,
          broadcastChannel,
          MEMBER_BROADCAST_PERMISSIONS,
        );
      });

      it('should return error when stranger getChannel', async () => {
        await expectForParamOnGateway(
          getBroadcastChannel,
          channelViewClient.getChannel,
          createHeaders(stranger.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });
    });
  });
});
