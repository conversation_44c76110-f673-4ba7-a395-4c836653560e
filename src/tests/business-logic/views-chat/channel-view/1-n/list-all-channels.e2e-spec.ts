import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Channel,
  V3CreateChannelRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import { V3ChannelTypeEnum } from '../../../../../../utils/http-client/views-chat-client';
import { expectListAllChannels } from '../../../../../expected-results/commands-chat/channel';
import { checkPaginationChannelView } from '../../../../../expected-results/views-chat/channel-view';
import channelClient, {
  getDMAfterSendMsg,
  mockChannelData,
} from '../../../../../helpers/channel-service';
import channelViewClient, {
  getChannelBotData,
} from '../../../../../helpers/channel-view-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  HEADERS,
  MAX_LIMIT,
  Methods,
  MIN_LIMIT,
  WORKSPACE_ID,
} from '../../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../../error-message'; // ListAllChannels, listChannels, listDMChannels

// ListAllChannels, listChannels, listDMChannels
describe(Methods.LIST_ALL_CHANNELS, () => {
  const prefix = getPrefixMockUser() + Methods.LIST_ALL_CHANNELS;

  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;

  let ownerHeaders: HEADERS;
  let adminHeaders: HEADERS;
  let memberHeaders: HEADERS;

  const quantity = 9;
  let users: V3MockedUser[] = [];

  const ownerChannels: V3Channel[] = [];
  let ownerChannel1N: V3Channel[] = [];
  let ownerDMChannel: V3Channel[] = [];

  const adminChannels: V3Channel[] = [];
  const memberChannels: V3Channel[] = [];

  const createChannel: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: faker.string.alpha(10),
  };

  const prepareData = async (
    request: V3CreateChannelRequest,
    owner: V3MockedUser,
    channelNum = quantity,
  ) => {
    await Promise.all(
      Array.from({ length: channelNum }).map(async (_, i) => {
        // Create Channel
        const channel = await mockChannelData(
          request,
          [owner, admin, member],
          [admin],
        );
        ownerChannels.unshift(channel); // sort first position
        adminChannels.unshift(channel);
        memberChannels.unshift(channel);

        const broadcastChannel = await mockChannelData(
          {
            ...request,
            channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
          },
          [owner, admin, member],
        );

        ownerChannels.unshift(broadcastChannel);
        adminChannels.unshift(broadcastChannel);
        memberChannels.unshift(broadcastChannel);

        //Send dm message to create dm channel
        ownerChannels.unshift(await getDMAfterSendMsg(owner, users[i]));
        adminChannels.unshift(await getDMAfterSendMsg(admin, users[i]));
        memberChannels.unshift(await getDMAfterSendMsg(member, users[i]));
      }),
    );

    ownerChannels.push(await getChannelBotData(owner));
    adminChannels.push(await getChannelBotData(admin));
    memberChannels.push(await getChannelBotData(member));
  };

  const expectLimit = async (
    limit: number,
    limitExpected: number,
    method = channelViewClient.listAllChannels,
  ) => {
    const listRes = await getResponseSuccess(
      { limit: limit },
      method,
      createHeaders(owner.token as string),
    );

    expect(listRes?.data).toHaveLength(limitExpected);
  };

  describe('Success cases', () => {
    beforeAll(async () => {
      [owner, admin, member] = await mockUsersData(
        prefix,
        3,
        V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
      );
      users = await mockUsersData(prefix, quantity);

      ownerHeaders = createHeaders(owner.token as string);
      adminHeaders = createHeaders(admin.token as string);
      memberHeaders = createHeaders(member.token as string);

      await prepareData(createChannel, owner);

      ownerChannel1N = ownerChannels.filter(
        (channel) => channel.type != V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM,
      );

      ownerDMChannel = ownerChannels.filter(
        (channel) => channel.type == V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM,
      );
    }, 100000);

    it('should return only channel Bot after user register', async () => {
      const [newUser] = await mockUsersData(prefix, 1);
      const channelBot = await getChannelBotData(newUser);

      const listAllChannelsRes = await getResponseSuccess(
        {},
        channelViewClient.listAllChannels,
        createHeaders(newUser.token as string),
      );
      expect(listAllChannelsRes?.data).toHaveLength(1);
      expect(listAllChannelsRes?.data).toEqual([{ channel: channelBot }]);

      const listDmChannelsRes = await getResponseSuccess(
        {},
        channelViewClient.listDmChannels,
        createHeaders(newUser.token as string),
      );
      expect(listDmChannelsRes?.data).toHaveLength(1);
      expect(listDmChannelsRes?.data).toEqual([{ channel: channelBot }]);

      // Note return Bot
      const listChannelsRes = await getResponseSuccess(
        {},
        channelViewClient.listChannels,
        createHeaders(newUser.token as string),
      );
      expect(listChannelsRes?.data).toHaveLength(0);
    });

    describe('Role', () => {
      it('Should return true when owner listing all channels', async () => {
        // ListAllChannels
        await expectListAllChannels(owner, ownerHeaders, ownerChannels, [
          owner.userId as string,
        ]);

        // ListChannels
        await expectListAllChannels(
          owner,
          ownerHeaders,
          ownerChannel1N,
          [owner.userId as string],
          channelViewClient.listChannels,
        );

        // ListChannels
        await expectListAllChannels(
          owner,
          ownerHeaders,
          ownerDMChannel,
          [owner.userId as string],
          channelViewClient.listDmChannels,
        );
      });

      it('Should return true when admin listing all channels', async () => {
        await expectListAllChannels(admin, adminHeaders, adminChannels, [
          owner.userId as string,
        ]);
      });

      it('Should return true when member1 listing all channels', async () => {
        await expectListAllChannels(member, memberHeaders, memberChannels, [
          owner.userId as string,
        ]);
      });
    });

    describe('Limit', () => {
      it('Should return true with limit = min limit', async () => {
        await expectLimit(
          MIN_LIMIT,
          MIN_LIMIT,
          channelViewClient.listAllChannels,
        );

        await expectLimit(MIN_LIMIT, MIN_LIMIT, channelViewClient.listChannels);

        await expectLimit(
          MIN_LIMIT,
          MIN_LIMIT,
          channelViewClient.listDmChannels,
        );
      });

      it('Should return true with limit = data list', async () => {
        await expectLimit(
          ownerChannels.length,
          ownerChannels.length,
          channelViewClient.listAllChannels,
        );

        await expectLimit(
          ownerChannel1N.length,
          ownerChannel1N.length,
          channelViewClient.listChannels,
        );

        await expectLimit(
          ownerDMChannel.length,
          ownerDMChannel.length,
          channelViewClient.listDmChannels,
        );
      });

      it('Should return true with limit > data list', async () => {
        await expectLimit(
          ownerChannels.length + 5,
          ownerChannels.length,
          channelViewClient.listAllChannels,
        );

        await expectLimit(
          ownerChannel1N.length + 5,
          ownerChannel1N.length,
          channelViewClient.listChannels,
        );

        await expectLimit(
          ownerDMChannel.length + 5,
          ownerDMChannel.length,
          channelViewClient.listDmChannels,
        );
      });
    });

    describe('Pagination', () => {
      describe('ListAllChannel', () => {
        it('Should return true when paging list all channels (record % limit == 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listAllChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listAllChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 7 }, // ownerChannel = 28
            originalList: data,
          });
        });

        it('Should return true when paging list all channels (record % limit != 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listAllChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listAllChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 8 }, // ownerChannel = 28
            originalList: data,
          });
        });
      });

      describe('ListChannels', () => {
        it('Should return true when paging list channels (record % limit == 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 3 }, // ownerChannel1N = 18
            originalList: data,
          });
        });

        it('Should return true when paging list channels (record % limit != 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 4 }, // ownerChannel1N = 18
            originalList: data,
          });
        });
      });

      describe('ListDMChannels', () => {
        it('Should return true when paging list DM channels (record % limit == 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listDmChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listDmChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 2 }, // ownerDMChannel = 10
            originalList: data,
          });
        });

        it('Should return true when paging list DM channels (record % limit != 0)', async () => {
          const listRes = await getResponseSuccess(
            {},
            channelViewClient.listDmChannels,
            createHeaders(owner.token as string),
          );
          const { data } = listRes;
          if (!data) throw new Error(NULL_UNDEFINED_ERROR);

          await checkPaginationChannelView({
            fetchList: channelViewClient.listDmChannels,
            headers: ownerHeaders,
            options: { quantity: data.length, limit: 3 }, // ownerDMChannel = 10
            originalList: data,
          });
        });
      });
    });

    it(`Should return true with max limit = ${MAX_LIMIT}`, async () => {
      await Promise.all(
        Array.from({ length: MAX_LIMIT / 2 }).map(async (_, i) => {
          await getResponseSuccess(
            {
              workspaceId: WORKSPACE_ID,
              name: `channel ${i}`,
            },
            channelClient.createChannel,
            ownerHeaders,
          );
          await getResponseSuccess(
            {
              workspaceId: WORKSPACE_ID,
              name: `channel ${i}`,
              channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
            },
            channelClient.createChannel,
            ownerHeaders,
          );
        }),
      );
      const listRes = await getResponseSuccess(
        { limit: MAX_LIMIT },
        channelViewClient.listAllChannels,
        createHeaders(owner.token as string),
      );

      expect(listRes?.data).toHaveLength(MAX_LIMIT);
    }, 300000);
  });
});
