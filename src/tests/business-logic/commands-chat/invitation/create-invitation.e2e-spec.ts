import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3Channel } from '../../../../../utils/http-client/cloudevent-client';
import {
  V3CreateChannelRequest,
  V3CreateInvitationRequest,
  V3InvitationData,
} from '../../../../../utils/http-client/commands-chat-client';
import { V3SendDMMessageRequest } from '../../../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { GetChannelParams } from '../../../../../utils/http-client/views-chat-client';
import { expectForCreateInvitation } from '../../../../expected-results/commands-chat/invitation';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../expected-results/expected-errors';
import channelClient, {
  mockChannelData,
} from '../../../../helpers/channel-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import invitationClient from '../../../../helpers/invitation-service';
import invitationViewClient from '../../../../helpers/invitation-view-service';
import memberClient from '../../../../helpers/member-service';
import messageClient from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
  randomEmojis,
} from '../../../../helpers/shared/common';
import {
  EXPIRES_IN_DEFAULT,
  HEADERS,
  MAX_USES_DEFAULT,
  Methods,
  PREFIX_INVITATION_LINK,
  WORKSPACE_ID,
} from '../../../const';
import {
  CHANNEL_INVALID,
  InvitationError,
  UNAUTHORIZED_REQUEST,
} from '../../../error-message';

describe(Methods.CREATE_INVITATION, () => {
  const prefix = getPrefixMockUser() + Methods.CREATE_INVITATION;
  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let ownerHeaders: HEADERS;
  let adminHeaders: HEADERS;

  let users: V3MockedUser[];
  let channel: V3Channel;

  let createInvitationReq: V3CreateInvitationRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);

    ownerHeaders = createHeaders(owner.token as string);
    adminHeaders = createHeaders(admin.token as string);
    channel = await mockChannelData(createChannelReq, users, [admin]);

    createInvitationReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
    };
  });

  const expectUserNotInMembers = async (
    code: string,
    metadata: HEADERS,
    userIdLeave: string,
    isJoined: boolean,
  ): Promise<void> => {
    const getInviteRes = await getResponseSuccess(
      { code },
      invitationViewClient.getInvitation,
      metadata,
    );
    const invitationData = getInviteRes?.data as V3InvitationData;
    const isExistInMembers = getInviteRes?.data?.channel?.members?.some(
      (mem) => mem.userId == userIdLeave,
    );
    expect(isExistInMembers).toBeFalsy();
    // TODO https://github.com/halonext/ziichat-issues/issues/22966
    expect(invitationData?.isJoined).toEqual(isJoined);
  };

  const expectInvitationAfterLeave = async (
    codes: string[],
    memberLeave: V3MockedUser,
  ) => {
    const memberLeaveHeaders = createHeaders(memberLeave.token as string);

    for (const metadata of [memberLeaveHeaders, ownerHeaders, adminHeaders]) {
      let isJoined = true;
      if (metadata == memberLeaveHeaders) isJoined = false;

      // GetInvitation after leave channel
      for (const code of codes) {
        await expectUserNotInMembers(
          code,
          metadata,
          memberLeave.userId as string,
          isJoined,
        );
      }
    }
  };

  describe('Success logic', () => {
    it('should return true with all params', async () => {
      const request: V3CreateInvitationRequest = {
        ...createInvitationReq,
        maxUses: MAX_USES_DEFAULT,
        expiresIn: EXPIRES_IN_DEFAULT,
      };
      await expectForCreateInvitation(request, owner, channel);
    });

    it('should return true with all param when expiresIn and maxUses different default values', async () => {
      const expiresIn = 900;
      const maxUses = 50;
      const request = {
        ...createInvitationReq,
        expiresIn: expiresIn,
        maxUses: maxUses,
      };
      await expectForCreateInvitation(request, member, channel);
    });

    describe('Roles', () => {
      it('should return true when owner create invitation', async () => {
        await expectForCreateInvitation(createInvitationReq, owner, channel);
      });

      it('should return true when admin create invitation', async () => {
        await expectForCreateInvitation(createInvitationReq, admin, channel);
      });

      it('should return true when member create invitation', async () => {
        await expectForCreateInvitation(createInvitationReq, member, channel);
      });
    });

    describe('Relative Leave channel', () => {
      let channelForLeave: V3Channel;
      let getChannelReq: GetChannelParams;
      let inviteLinkFromCreateChannel: string;
      let codeFromCreateChannel: string;

      let memberLeave: V3MockedUser;
      let memberLeaveHeaders: HEADERS;

      beforeAll(async () => {
        channelForLeave = await mockChannelData(createChannelReq, users, [
          admin,
        ]);

        getChannelReq = {
          channelId: channelForLeave.channelId,
          workspaceId: channelForLeave.workspaceId,
        };
        inviteLinkFromCreateChannel = channelForLeave.invitationLink as string;

        codeFromCreateChannel = inviteLinkFromCreateChannel.substring(
          PREFIX_INVITATION_LINK.length,
        ) as string;
      });

      beforeEach(async () => {
        [memberLeave] = await mockUsersData(prefix, 1);
        memberLeaveHeaders = createHeaders(memberLeave.token as string);

        await getResponseSuccess(
          { invitationLink: inviteLinkFromCreateChannel },
          invitationClient.acceptInvitation,
          memberLeaveHeaders,
        );
      });

      it('should not return member after LeaveChannel', async () => {
        await getResponseSuccess(
          getChannelReq,
          memberClient.leaveChannel,
          memberLeaveHeaders,
        );
        await expectForParamOnGateway(
          getChannelReq,
          invitationClient.createInvitation,
          memberLeaveHeaders,
          UNAUTHORIZED_REQUEST,
        );

        const createInvitationRes = await getResponseSuccess(
          getChannelReq,
          invitationClient.createInvitation,
          ownerHeaders,
        );

        const code = createInvitationRes?.data?.code as string;

        await expectInvitationAfterLeave(
          [code, codeFromCreateChannel],
          memberLeave,
        );
      });

      it('should not return member after RemoveFromChannel', async () => {
        await getResponseSuccess(
          { ...getChannelReq, userId: memberLeave.userId },
          memberClient.removeFromChannel,
          adminHeaders,
        );
        await expectForParamOnGateway(
          getChannelReq,
          invitationClient.createInvitation,
          memberLeaveHeaders,
          UNAUTHORIZED_REQUEST,
        );

        const createInvitationRes = await getResponseSuccess(
          getChannelReq,
          invitationClient.createInvitation,
          ownerHeaders,
        );

        const code = createInvitationRes?.data?.code as string;

        await expectInvitationAfterLeave(
          [code, codeFromCreateChannel],
          memberLeave,
        );
      });

      it('should not return member after BanFromChannel', async () => {
        await getResponseSuccess(
          { ...getChannelReq, userId: memberLeave.userId },
          memberClient.banFromChannel,
          adminHeaders,
        );
        await expectForParamOnGateway(
          getChannelReq,
          invitationClient.createInvitation,
          memberLeaveHeaders,
          UNAUTHORIZED_REQUEST,
        );

        const createInvitationRes = await getResponseSuccess(
          getChannelReq,
          invitationClient.createInvitation,
          ownerHeaders,
        );

        const code = createInvitationRes?.data?.code as string;

        await expectInvitationAfterLeave(
          [code, codeFromCreateChannel],
          memberLeave,
        );
      });

      it('should not return member after TransferOwnershipAndLeaveChannel', async () => {
        await getResponseSuccess(
          { ...getChannelReq, userId: member.userId },
          memberClient.transferOwnershipAndLeaveChannel,
          ownerHeaders,
        );

        await expectForParamOnGateway(
          getChannelReq,
          invitationClient.createInvitation,
          ownerHeaders,
          UNAUTHORIZED_REQUEST,
        );

        const createInvitationRes = await getResponseSuccess(
          getChannelReq,
          invitationClient.createInvitation,
          adminHeaders,
        );

        const code = createInvitationRes?.data?.code as string;
        for (const metadata of [
          memberLeaveHeaders,
          ownerHeaders,
          adminHeaders,
        ]) {
          let isJoined = true;
          if (metadata == ownerHeaders) isJoined = false; // because owner leave channel
          for (const codeInvite of [code, codeFromCreateChannel]) {
            await expectUserNotInMembers(
              codeInvite,
              metadata,
              owner.userId as string,
              isJoined,
            );
          }
        }
      });
    });
  });

  describe('Error cases', () => {
    it('should return false when user create invitation in DM channel', async () => {
      const sendDMMsgReq: V3SendDMMessageRequest = {
        userId: member.userId as string,
        content: randomEmojis(3),
        ref: ulid(),
      };

      const msgRes = await getResponseSuccess(
        sendDMMsgReq,
        messageClient.sendDmMessage,
        createHeaders(admin.token as string),
      );

      await expectForParamInvalid(
        {
          channelId: msgRes?.data?.message?.channelId as string,
          workspaceId: msgRes?.data?.message?.workspaceId as string,
        },
        invitationClient.createInvitation,
        createHeaders(member.token as string),
        { ...InvitationError.CREATE_INVITATION, details: CHANNEL_INVALID },
      );
    });

    it('should return false when user create invitation in deleted channel', async () => {
      const newChannel = await mockChannelData(createChannelReq, [owner]);
      const getChannelReq = {
        workspaceId: newChannel.workspaceId as string,
        channelId: newChannel.channelId as string,
      };

      await getResponseSuccess(
        getChannelReq,
        channelClient.deleteChannel,
        createHeaders(owner.token as string),
      );

      await expectForParamOnGateway(
        getChannelReq,
        invitationClient.createInvitation,
        createHeaders(owner.token as string),
        UNAUTHORIZED_REQUEST,
      );
    });

    it('should return false when user who is not member create invitation', async () => {
      [stranger] = await mockUsersData(prefix, 1);

      await expectForParamOnGateway(
        createInvitationReq,
        invitationClient.createInvitation,
        createHeaders(stranger.token as string),
        UNAUTHORIZED_REQUEST,
      );
    });
  });
});
