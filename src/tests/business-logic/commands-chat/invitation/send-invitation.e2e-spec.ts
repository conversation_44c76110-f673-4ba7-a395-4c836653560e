import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3Channel } from '../../../../../utils/http-client/cloudevent-client';
import {
  V3CreateChannelRequest,
  V3SendInvitationRequest,
} from '../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectInviteMessageInBot } from '../../../../expected-results/views-message/message-view';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../expected-results/expected-errors';
import { mockChannelData } from '../../../../helpers/channel-service';
import { createAddAndAcceptFriends } from '../../../../helpers/friend-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import invitationClient from '../../../../helpers/invitation-service';
import { getLastMsgInBot } from '../../../../helpers/message-view-service';
import { userSettingClient } from '../../../../helpers/user-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import {
  HEADERS,
  Methods,
  WELCOME_MESSAGE,
  WORKSPACE_ID,
  ZIICHAT_BOT_USERID,
} from '../../../const';
import {
  InvitationError,
  NULL_UNDEFINED_ERROR,
  UNAUTHORIZED_REQUEST,
} from '../../../error-message';

describe(Methods.SEND_INVITATION, () => {
  const prefix = getPrefixMockUser() + Methods.SEND_INVITATION;
  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let friend: V3MockedUser;
  let stranger: V3MockedUser;
  let blockedUser: V3MockedUser;
  let blockerUser: V3MockedUser;

  let ownerHeaders: HEADERS;
  let adminHeaders: HEADERS;
  let memberHeaders: HEADERS;
  let friendHeaders: HEADERS;
  let strangerHeaders: HEADERS;
  let blockedUserHeaders: HEADERS;
  let blockerUserHeaders: HEADERS;

  let users: V3MockedUser[];
  let channel: V3Channel;

  let sendInvitationReq: V3SendInvitationRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);
    [friend, stranger, blockedUser, blockerUser] = await mockUsersData(
      prefix + '_extra',
      4,
    );

    ownerHeaders = createHeaders(owner.token as string);
    adminHeaders = createHeaders(admin.token as string);
    memberHeaders = createHeaders(member.token as string);
    friendHeaders = createHeaders(friend.token as string);
    strangerHeaders = createHeaders(stranger.token as string);
    blockedUserHeaders = createHeaders(blockedUser.token as string);
    blockerUserHeaders = createHeaders(blockerUser.token as string);

    channel = await mockChannelData(createChannelReq, users, [admin]);

    // Setup friend relationship
    await createAddAndAcceptFriends(member, [friend]);

    // Setup block relationship
    await getResponseSuccess(
      { targetUserId: blockedUser.userId },
      userSettingClient.blockUser,
      blockerUserHeaders,
    );
    await wait(2000); // Wait for block to take effect

    sendInvitationReq = {
      invitationLink: channel.invitationLink as string,
      userIds: [],
    };
  });

  const expectUserReceivesInvitationMessage = async (
    invitedUsers: V3MockedUser[],
    sender: V3MockedUser,
    shouldReceiveMessage = true,
  ): Promise<void> => {
    await wait(3000); // Wait for message to be delivered

    for (const user of invitedUsers) {
      const lastMsg = await getLastMsgInBot(user);

      if (shouldReceiveMessage) {
        expect(lastMsg?.userId).toEqual(ZIICHAT_BOT_USERID);
        await expectInviteMessageInBot(
          [user],
          channel.invitationLink as string,
          sender,
        );
      } else {
        // For blocked users, they should not receive invitation message
        // The last message should be the welcome message
        expect(lastMsg?.content).toBe(WELCOME_MESSAGE);
      }
    }
  };

  describe('Success logic', () => {
    describe('Send invitation to different user types', () => {
      it('should send invitation to friend successfully', async () => {
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [friend.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          memberHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([friend], member);
      });

      it('should send invitation to stranger successfully', async () => {
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [stranger.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          ownerHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([stranger], owner);
      });

      it('should send invitation to existing channel member successfully', async () => {
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [admin.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          ownerHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([admin], owner);
      });

      it('should send invitation to multiple users successfully', async () => {
        const [user1, user2, user3] = await mockUsersData(prefix + '_multi', 3);
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [
            user1.userId as string,
            user2.userId as string,
            user3.userId as string,
          ],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          adminHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([user1, user2, user3], admin);
      });
    });

    describe('Roles', () => {
      it('should allow owner to send invitation', async () => {
        const [testUser] = await mockUsersData(prefix + '_owner_test', 1);
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [testUser.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          ownerHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([testUser], owner);
      });

      it('should allow admin to send invitation', async () => {
        const [testUser] = await mockUsersData(prefix + '_admin_test', 1);
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [testUser.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          adminHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([testUser], admin);
      });

      it('should allow member to send invitation', async () => {
        const [testUser] = await mockUsersData(prefix + '_member_test', 1);
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [testUser.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          memberHeaders,
        );

        expect(response.ok).toBe(true);
        await expectUserReceivesInvitationMessage([testUser], member);
      });
    });

    describe('Block/Unblock scenarios', () => {
      it('should send invitation to blocked user but user does not receive message', async () => {
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [blockedUser.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          blockerUserHeaders,
        );

        expect(response.ok).toBe(true);
        // Blocked user should not receive the invitation message
        await expectUserReceivesInvitationMessage([blockedUser], blockerUser, false);
      });

      it('should send invitation from blocked user but blocker does not receive message', async () => {
        const request: V3SendInvitationRequest = {
          ...sendInvitationReq,
          userIds: [blockerUser.userId as string],
        };

        const response = await getResponseSuccess(
          request,
          invitationClient.sendInvitation,
          blockedUserHeaders,
        );

        expect(response.ok).toBe(true);
        // Blocker should not receive the invitation message
        await expectUserReceivesInvitationMessage([blockerUser], blockedUser, false);
      });
    });
  });

  describe('Error cases', () => {
    it('should return error when non-member tries to send invitation', async () => {
      const request: V3SendInvitationRequest = {
        ...sendInvitationReq,
        userIds: [friend.userId as string],
      };

      await expectForParamOnGateway(
        request,
        invitationClient.sendInvitation,
        strangerHeaders,
        UNAUTHORIZED_REQUEST,
      );
    });

    it('should return error when invitation link is invalid', async () => {
      const request: V3SendInvitationRequest = {
        invitationLink: 'https://zii.chat/i/invalid_code',
        userIds: [friend.userId as string],
      };

      await expectForParamInvalid(
        request,
        invitationClient.sendInvitation,
        ownerHeaders,
        InvitationError.SEND_INVITATION,
      );
    });

    it('should return error when userIds array is empty', async () => {
      const request: V3SendInvitationRequest = {
        ...sendInvitationReq,
        userIds: [],
      };

      await expectForParamInvalid(
        request,
        invitationClient.sendInvitation,
        ownerHeaders,
        InvitationError.SEND_INVITATION,
      );
    });

    it('should return error when userIds contains invalid user ID', async () => {
      const request: V3SendInvitationRequest = {
        ...sendInvitationReq,
        userIds: ['invalid_user_id'],
      };

      await expectForParamInvalid(
        request,
        invitationClient.sendInvitation,
        ownerHeaders,
        InvitationError.SEND_INVITATION,
      );
    });
  });
});