import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3Channel } from '../../../../../../utils/http-client/cloudevent-client';
import {
  V3CreateChannelRequest,
  V3UpdateChannelAvatarRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForUpdateChannelAvatar } from '../../../../../expected-results/commands-chat/channel';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../../expected-results/expected-errors';
import channelClient, {
  mockChannelData,
} from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import invitationClient from '../../../../../helpers/invitation-service';
import invitationViewClient from '../../../../../helpers/invitation-view-service';
import messageService from '../../../../../helpers/message-service';
import { getLastMsgInBot } from '../../../../../helpers/message-view-service';
import {
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import {
  ADMIN_PERMISSIONS,
  CONFIG_FILE_STORE_HOST,
  Methods,
  WORKSPACE_ID,
} from '../../../../const';
import {
  ChannelError,
  NULL_UNDEFINED_ERROR,
  UNAUTHORIZED_REQUEST,
  UPDATE_CHANNEL_DETAILS,
} from '../../../../error-message';

describe(Methods.UPDATE_CHANNEL_AVATAR, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_CHANNEL_AVATAR;
  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let users: V3MockedUser[];
  let channel: V3Channel;
  let updateChannelAvatarReq: V3UpdateChannelAvatarRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);

    channel = await mockChannelData(createChannelReq, users, [admin]);
    updateChannelAvatarReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
      avatarPath: `${CONFIG_FILE_STORE_HOST}${channel.channelId}/avatar.png`,
    };
  });

  describe('Business logic', () => {
    let updateAvtFirst: V3Channel;
    let updateAvtSecond: V3Channel;

    describe('Role', () => {
      it('should return true when owner update channel avatar', async () => {
        updateAvtFirst = await expectForUpdateChannelAvatar(
          updateChannelAvatarReq,
          owner,
          channel,
        );
      });

      it('should return true when admin update channel avatar', async () => {
        updateAvtSecond = await expectForUpdateChannelAvatar(
          updateChannelAvatarReq,
          admin,
          channel,
          ADMIN_PERMISSIONS,
        );

        if (updateAvtFirst) {
          expect(updateAvtFirst.avatar).not.toEqual(updateAvtSecond.avatar);
          expectGreaterThanTime(
            updateAvtFirst.updateTime as string,
            updateAvtSecond.updateTime as string,
          );
        }
      });

      it('should return false when member update channel avatar', async () => {
        await expectForParamInvalid(
          updateChannelAvatarReq,
          channelClient.updateChannelAvatar,
          createHeaders(member.token as string),
          {
            ...ChannelError.UPDATE_CHANNEL,
            details: UPDATE_CHANNEL_DETAILS.at(0),
          },
        );
      });
    });

    describe('Relative Invitation', () => {
      it('should return name, avatar correction in invitation message', async () => {
        const newName = `new ${ulid()}`;
        const newChannel = await mockChannelData(createChannelReq, [
          owner,
          admin,
        ]);

        const ownerHeaders = createHeaders(owner.token as string);
        const getChannelReq = {
          channelId: newChannel.channelId,
          workspaceId: newChannel.workspaceId,
        };

        // UpdateChannelName
        await getResponseSuccess(
          { ...getChannelReq, name: newName },
          channelClient.updateChannelName,
          ownerHeaders,
        );

        // UpdateChannelAvatar
        const updateChannelAvatarRes = await getResponseSuccess(
          {
            ...getChannelReq,
            avatarPath: `${CONFIG_FILE_STORE_HOST}${newChannel.channelId}/avatar.png`,
          },
          channelClient.updateChannelAvatar,
          ownerHeaders,
        );

        // expect avatar, name in invitation
        const invitation = await getResponseSuccess(
          getChannelReq,
          invitationClient.createInvitation,
          createHeaders(admin.token as string),
        );

        const getInvitationRes = await getResponseSuccess(
          { code: invitation?.data?.code },
          invitationViewClient.getInvitation,
          createHeaders(owner.token as string),
        );

        const channelInvitation = getInvitationRes.data?.channel;
        expect(channelInvitation?.name).toEqual(newName);
        expect(channelInvitation?.avatar).toEqual(
          updateChannelAvatarRes?.data?.channel?.avatar,
        );

        // sendInvitation
        await getResponseSuccess(
          {
            invitationLink: getInvitationRes.data?.invitationLink as string,
            userIds: [member.userId],
          },
          invitationClient.sendInvitation,
          ownerHeaders,
        );

        await wait();
        const inviteMsg = await getLastMsgInBot(member);
        if (!inviteMsg.embed) throw new Error(NULL_UNDEFINED_ERROR);

        const inviteDataInMsg = inviteMsg.embed[0].invitationData;
        expect(inviteDataInMsg?.channel?.name).toEqual(newName);
        expect(inviteDataInMsg?.channel?.avatar).toEqual(
          updateChannelAvatarRes?.data?.channel?.avatar,
        );
      });
    });

    describe('Error case', () => {
      it('should return false when user is not member', async () => {
        [stranger] = await mockUsersData(prefix, 1);
        await expectForParamOnGateway(
          updateChannelAvatarReq,
          channelClient.updateChannelAvatar,
          createHeaders(stranger.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      it('should return false when the user tries to update a deleted channel', async () => {
        await getResponseSuccess(
          {
            channelId: channel.channelId as string,
            workspaceId: channel.workspaceId as string,
          },
          channelClient.deleteChannel,
          createHeaders(owner.token as string),
        );

        await expectForParamOnGateway(
          updateChannelAvatarReq,
          channelClient.updateChannelAvatar,
          createHeaders(owner.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      // TODO https://github.com/halonext/ziichat-issues/issues/21830
      it.skip('should return false when user update in DM channel', async () => {
        const messageRes = await getResponseSuccess(
          { userId: admin.userId as string, content: ulid(), ref: ulid() },
          messageService.sendDmMessage,
          createHeaders(owner.token as string),
        );

        await expectForParamInvalid(
          {
            ...updateChannelAvatarReq,
            channelId: messageRes?.data?.message?.channelId,
          },
          channelClient.updateChannelAvatar,
          createHeaders(owner.token as string),
          {
            ...ChannelError.UPDATE_CHANNEL,
            details: UPDATE_CHANNEL_DETAILS.at(0),
          },
        );
      });
    });
  });
});
