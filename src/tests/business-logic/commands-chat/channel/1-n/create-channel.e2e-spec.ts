import { faker } from '@faker-js/faker';
import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../../../../utils/http-client/faker-client';
import {
  CHANNEL_NAME_CASES,
  expectForCreateChannel,
} from '../../../../../expected-results/commands-chat/channel';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import { expectInviteMessageInBot } from '../../../../../expected-results/views-message/message-view';
import channelClient from '../../../../../helpers/channel-service';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userSettingClient } from '../../../../../helpers/user-service';
import {
  CONFIG_FILE_STORE_HOST,
  Methods,
  WORKSPACE_ID,
  ZIICHAT_BOT_USERID,
} from '../../../../const';
import {
  ChannelError,
  CREATE_CHANNEL_DETAILS,
} from '../../../../error-message';

describe(Methods.CREATE_CHANNEL, () => {
  const prefix = getPrefixMockUser() + Methods.CREATE_CHANNEL;
  let userDefault: V3MockedUser;

  let userBlue: V3MockedUser;
  let userGray: V3MockedUser;
  let userYellow: V3MockedUser;

  let users: V3MockedUser[];

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    [userDefault] = await mockUsersData(prefix, 1);
    [userBlue] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_BLUE,
    );
    [userGray] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_GRAY,
    );
    [userYellow] = await mockUsersData(
      prefix,
      1,
      V3UserBadgeTypeEnum.USER_BADGE_TYPE_YELLOW,
    );

    users = [userDefault, userBlue, userGray, userYellow];
  });

  describe('Business logic', () => {
    describe('ChannelType', () => {
      it('should return false when channelType is DM (all userBadgeType)', async () => {
        const request = {
          ...createChannelReq,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM,
        };

        const errors = {
          ...ChannelError.CREATE_CHANNEL,
          details: CREATE_CHANNEL_DETAILS.at(0),
        };

        for (const user of users) {
          await expectForParamInvalid(
            request,
            channelClient.createChannel,
            createHeaders(user.token as string),
            errors,
          );
        }
      });

      it('should return true when channelType is Channel (all userBadgeType)', async () => {
        const request = {
          ...createChannelReq,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL,
        };

        for (const user of users) {
          const res = await getResponseSuccess(
            request,
            channelClient.createChannel,
            createHeaders(user.token as string),
          );
          expect(res.data?.channel?.type).toEqual(
            V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL,
          );
        }
      });

      it('should return true when channelType is Broadcast (except userDefault)', async () => {
        const request = {
          ...createChannelReq,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
        };

        for (const user of users.splice(1)) {
          const res = await getResponseSuccess(
            request,
            channelClient.createChannel,
            createHeaders(user.token as string),
          );
          expect(res.data?.channel?.type).toEqual(
            V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
          );
        }

        const errors = {
          ...ChannelError.CREATE_CHANNEL,
          details: CREATE_CHANNEL_DETAILS.at(1),
        };

        await expectForParamInvalid(
          request,
          channelClient.createChannel,
          createHeaders(userDefault.token as string),
          errors,
        );
      });
    });

    describe('Format name', () => {
      for (const value of CHANNEL_NAME_CASES) {
        it(`should return true when name is ${value.title} (1-n)`, async () => {
          const request: V3CreateChannelRequest = {
            workspaceId: WORKSPACE_ID,
            name: value.name,
          };

          await expectForCreateChannel(request, userDefault);
        });

        it(`should return true when name is ${value.title} (1-n, has channelType)`, async () => {
          const request: V3CreateChannelRequest = {
            workspaceId: WORKSPACE_ID,
            name: value.name,
            channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL,
          };

          await expectForCreateChannel(request, userDefault);
        });

        it(`should return true when name is ${value.title} (Broadcast)`, async () => {
          const request: V3CreateChannelRequest = {
            workspaceId: WORKSPACE_ID,
            name: value.name,
            channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
          };

          await expectForCreateChannel(request, userBlue);
        });
      }
    });

    describe('Has avatarPath', () => {
      it('should return avatar when params has avatarPath', async () => {
        const request: V3CreateChannelRequest = {
          workspaceId: WORKSPACE_ID,
          name: faker.string.alpha(17),
          avatarPath: `${CONFIG_FILE_STORE_HOST}avatar.png`,
        };
        await expectForCreateChannel(request, userDefault);
      });

      it('should return avatar when params has avatarPath', async () => {
        const request: V3CreateChannelRequest = {
          workspaceId: WORKSPACE_ID,
          name: faker.string.alpha(17),
          avatarPath: `${CONFIG_FILE_STORE_HOST}avatar.png`,
          channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST,
        };
        await expectForCreateChannel(request, userGray);
      });
    });

    describe('Has UserIds', () => {
      let stranger: V3MockedUser,
        friend: V3MockedUser,
        blockedUser: V3MockedUser;
      beforeAll(async () => {
        [stranger, friend, blockedUser] = await mockUsersData(prefix, 3);

        await createAddAndAcceptFriends(userDefault, [friend]);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          createHeaders(userDefault.token as string),
        );
      });

      it('should return true and userIds receive invitationLink message', async () => {
        const userIds = [
          stranger.userId,
          friend.userId,
          blockedUser.userId,
          ZIICHAT_BOT_USERID,
        ];
        const request: V3CreateChannelRequest = {
          workspaceId: WORKSPACE_ID,
          name: faker.string.alpha(17),
          userIds,
        };

        const channel = await expectForCreateChannel(request, userDefault);

        await expectInviteMessageInBot(
          [stranger, friend],
          channel.invitationLink as string,
          userDefault,
        );
        await expectInviteMessageInBot(
          [blockedUser],
          channel.invitationLink as string,
          userDefault,
          false,
        );
      });
    });
  });
});
