import { faker } from '@faker-js/faker';
import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Channel,
  V3CreateChannelRequest,
  V3UpdateChannelNameRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import {
  CHANNEL_NAME_CASES,
  expectForUpdateChannelName,
} from '../../../../../expected-results/commands-chat/channel';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../../expected-results/expected-errors';
import channelClient, {
  mockChannelData,
} from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
  randomEmojis,
} from '../../../../../helpers/shared/common';
import {
  ADMIN_PERMISSIONS,
  MaxLength,
  Methods,
  WORKSPACE_ID,
} from '../../../../const';
import {
  ChannelError,
  UNAUTHORIZED_REQUEST,
  UPDATE_CHANNEL_DETAILS,
} from '../../../../error-message';

describe(Methods.UPDATE_CHANNEL_NAME, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_CHANNEL_NAME;
  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let users: V3MockedUser[];
  let channel: V3Channel;
  let updateChannelNameReq: V3UpdateChannelNameRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);

    channel = await mockChannelData(createChannelReq, users, [admin]);
    updateChannelNameReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
      name: '',
    };
  });

  describe('Business logic', () => {
    describe('Role', () => {
      it('should return true when owner update channel name', async () => {
        await expectForUpdateChannelName(
          {
            ...updateChannelNameReq,
            name: faker.string.alpha(MaxLength.CHANNEL_NAME - 1),
          },
          owner,
          channel,
        );
      });

      it('should return true when admin update channel name', async () => {
        await expectForUpdateChannelName(
          {
            ...updateChannelNameReq,
            name: randomEmojis(MaxLength.CHANNEL_NAME - 1),
          },
          admin,
          channel,
          ADMIN_PERMISSIONS,
        );
      });

      it('should return false when member update channel name', async () => {
        await expectForParamInvalid(
          {
            ...updateChannelNameReq,
            name: randomEmojis(MaxLength.CHANNEL_NAME),
          },
          channelClient.updateChannelName,
          createHeaders(member.token as string),
          {
            ...ChannelError.UPDATE_CHANNEL,
            details: UPDATE_CHANNEL_DETAILS.at(0),
          },
        );
      });
    });

    describe('Format name', () => {
      for (const value of CHANNEL_NAME_CASES) {
        it(`should return true when name is ${value.title} (1-n)`, async () => {
          await expectForUpdateChannelName(
            { ...updateChannelNameReq, name: value.name },
            owner,
            channel,
          );
        });
      }
    });

    describe('Error case', () => {
      it('should return false when user is not member', async () => {
        [stranger] = await mockUsersData(prefix, 1);
        await expectForParamOnGateway(
          { ...updateChannelNameReq, name: ulid() },
          channelClient.updateChannelName,
          createHeaders(stranger.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      it('should return false when user updates with the same name', async () => {
        const name = `${ulid()} ${randomEmojis(1)}`;
        const request = { ...updateChannelNameReq, name: name };

        await getResponseSuccess(
          request,
          channelClient.updateChannelName,
          createHeaders(owner.token as string),
        );

        await expectForParamInvalid(
          request,
          channelClient.updateChannelName,
          createHeaders(admin.token as string),
          {
            ...ChannelError.UPDATE_CHANNEL,
            details: UPDATE_CHANNEL_DETAILS.at(1),
          },
        );
      });

      it('should return false when the user tries to update a deleted channel', async () => {
        const name = `${ulid()} ${randomEmojis(1)}`;
        const request = { ...updateChannelNameReq, name: name };

        await getResponseSuccess(
          {
            channelId: channel.channelId as string,
            workspaceId: channel.workspaceId as string,
          },
          channelClient.deleteChannel,
          createHeaders(owner.token as string),
        );

        await expectForParamOnGateway(
          request,
          channelClient.updateChannelName,
          createHeaders(owner.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      // TODO https://github.com/halonext/ziichat-issues/issues/21830
      it.skip('should return false when user update in DM channel', async () => {
        const messageRes = await getResponseSuccess(
          { userId: admin.userId as string, content: ulid(), ref: ulid() },
          messageService.sendDmMessage,
          createHeaders(owner.token as string),
        );

        await expectForParamInvalid(
          {
            ...updateChannelNameReq,
            channelId: messageRes?.data?.message?.channelId as string,
          },
          channelClient.updateChannelName,
          createHeaders(owner.token as string),
          {
            ...ChannelError.UPDATE_CHANNEL,
            details: UPDATE_CHANNEL_DETAILS.at(0),
          },
        );
      });
    });
  });
});
