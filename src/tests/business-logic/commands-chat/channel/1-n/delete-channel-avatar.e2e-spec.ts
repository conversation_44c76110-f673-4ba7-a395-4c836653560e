import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3Channel } from '../../../../../../utils/http-client/cloudevent-client';
import {
  V3CreateChannelRequest,
  V3DeleteChannelAvatarRequest,
  V3UpdateChannelAvatarRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForDeleteChannelAvatar } from '../../../../../expected-results/commands-chat/channel';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../../expected-results/expected-errors';
import channelClient, {
  mockChannelData,
} from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import invitationClient from '../../../../../helpers/invitation-service';
import invitationViewClient from '../../../../../helpers/invitation-view-service';
import messageService from '../../../../../helpers/message-service';
import { getLastMsgInBot } from '../../../../../helpers/message-view-service';
import {
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
  wait,
} from '../../../../../helpers/shared/common';
import {
  ADMIN_PERMISSIONS,
  CONFIG_FILE_STORE_HOST,
  Methods,
  WORKSPACE_ID,
} from '../../../../const';
import {
  ChannelError,
  DELETE_CHANNEL_AVATAR_DETAILS,
  NULL_UNDEFINED_ERROR,
  UNAUTHORIZED_REQUEST,
} from '../../../../error-message';

describe(Methods.DELETE_CHANNEL_AVATAR, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_CHANNEL_AVATAR;
  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let users: V3MockedUser[];
  let channel: V3Channel;
  let updateChannelAvatarReq: V3UpdateChannelAvatarRequest;
  let deleteChannelAvatarReq: V3DeleteChannelAvatarRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);

    channel = await mockChannelData(createChannelReq, users, [admin]);
    updateChannelAvatarReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
      avatarPath: `${CONFIG_FILE_STORE_HOST}${channel.channelId}/${ulid()}.png`,
    };

    deleteChannelAvatarReq = {
      channelId: channel.channelId as string,
      workspaceId: channel.workspaceId as string,
    };
  });

  describe('Success case', () => {
    beforeEach(async () => {
      await getResponseSuccess(
        updateChannelAvatarReq,
        channelClient.updateChannelAvatar,
        createHeaders(owner.token as string),
      );
    });

    describe('Role', () => {
      let deleteAvtFirst: V3Channel;
      let deleteAvtSecond: V3Channel;

      it('should return true when owner delete channel avatar', async () => {
        deleteAvtFirst = await expectForDeleteChannelAvatar(
          deleteChannelAvatarReq,
          owner,
          channel,
        );
      });

      // TODO https://github.com/halonext/ziichat-issues/issues/21062
      it.skip('should return true when admin delete channel avatar', async () => {
        deleteAvtSecond = await expectForDeleteChannelAvatar(
          deleteChannelAvatarReq,
          admin,
          channel,
          ADMIN_PERMISSIONS,
        );

        if (deleteAvtFirst) {
          expect(deleteAvtFirst.avatar).not.toEqual(deleteAvtFirst.avatar);
          expectGreaterThanTime(
            deleteAvtSecond.updateTime as string,
            deleteAvtSecond.updateTime as string,
          );
        }
      });

      it('should return false when member delete channel avatar', async () => {
        await expectForParamOnGateway(
          deleteChannelAvatarReq,
          channelClient.deleteChannelAvatar,
          createHeaders(member.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });
    });

    describe('Relative Invitation', () => {
      it('should return name, avatar correction in invitation message', async () => {
        const updateChannelAvatarRes = await getResponseSuccess(
          deleteChannelAvatarReq,
          channelClient.deleteChannelAvatar,
          createHeaders(owner.token as string),
        );
        expect(updateChannelAvatarRes?.data?.channel?.avatar).toEqual('');

        // expect avatar in invitation
        const invitation = await getResponseSuccess(
          deleteChannelAvatarReq,
          invitationClient.createInvitation,
          createHeaders(admin.token as string),
        );

        const getInvitationRes = await getResponseSuccess(
          { code: invitation?.data?.code },
          invitationViewClient.getInvitation,
          createHeaders(owner.token as string),
        );

        const channelInvitation = getInvitationRes.data?.channel;
        expect(channelInvitation?.avatar).toEqual(
          updateChannelAvatarRes?.data?.channel?.avatar,
        );

        // sendInvitation
        await getResponseSuccess(
          {
            invitationLink: getInvitationRes.data?.invitationLink as string,
            userIds: [member.userId],
          },
          invitationClient.sendInvitation,
          createHeaders(admin.token as string),
        );

        await wait();
        const inviteMsg = await getLastMsgInBot(member);
        if (!inviteMsg.embed) throw new Error(NULL_UNDEFINED_ERROR);

        const inviteDataInMsg = inviteMsg.embed[0].invitationData;
        expect(inviteDataInMsg?.channel?.avatar).toEqual(
          updateChannelAvatarRes?.data?.channel?.avatar,
        );
      });
    });
  });

  describe('Error case', () => {
    it('should return false when user is not member delete avatar', async () => {
      [stranger] = await mockUsersData(prefix, 1);
      await expectForParamOnGateway(
        deleteChannelAvatarReq,
        channelClient.deleteChannelAvatar,
        createHeaders(stranger.token as string),
        UNAUTHORIZED_REQUEST,
      );
    });

    it('should return false when the user tries to delete avatar a deleted channel', async () => {
      await getResponseSuccess(
        deleteChannelAvatarReq,
        channelClient.deleteChannel,
        createHeaders(owner.token as string),
      );

      await expectForParamOnGateway(
        deleteChannelAvatarReq,
        channelClient.deleteChannelAvatar,
        createHeaders(owner.token as string),
        UNAUTHORIZED_REQUEST,
      );
    });

    // TODO https://github.com/halonext/ziichat-issues/issues/21830, CHANGE ERROR
    it('should return false when user delete avatar in DM channel', async () => {
      const messageRes = await getResponseSuccess(
        { userId: admin.userId as string, content: ulid(), ref: ulid() },
        messageService.sendDmMessage,
        createHeaders(owner.token as string),
      );

      await expectForParamInvalid(
        {
          workspaceId: WORKSPACE_ID,
          channelId: messageRes?.data?.message?.channelId,
        },
        channelClient.deleteChannelAvatar,
        createHeaders(owner.token as string),
        {
          ...ChannelError.DELETE_CHANNEL_AVATAR,
          details: DELETE_CHANNEL_AVATAR_DETAILS.at(0),
        },
      );
    });
  });
});
