import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3Channel } from '../../../../../../utils/http-client/cloudevent-client';
import {
  V3CreateChannelRequest,
  V3DeleteChannelRequest,
} from '../../../../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForParamOnGateway } from '../../../../../expected-results/expected-errors';
import { isNotExistInListChannels } from '../../../../../expected-results/views-chat/channel-view';
import channelClient, {
  mockChannelData,
} from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { Methods, WORKSPACE_ID } from '../../../../const';
import { UNAUTHORIZED_REQUEST } from '../../../../error-message';

describe(Methods.DELETE_CHANNEL, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_CHANNEL;

  let owner: V3MockedUser;
  let admin: V3MockedUser;
  let member: V3MockedUser;
  let stranger: V3MockedUser;

  let users: V3MockedUser[];
  let channel: V3Channel;
  let deleteChannelReq: V3DeleteChannelRequest;

  const createChannelReq: V3CreateChannelRequest = {
    workspaceId: WORKSPACE_ID,
    name: ulid(),
  };

  beforeAll(async () => {
    users = [owner, admin, member] = await mockUsersData(prefix, 3);
  });

  describe('Business logic', () => {
    describe('Success case', () => {
      beforeEach(async () => {
        channel = await mockChannelData(createChannelReq, users, [admin]);
        deleteChannelReq = {
          channelId: channel.channelId as string,
          workspaceId: channel.workspaceId as string,
        };
      });

      it('should return true when owner delete channel', async () => {
        await getResponseSuccess(
          deleteChannelReq,
          channelClient.deleteChannel,
          createHeaders(owner.token as string),
        );

        await isNotExistInListChannels(channel.channelId as string, users);

        // delete twice
        await expectForParamOnGateway(
          deleteChannelReq,
          channelClient.deleteChannel,
          createHeaders(admin.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });
    });

    describe('Error case', () => {
      beforeAll(async () => {
        channel = await mockChannelData(createChannelReq, users, [admin]);

        deleteChannelReq = {
          channelId: channel.channelId as string,
          workspaceId: channel.workspaceId as string,
        };
      });

      it('should return false when admin delete channel', async () => {
        await expectForParamOnGateway(
          deleteChannelReq,
          channelClient.deleteChannel,
          createHeaders(admin.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      it('should return false when member delete channel', async () => {
        await expectForParamOnGateway(
          deleteChannelReq,
          channelClient.deleteChannel,
          createHeaders(member.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      it('should return false when not member delete channel', async () => {
        [stranger] = await mockUsersData(prefix, 1);
        await expectForParamOnGateway(
          deleteChannelReq,
          channelClient.deleteChannel,
          createHeaders(stranger.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });

      it('should return false when user delete DM channel', async () => {
        const messageRes = await getResponseSuccess(
          { userId: admin.userId as string, content: ulid(), ref: ulid() },
          messageService.sendDmMessage,
          createHeaders(owner.token as string),
        );

        await expectForParamOnGateway(
          {
            channelId: messageRes?.data?.message?.channelId as string,
            workspaceId: messageRes?.data?.message?.workspaceId as string,
          },
          channelClient.deleteChannel,
          createHeaders(member.token as string),
          UNAUTHORIZED_REQUEST,
        );
      });
    });
  });
});
