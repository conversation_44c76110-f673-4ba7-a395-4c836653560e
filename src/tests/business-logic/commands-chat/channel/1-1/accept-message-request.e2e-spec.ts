import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3AttachmentTypeEnum,
  V3Message,
  V3SendDMLocationRequest,
  V3SendDmMessageMediaRequest,
  V3SendDMMessageRequest,
} from '../../../../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { V3GetDMChannelResponse } from '../../../../../../utils/http-client/views-chat-client';
import { expectForAcceptMessageRequest } from '../../../../../expected-results/commands-chat/channel';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../../expected-results/expected-errors';
import {
  expectChannelInListIncoming,
  expectChannelInListOutGoing,
  expectForGetDMChannel,
  expectForListDMChannel,
  isExistInListChannels,
} from '../../../../../expected-results/views-chat/channel-view';
import channelClient from '../../../../../helpers/channel-service';
import channelViewClient from '../../../../../helpers/channel-view-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { getUserProfile } from '../../../../../helpers/user-view-service';
import { HEADERS, Methods } from '../../../../const';
import {
  ChannelError,
  MESSAGE_REQUEST_DETAILS,
  NULL_UNDEFINED_ERROR,
  UNAUTHORIZED_REQUEST,
} from '../../../../error-message';

describe(Methods.ACCEPT_MESSAGE_REQUEST, () => {
  const prefix = getPrefixMockUser() + Methods.ACCEPT_MESSAGE_REQUEST;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  beforeEach(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  });

  describe('Success case', () => {
    it('should return true when user accept message request (text)', async () => {
      const sendDMMsgReq: V3SendDMMessageRequest = {
        userId: receiver.userId as string,
        content: ulid(),
        ref: ulid(),
      };

      const msgRes = await getResponseSuccess(
        sendDMMsgReq,
        messageService.sendDmMessage,
        senderHeaders,
      );

      const msgSend = msgRes?.data?.message as V3Message;

      const acceptRes = await expectForAcceptMessageRequest(
        receiver,
        sender,
        msgSend,
      );

      // expect for user accept
      await expectForGetDMChannel(
        { userId: sender.userId as string },
        acceptRes as V3GetDMChannelResponse,
        receiver,
      );

      await expectForListDMChannel(
        acceptRes as V3GetDMChannelResponse,
        receiver,
      );

      // expect for user accept
      const receiverProfile = await getUserProfile(receiver);
      if (
        !acceptRes.data ||
        !acceptRes.data.channel ||
        !acceptRes.data.channel.name
      )
        throw new Error(NULL_UNDEFINED_ERROR);

      acceptRes.data.channel.name = receiverProfile.profile?.displayName;
      acceptRes.data.channel.avatar = receiverProfile.profile?.avatar;
      acceptRes.data.channel.originalAvatar =
        receiverProfile.profile?.originalAvatar;

      if (!acceptRes.includes || !acceptRes.includes.channelMetadata)
        throw new Error(NULL_UNDEFINED_ERROR);
      acceptRes.includes.channelMetadata[0].unreadCount = 0;

      await expectForGetDMChannel(
        { userId: receiver.userId as string },
        acceptRes as V3GetDMChannelResponse,
        sender,
      );

      await expectForListDMChannel(acceptRes as V3GetDMChannelResponse, sender);

      // Accept twice
      await expectForParamInvalid(
        { userId: sender.userId },
        channelClient.acceptMessageRequest,
        createHeaders(receiver.token as string),
        {
          ...ChannelError.ACCEPT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );
    });

    it('should return true when user reply message request (location)', async () => {
      const sendDMMsgReq: V3SendDMLocationRequest = {
        userId: receiver.userId as string,
        ref: ulid(),
        latitude: '1',
        longitude: '2',
      };

      const msgRes = await getResponseSuccess(
        sendDMMsgReq,
        messageService.sendDmLocation,
        senderHeaders,
      );

      const channelId = msgRes?.data?.message?.channelId as string;
      await getResponseSuccess(
        { ...sendDMMsgReq, userId: sender.userId },
        messageService.sendDmLocation,
        receiverHeaders,
      );

      await expectChannelInListIncoming(channelId, receiver, false);
      await expectChannelInListOutGoing(channelId, receiver, false);

      await expectChannelInListIncoming(channelId, sender, false);
      await expectChannelInListOutGoing(channelId, sender, false);

      await isExistInListChannels(
        channelId as string,
        [sender, receiver],
        [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
      );
    });

    // TODO https://github.com/halonext/ziichat-issues/issues/21945
    it.skip('should return true when user accept message request (media)', async () => {
      const sendDMMsgReq: V3SendDmMessageMediaRequest = {
        userId: receiver.userId as string,
        attachmentType: V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_PHOTO,
        ref: ulid(),
        mediaObjects: [
          {
            attachmentType: V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_PHOTO,
            fileUrl:
              'https://www.youtube.com/watch?v=pS7qRsQqV0g&list=RDpS7qRsQqV0g&start_radio=1',
            fileMetadata: {
              mimetype: 'text/plain',
              filename: 'ab.txt',
              extension: 'txt',
            },
          },
        ],
      };

      const msgRes = await getResponseSuccess(
        sendDMMsgReq,
        messageService.sendDmMessageMedia,
        senderHeaders,
      );

      const msgSend = msgRes?.data?.message as V3Message;
      await expectForAcceptMessageRequest(receiver, sender, msgSend);
    });
  });

  describe('Error cases', () => {
    it('should return false when user accept message request himself', async () => {
      await expectForParamOnGateway(
        { userId: sender.userId },
        channelClient.acceptMessageRequest,
        senderHeaders,
        UNAUTHORIZED_REQUEST,
      );
    });

    it('should return false when user accept message request not exist', async () => {
      await expectForParamInvalid(
        { userId: receiver.userId },
        channelClient.acceptMessageRequest,
        senderHeaders,
        {
          ...ChannelError.ACCEPT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );
    });

    it('should return false when user accept message request (reject before)', async () => {
      const sendDMMsgReq: V3SendDMMessageRequest = {
        userId: receiver.userId as string,
        content: ulid(),
        ref: ulid(),
      };

      await getResponseSuccess(
        sendDMMsgReq,
        messageService.sendDmMessage,
        senderHeaders,
      );

      // Reject
      await getResponseSuccess(
        { userId: sender.userId as string },
        channelClient.rejectMessageRequest,
        receiverHeaders,
      );

      // Accept
      await expectForParamInvalid(
        { userId: sender.userId },
        channelClient.acceptMessageRequest,
        createHeaders(receiver.token as string),
        {
          ...ChannelError.ACCEPT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(1),
        },
      );
    });
  });
});
