import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import {
  V3Message,
  V3SendDMMessageRequest,
} from '../../../../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForRejectMessageRequest } from '../../../../../expected-results/commands-chat/channel';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../../expected-results/expected-errors';
import {
  expectChannelInListIncoming,
  expectChannelInListOutGoing,
} from '../../../../../expected-results/views-chat/channel-view';
import channelClient from '../../../../../helpers/channel-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import messageService from '../../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { HEADERS, Methods } from '../../../../const';
import {
  ChannelError,
  MESSAGE_REQUEST_DETAILS,
  UNAUTHORIZED_REQUEST,
} from '../../../../error-message';

describe(Methods.REJECT_MESSAGE_REQUEST, () => {
  const prefix = getPrefixMockUser() + Methods.REJECT_MESSAGE_REQUEST;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;
  let otherUser: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  let msgSend: V3Message;
  let sendDMMsgReq: V3SendDMMessageRequest;

  beforeEach(async () => {
    [sender, receiver, otherUser] = await mockUsersData(prefix, 3);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
    sendDMMsgReq = {
      userId: receiver.userId as string,
      content: ulid(),
      ref: ulid(),
    };

    const msgRes = await getResponseSuccess(
      sendDMMsgReq,
      messageService.sendDmMessage,
      senderHeaders,
    );

    msgSend = msgRes?.data?.message as V3Message;
  });

  describe('Success case', () => {
    it('should return rejectTime when user reject message request', async () => {
      await expectForRejectMessageRequest(receiver, sender, msgSend);

      // Twice
      await expectForParamInvalid(
        { userId: sender.userId },
        channelClient.rejectMessageRequest,
        receiverHeaders,
        {
          ...ChannelError.REJECT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );
    });

    it('should return DM in message request when re-send message', async () => {
      await expectForRejectMessageRequest(receiver, sender, msgSend);

      // send message
      const msgRes = await getResponseSuccess(
        sendDMMsgReq,
        messageService.sendDmMessage,
        senderHeaders,
      );

      msgSend = msgRes?.data?.message as V3Message;
      const { channelId } = msgSend;
      await expectChannelInListIncoming(channelId as string, receiver);
      await expectChannelInListOutGoing(channelId as string, receiver, false);

      await expectChannelInListIncoming(channelId as string, sender, false);
      await expectChannelInListOutGoing(channelId as string, sender);

      // Reject twice
      await expectForRejectMessageRequest(receiver, sender, msgSend);
    });
  });

  describe('Error cases', () => {
    it('should return false when user reject himself', async () => {
      await expectForParamOnGateway(
        { userId: receiver.userId },
        channelClient.rejectMessageRequest,
        receiverHeaders,
        UNAUTHORIZED_REQUEST,
      );
    });

    // TODO https://github.com/halonext/ziichat-issues/issues/21988
    it.skip('should return false when user reject not exist', async () => {
      await expectForParamInvalid(
        { userId: otherUser.userId },
        channelClient.rejectMessageRequest,
        senderHeaders,
        {
          ...ChannelError.REJECT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        channelClient.rejectMessageRequest,
        senderHeaders,
        {
          ...ChannelError.REJECT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );
    });

    it('should return false when user reject after accept', async () => {
      await getResponseSuccess(
        { userId: sender.userId },
        channelClient.acceptMessageRequest,
        receiverHeaders,
      );

      await expectForParamInvalid(
        { userId: sender.userId },
        channelClient.rejectMessageRequest,
        receiverHeaders,
        {
          ...ChannelError.REJECT_MESSAGE_REQUEST,
          details: MESSAGE_REQUEST_DETAILS.at(0),
        },
      );
    });
  });
});
