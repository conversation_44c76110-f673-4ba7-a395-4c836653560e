import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3DirectMessageStatusEnum,
  V3FriendStatusEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectAddFriendResponse } from '../../../../expected-results/commands-chat/friend';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../expected-results/expected-errors';
import { expectDMStatus } from '../../../../expected-results/views-chat/channel-view';
import {
  expectEmptyListFriendRequests,
  expectGetFriendResponse,
  isExistInListInComingFriendRequests,
  isExistInListOutGoingFriendRequests,
} from '../../../../expected-results/views-chat/friend-view';
import { expectFriendStatus } from '../../../../expected-results/views-chat/user-view';
import friendClient from '../../../../helpers/friend-service';
import friendViewService from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import {
  HEADERS,
  Methods,
  ZIICHAT_BOT_USERID,
  ZIICHAT_GHOST_USERID,
} from '../../../const';
import {
  ADD_FRIEND_DETAILS,
  FriendError,
  UNAUTHORIZED_REQUEST,
} from '../../../error-message';

describe(Methods.ADD_FRIEND, () => {
  const prefix = getPrefixMockUser() + Methods.ADD_FRIEND;

  let receiverHeaders: HEADERS;
  let senderHeaders: HEADERS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let expectedData;

  const expectedError = {
    details: [FriendError.ADD_NEW_FRIEND.message],
    ...FriendError.ADD_NEW_FRIEND,
  };

  const prepareData = async (): Promise<void> => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    expectedData = {
      requestedFromUserId: sender.userId,
      requestedToUserId: receiver.userId,
      status: V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_SENT,
      participantIds: [sender.userId, receiver.userId],
    };
  };

  describe('Business Logic', () => {
    describe('Send friend request', () => {
      beforeAll(async () => {
        await prepareData();

        await expectAddFriendResponse(sender, receiver, expectedData);
      });
      it('should sender receive REQUEST_SENT, receiver receive REQUEST_RECEIVED when getUser, getUserByUserName', async () => {
        await expectFriendStatus(
          receiver,
          sender,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_SENT,
        );

        await expectFriendStatus(
          sender,
          receiver,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_RECEIVED,
        );
      });

      it('should return true when sender saw new friend request in ListOutGoingFriendRequests', async () => {
        await isExistInListOutGoingFriendRequests(
          sender,
          receiver,
          senderHeaders,
        );
        await expectEmptyListFriendRequests(
          senderHeaders,
          friendViewService.listInComingFriendRequests,
        );
      });

      it('should return true when receiver saw new friend request in ListInComingFriendRequests', async () => {
        await isExistInListInComingFriendRequests(
          sender,
          receiver,
          receiverHeaders,
        );

        await expectEmptyListFriendRequests(
          receiverHeaders,
          friendViewService.listOutGoingFriendRequests,
        );
      });

      it('should return correct data when GetFriend', async () => {
        const expectedDataReceiver = {
          ...expectedData,
          status: V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_RECEIVED,
        };
        await expectGetFriendResponse(sender, receiver, expectedData);
        await expectGetFriendResponse(receiver, sender, expectedDataReceiver);
      });
    });
    describe('Re-sending friend request', () => {
      beforeEach(async () => {
        await prepareData();

        await getResponseSuccess(
          { userId: receiver.userId },
          friendClient.addFriend,
          senderHeaders,
        );
      });
      it('should return true when add friend after cancelFriendRequest', async () => {
        await getResponseSuccess(
          { userId: receiver.userId },
          friendClient.cancelFriendRequest,
          senderHeaders,
        );

        await getResponseSuccess(
          { userId: sender.userId },
          friendClient.addFriend,
          receiverHeaders,
        );

        await getResponseSuccess(
          { userId: sender.userId },
          friendClient.cancelFriendRequest,
          receiverHeaders,
        );
      });

      it('should return true when add friend after unfriend', async () => {
        await getResponseSuccess(
          { userId: sender.userId },
          friendClient.acceptFriendRequest,
          receiverHeaders,
        );

        await getResponseSuccess(
          { userId: sender.userId },
          friendClient.unfriend,
          receiverHeaders,
        );

        await getResponseSuccess(
          { userId: sender.userId },
          friendClient.addFriend,
          receiverHeaders,
        );
      });
    });

    describe('Remain dmStatus', () => {
      beforeEach(async () => {
        await prepareData();
      });

      it('Should return dmStatus is PENDING on both user', async () => {
        await mockDmMessageData(senderHeaders, receiver, 'hello');

        await expectAddFriendResponse(sender, receiver, expectedData);

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );
      });

      it('Should return dmStatus is CONTACTED on both user', async () => {
        await mockDmMessageData(senderHeaders, receiver, 'hello');
        await mockDmMessageData(receiverHeaders, sender, 'hello');

        await expectAddFriendResponse(sender, receiver, expectedData);

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );
      });
    });
  });

  describe('Error Logic', () => {
    beforeEach(async () => {
      await prepareData();
    });
    it('should return error when add friend after deleteFriendRequest', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: sender.userId },
        friendClient.deleteFriendRequest,
        receiverHeaders,
      );

      await expectForParamInvalid(
        { userId: sender.userId },
        friendClient.addFriend,
        receiverHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when add friend twice', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when add friend after being friend', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: sender.userId },
        friendClient.acceptFriendRequest,
        receiverHeaders,
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.addFriend,
        senderHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        { userId: sender.userId },
        friendClient.addFriend,
        receiverHeaders,
        expectedError,
      );
    });

    it('should return error when add friend user bot', async () => {
      await expectForParamInvalid(
        { userId: ZIICHAT_BOT_USERID },
        friendClient.addFriend,
        receiverHeaders,
        expectedError,
      );
    });

    it('should return error when add friend user ghost', async () => {
      await expectForParamInvalid(
        { userId: ZIICHAT_GHOST_USERID },
        friendClient.addFriend,
        receiverHeaders,
        {
          ...FriendError.ADD_NEW_FRIEND,
          details: ADD_FRIEND_DETAILS[0],
        },
      );
    });

    it('should return error when add friend myself', async () => {
      await expectForParamOnGateway(
        { userId: receiver.userId },
        friendClient.addFriend,
        receiverHeaders,
        UNAUTHORIZED_REQUEST,
      );
    });
  });
});
