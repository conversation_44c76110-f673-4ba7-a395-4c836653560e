import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3DirectMessageStatusEnum,
  V3FriendStatusEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import {
  expectDMStatus,
  isExistInListChannels,
  isNotExistInListChannels,
} from '../../../../expected-results/views-chat/channel-view';
import {
  expectEmptyListFriendRequests,
  expectGetFriendResponse,
  isExistInListOutGoingFriendRequests,
  isNotExistInListFriend,
} from '../../../../expected-results/views-chat/friend-view';
import { expectFriendStatus } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { HEADERS, Methods } from '../../../const';
import {
  DELETE_FRIEND_REQUEST_DETAILS,
  FriendError,
} from '../../../error-message';

describe(Methods.DELETE_FRIEND_REQUEST, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_FRIEND_REQUEST;

  let receiverHeaders: HEADERS;
  let senderHeaders: HEADERS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let expectedData;

  const expectedError = {
    details: DELETE_FRIEND_REQUEST_DETAILS[0],
    ...FriendError.DELETE_FRIEND_REQUEST,
  };

  const prepareData = async (): Promise<void> => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    expectedData = {
      requestedFromUserId: sender.userId,
      requestedToUserId: receiver.userId,
      status: V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_SENT,
      participantIds: [sender.userId, receiver.userId],
    };
  };

  describe('Business Logic', () => {
    describe('After deleteFriendRequest', () => {
      beforeAll(async () => {
        await prepareData();

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: sender.userId,
          },
          friendClient.deleteFriendRequest,
          receiverHeaders,
        );
      });

      it('should return FRIEND_STATUS_ENUM_NOT_FRIEND when getUser, getUserByUserName', async () => {
        await expectFriendStatus(
          receiver,
          sender,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_SENT,
        );

        await expectFriendStatus(
          sender,
          receiver,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_DELETED,
        );
      });

      it('should keep the request in ListOutGoingFriendRequests, remove request in listInComingFriendRequests', async () => {
        await isExistInListOutGoingFriendRequests(
          sender,
          receiver,
          senderHeaders,
        );

        await expectEmptyListFriendRequests(
          receiverHeaders,
          friendViewClient.listInComingFriendRequests,
        );
      });

      it('should return correct data when GetFriend', async () => {
        await expectGetFriendResponse(sender, receiver, expectedData, true);

        expectedData.status =
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_DELETED;
        await expectGetFriendResponse(receiver, sender, expectedData, true);
      });

      it('should not display in ListFriend', async () => {
        await isNotExistInListFriend(sender, receiver, senderHeaders);
        await isNotExistInListFriend(sender, receiver, receiverHeaders);
      });
    });

    describe('Remain dmStatus', () => {
      let msgSend;
      beforeEach(async () => {
        await prepareData();

        msgSend = await mockDmMessageData(senderHeaders, receiver, 'hello');

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: sender.userId,
          },
          friendClient.deleteFriendRequest,
          receiverHeaders,
        );
      });

      it('Should remain dmStatus = PENDING and not display in ListDmChannels, ListAllChannels', async () => {
        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );

        await isNotExistInListChannels(
          msgSend.channelId as string,
          [receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });

      it('Should remain dmStatus = CONTACTED and display in ListDmChannels, ListAllChannels', async () => {
        await mockDmMessageData(receiverHeaders, sender, 'hello');

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });
    });
  });

  describe('Error Logic', () => {
    beforeEach(async () => {
      await prepareData();
    });

    it('should return error when deleteFriendRequest by sender', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.deleteFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when deleteFriendRequest twice', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          userId: sender.userId,
        },
        friendClient.deleteFriendRequest,
        receiverHeaders,
      );

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.deleteFriendRequest,
        receiverHeaders,
        expectedError,
      );
    });

    it('should return error after being friend', async () => {
      await createAddAndAcceptFriends(sender, [receiver]);

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.deleteFriendRequest,
        receiverHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.deleteFriendRequest,
        senderHeaders,
        expectedError,
      );
    });
  });
});
