import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3DirectMessageStatusEnum,
  V3FriendStatusEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import {
  expectDMStatus,
  isExistInListChannels,
  isNotExistInListChannels,
} from '../../../../expected-results/views-chat/channel-view';
import {
  expectEmptyListFriendRequests,
  isNotExistInListFriend,
} from '../../../../expected-results/views-chat/friend-view';
import { expectFriendStatus } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import friendViewService from '../../../../helpers/friend-view-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { HEADERS, Methods } from '../../../const';
import {
  CANCEL_FRIEND_REQUEST_DETAILS,
  FriendError,
  GET_FRIEND_DETAILS,
} from '../../../error-message';

describe(Methods.CANCEL_FRIEND_REQUEST, () => {
  const prefix = getPrefixMockUser() + Methods.CANCEL_FRIEND_REQUEST;

  let receiverHeaders: HEADERS;
  let senderHeaders: HEADERS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  const expectedError = {
    details: CANCEL_FRIEND_REQUEST_DETAILS[0],
    ...FriendError.CANCEL_FRIEND_REQUEST,
  };

  const prepareData = async (): Promise<void> => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  };

  describe('Business Logic', () => {
    describe('After cancelFriendRequest', () => {
      beforeAll(async () => {
        await prepareData();

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.cancelFriendRequest,
          senderHeaders,
        );
      });

      it('should return FRIEND_STATUS_ENUM_NOT_FRIEND when getUser, getUserByUserName', async () => {
        await expectFriendStatus(
          receiver,
          sender,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_NOT_FRIEND,
        );

        await expectFriendStatus(
          sender,
          receiver,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_NOT_FRIEND,
        );
      });

      it('should not display in list friend request', async () => {
        for (const method of [
          friendViewService.listOutGoingFriendRequests,
          friendViewService.listInComingFriendRequests,
        ]) {
          await expectEmptyListFriendRequests(senderHeaders, method);
          await expectEmptyListFriendRequests(receiverHeaders, method);
        }
      });
      //TODO https://github.com/halonext/ziichat-issues/issues/22847
      it('should return error when GetFriend', async () => {
        await expectForParamInvalid(
          { userId: receiver.userId },
          friendViewClient.getFriend,
          senderHeaders,
          {
            ...FriendError.GET_FRIEND,
            details: GET_FRIEND_DETAILS[0],
          },
        );
        await expectForParamInvalid(
          { userId: sender.userId },
          friendViewClient.getFriend,
          receiverHeaders,
          {
            ...FriendError.GET_FRIEND,
            details: GET_FRIEND_DETAILS[0],
          },
        );
      });

      it('should not display in ListFriend', async () => {
        await isNotExistInListFriend(sender, receiver, senderHeaders);
        await isNotExistInListFriend(sender, receiver, receiverHeaders);
      });
    });

    describe('Remain dmStatus', () => {
      let msgSend;
      beforeEach(async () => {
        await prepareData();

        msgSend = await mockDmMessageData(senderHeaders, receiver, 'hello');

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.cancelFriendRequest,
          senderHeaders,
        );
      });

      it('Should remain dmStatus = PENDING and not display in ListDmChannels, ListAllChannels', async () => {
        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
        );

        await isNotExistInListChannels(
          msgSend.channelId as string,
          [receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });

      it('Should remain dmStatus = CONTACTED and display in ListDmChannels, ListAllChannels', async () => {
        await mockDmMessageData(receiverHeaders, sender, 'hello');

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });
    });
  });

  describe('Error Logic', () => {
    beforeEach(async () => {
      await prepareData();
    });

    it('should return error when cancelFriendRequest by receiver', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.cancelFriendRequest,
        receiverHeaders,
        expectedError,
      );
    });

    it('should return error when cancelFriendRequest after deleteFriendRequest', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          userId: sender.userId,
        },
        friendClient.deleteFriendRequest,
        receiverHeaders,
      );

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.cancelFriendRequest,
        receiverHeaders,
        expectedError,
      );
    });
    //TODO https://github.com/halonext/ziichat-issues/issues/21988
    it('should return error when not sending friend request before', async () => {
      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.cancelFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when cancelFriendRequest twice', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.cancelFriendRequest,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.cancelFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when being friend', async () => {
      await createAddAndAcceptFriends(sender, [receiver]);

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.cancelFriendRequest,
        senderHeaders,
        expectedError,
      );
    });
  });
});
