import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3DirectMessageStatusEnum,
  V3FriendStatusEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import {
  expectDMStatus,
  isExistInListChannels,
} from '../../../../expected-results/views-chat/channel-view';
import { isNotExistInListFriend } from '../../../../expected-results/views-chat/friend-view';
import { expectFriendStatus } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import {
  HEADERS,
  Methods,
  ZIICHAT_BOT_USERID,
  ZIICHAT_GHOST_USERID,
} from '../../../const';
import {
  EXPECTED_VALIDATION_ERROR,
  FriendError,
  GET_FRIEND_DETAILS,
  UNFRIEND_DETAILS,
} from '../../../error-message';

describe(Methods.UNFRIEND, () => {
  const prefix = getPrefixMockUser() + Methods.UNFRIEND;

  let receiverHeaders: HEADERS;
  let senderHeaders: HEADERS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  const expectedError = {
    details: UNFRIEND_DETAILS[0],
    ...FriendError.UNFRIEND,
  };

  const prepareData = async (): Promise<void> => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  };

  describe('Business Logic', () => {
    describe('After Unfriend', () => {
      beforeAll(async () => {
        await prepareData();
        await createAddAndAcceptFriends(sender, [receiver]);

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.unfriend,
          senderHeaders,
        );
      });

      it('should return FRIEND_STATUS_ENUM_NOT_FRIEND when getUser, getUserByUserName', async () => {
        await expectFriendStatus(
          receiver,
          sender,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_NOT_FRIEND,
        );

        await expectFriendStatus(
          sender,
          receiver,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_NOT_FRIEND,
        );
      });
      //TODO https://github.com/halonext/ziichat-issues/issues/22847
      it('should return error when GetFriend', async () => {
        await expectForParamInvalid(
          { userId: receiver.userId },
          friendViewClient.getFriend,
          senderHeaders,
          {
            ...FriendError.GET_FRIEND,
            details: GET_FRIEND_DETAILS[0],
          },
        );
        await expectForParamInvalid(
          { userId: sender.userId },
          friendViewClient.getFriend,
          receiverHeaders,
          {
            ...FriendError.GET_FRIEND,
            details: GET_FRIEND_DETAILS[0],
          },
        );
      });

      it('should not display in ListFriend', async () => {
        await isNotExistInListFriend(sender, receiver, senderHeaders);
        await isNotExistInListFriend(sender, receiver, receiverHeaders);
      });
    });

    describe('Remain dmStatus', () => {
      let msgSend;
      beforeEach(async () => {
        await prepareData();

        msgSend = await mockDmMessageData(senderHeaders, receiver, 'hello');
      });

      it('Should return dmStatus = CONTACTED and display in ListDmChannels, ListAllChannels (chat from 1 side)', async () => {
        await createAddAndAcceptFriends(sender, [receiver]);

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.unfriend,
          senderHeaders,
        );

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });

      it('Should remain dmStatus = CONTACTED and display in ListDmChannels, ListAllChannels (chat from 2 side)', async () => {
        await mockDmMessageData(receiverHeaders, sender, 'hello');

        await createAddAndAcceptFriends(sender, [receiver]);

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.unfriend,
          senderHeaders,
        );

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });
    });
  });

  describe('Error Logic', () => {
    beforeAll(async () => {
      await prepareData();
    });

    it('should return false when unfriend to ziichat bot', async () => {
      await expectForParamInvalid(
        { userId: ZIICHAT_BOT_USERID },
        friendClient.unfriend,
        receiverHeaders,
        { ...EXPECTED_VALIDATION_ERROR, details: UNFRIEND_DETAILS[0] },
      );
    });

    it('should return false when unfriend to ziichat ghost ', async () => {
      await expectForParamInvalid(
        { userId: ZIICHAT_GHOST_USERID },
        friendClient.unfriend,
        senderHeaders,
        { ...EXPECTED_VALIDATION_ERROR, details: UNFRIEND_DETAILS[0] },
      );
    });
    //TODO https://github.com/halonext/ziichat-issues/issues/21988
    it('should return false when unfriend twice', async () => {
      await createAddAndAcceptFriends(sender, [receiver]);

      await getResponseSuccess(
        { userId: receiver.userId },
        friendClient.unfriend,
        senderHeaders,
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.unfriend,
        senderHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        { userId: sender.userId },
        friendClient.unfriend,
        receiverHeaders,
        expectedError,
      );
    });
  });
});
