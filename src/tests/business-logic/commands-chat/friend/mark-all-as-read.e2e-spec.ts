import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectEmptyListFriendRequests } from '../../../../expected-results/views-chat/friend-view';
import friendClient from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  assertIsDate,
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { HEADERS, Methods } from '../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../error-message';

describe(Methods.MARK_ALL_AS_READ, () => {
  const prefix = getPrefixMockUser() + Methods.MARK_ALL_AS_READ;

  let headers: HEADERS;

  let mainUser: V3MockedUser;
  let otherUsers: V3MockedUser[];

  beforeAll(async () => {
    [mainUser, ...otherUsers] = await mockUsersData(prefix, 11);
    headers = createHeaders(mainUser.token as string);

    for (const { token } of otherUsers) {
      await getResponseSuccess(
        { userId: mainUser.userId },
        friendClient.addFriend,
        createHeaders(token as string),
      );
    }
  });

  it('should return readTime when markAllAsRead', async () => {
    let response = await getResponseSuccess(
      {},
      friendViewClient.listInComingFriendRequests,
      headers,
    );
    if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

    for (const friend of response.data) {
      expect(friend.friend?.readTime).toBeUndefined();
    }

    await getResponseSuccess({}, friendClient.markAllAsRead, headers);

    response = await getResponseSuccess(
      {},
      friendViewClient.listInComingFriendRequests,
      headers,
    );
    if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

    for (const friend of response.data) {
      assertIsDate(friend.friend?.readTime as string);
    }
  });

  it('should return true when listInComingFriendRequests is empty', async () => {
    const headers = createHeaders(otherUsers[0].token as string);

    await expectEmptyListFriendRequests(
      headers,
      friendViewClient.listInComingFriendRequests,
    );

    await getResponseSuccess({}, friendClient.markAllAsRead, headers);
  });
});
