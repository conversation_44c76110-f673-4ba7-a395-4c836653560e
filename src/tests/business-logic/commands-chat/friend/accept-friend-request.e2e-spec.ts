import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3DirectMessageStatusEnum,
  V3FriendStatusEnum,
} from '../../../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectAcceptFriendResponse } from '../../../../expected-results/commands-chat/friend';
import {
  expectForParamInvalid,
  expectForParamOnGateway,
} from '../../../../expected-results/expected-errors';
import {
  expectDMStatus,
  isExistInListChannels,
} from '../../../../expected-results/views-chat/channel-view';
import {
  expectEmptyListFriendRequests,
  expectGetFriendResponse,
  isExistInListFriend,
} from '../../../../expected-results/views-chat/friend-view';
import { expectFriendStatus } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import friendViewService from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { HEADERS, Methods, ZIICHAT_GHOST_USERID } from '../../../const';
import {
  ACCEPT_FRIEND_REQUEST_DETAILS,
  FriendError,
  UNAUTHORIZED_REQUEST,
} from '../../../error-message';

describe(Methods.ACCEPT_FRIEND_REQUEST, () => {
  const prefix = getPrefixMockUser() + Methods.ACCEPT_FRIEND_REQUEST;

  let receiverHeaders: HEADERS;
  let senderHeaders: HEADERS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let expectedData;

  const expectedError = {
    details: [],
    ...FriendError.ACCEPT_FRIEND_REQUEST,
  };

  const prepareData = async (): Promise<void> => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    expectedData = {
      requestedFromUserId: sender.userId,
      requestedToUserId: receiver.userId,
      status: V3FriendStatusEnum.FRIEND_STATUS_ENUM_FRIEND,
      participantIds: [sender.userId, receiver.userId],
    };
  };

  describe('Business Logic', () => {
    describe('Accept friend request successfully', () => {
      beforeAll(async () => {
        await prepareData();

        const response = await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        expectedData.friendId = response.data?.friend?.friendId;
        await expectAcceptFriendResponse(receiver, sender, expectedData);
      });

      it('should return FRIEND_STATUS_ENUM_FRIEND when getUser, getUserByUserName', async () => {
        await expectFriendStatus(
          receiver,
          sender,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_FRIEND,
        );

        await expectFriendStatus(
          sender,
          receiver,
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_FRIEND,
        );
      });

      it('should not display in list friend request', async () => {
        for (const method of [
          friendViewService.listOutGoingFriendRequests,
          friendViewService.listInComingFriendRequests,
        ]) {
          await expectEmptyListFriendRequests(senderHeaders, method);
          await expectEmptyListFriendRequests(receiverHeaders, method);
        }
      });

      it('should return correct data when GetFriend', async () => {
        await expectGetFriendResponse(sender, receiver, expectedData);
        await expectGetFriendResponse(receiver, sender, expectedData);
      });

      it('should display in ListFriend', async () => {
        await isExistInListFriend(sender, receiver, senderHeaders);
        await isExistInListFriend(sender, receiver, receiverHeaders);
      });
    });

    describe('Update dmStatus after accept friend request', () => {
      beforeEach(async () => {
        await prepareData();
      });

      it('Should update dmStatus from PENDING to CONTACTED and move to ListDmChannels, ListAllChannels', async () => {
        const msgSend = await mockDmMessageData(
          senderHeaders,
          receiver,
          'hello',
        );

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: sender.userId,
          },
          friendClient.acceptFriendRequest,
          receiverHeaders,
        );

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });

      it('Should remain dmStatus from CONTACTED to CONTACTED and move to ListDmChannels, ListAllChannels', async () => {
        const msgSend = await mockDmMessageData(
          senderHeaders,
          receiver,
          'hello',
        );
        await mockDmMessageData(receiverHeaders, sender, 'hello');

        await getResponseSuccess(
          {
            userId: receiver.userId,
          },
          friendClient.addFriend,
          senderHeaders,
        );

        await getResponseSuccess(
          {
            userId: sender.userId,
          },
          friendClient.acceptFriendRequest,
          receiverHeaders,
        );

        await expectDMStatus(
          receiver,
          sender,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await expectDMStatus(
          sender,
          receiver,
          V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
        );

        await isExistInListChannels(
          msgSend.channelId as string,
          [sender, receiver],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );
      });
    });

    it('should return true after deleteFriendRequest', async () => {
      await prepareData();

      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          userId: sender.userId,
        },
        friendClient.deleteFriendRequest,
        receiverHeaders,
      );

      await getResponseSuccess(
        {
          userId: sender.userId,
        },
        friendClient.acceptFriendRequest,
        receiverHeaders,
      );
    });
  });
  //TODO https://github.com/halonext/ziichat-issues/issues/21988
  describe('Error Logic', () => {
    beforeEach(async () => {
      await prepareData();
    });
    it('should return error after cancelFriendRequest', async () => {
      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.addFriend,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.cancelFriendRequest,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.acceptFriendRequest,
        receiverHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.acceptFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error after unfriend', async () => {
      await createAddAndAcceptFriends(sender, [receiver]);

      await getResponseSuccess(
        {
          userId: receiver.userId,
        },
        friendClient.unfriend,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          userId: sender.userId,
        },
        friendClient.acceptFriendRequest,
        receiverHeaders,
        expectedError,
      );

      await expectForParamInvalid(
        {
          userId: receiver.userId,
        },
        friendClient.acceptFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error when accept friend request twice', async () => {
      await createAddAndAcceptFriends(sender, [receiver]);

      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.acceptFriendRequest,
        senderHeaders,
        { ...expectedError, details: ACCEPT_FRIEND_REQUEST_DETAILS[0] },
      );
    });

    it('should return error when not receiving a friend request', async () => {
      await expectForParamInvalid(
        { userId: receiver.userId },
        friendClient.acceptFriendRequest,
        senderHeaders,
        expectedError,
      );
    });

    it('should return error with userId of myself', async () => {
      await expectForParamOnGateway(
        { userId: sender.userId },
        friendClient.acceptFriendRequest,
        senderHeaders,
        UNAUTHORIZED_REQUEST,
      );
    });

    it('should return error with userId of ghost', async () => {
      await expectForParamInvalid(
        { userId: ZIICHAT_GHOST_USERID },
        friendClient.acceptFriendRequest,
        senderHeaders,
        expectedError,
      );
    });
  });
});
