import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../../utils/http-client/views-chat-client';
import { expectAddUserStatus } from '../../../../../expected-results/commands-user-data/user-profile';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import { expectStatusDataInListFriend } from '../../../../../expected-results/views-chat/friend-view';
import { expectStatusDataInGetUser } from '../../../../../expected-results/views-chat/user-view';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../../helpers/friend-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getEnumValues,
  getResponseSuccess,
  randomEmojis,
} from '../../../../../helpers/shared/common';
import {
  deleteStatus,
  userProfileClient,
} from '../../../../../helpers/user-service';
import {
  HEADERS,
  MAX_LENGTH_CONTENT_STATUS,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../../const';
import {
  ADD_USER_STATUS_DETAILS,
  EXPECTED_VALIDATION_ERROR,
  STATUS_ERRORS,
  UserError,
} from '../../../../error-message';

describe(Methods.ADD_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + Methods.ADD_USER_STATUS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  let addUserStatusReq;

  const contentValues = [
    {
      title: 'text',
      value: faker.string.alpha(10),
    },
    {
      value: faker.string.alpha(MAX_LENGTH_CONTENT_STATUS - 1),
      title: MAX_LENGTH_CONTENT_STATUS - 1 + ' characters',
    },
    {
      value: faker.string.alpha(MAX_LENGTH_CONTENT_STATUS),
      title: MAX_LENGTH_CONTENT_STATUS + ' characters',
    },
    {
      title: 'number',
      value: faker.number.int().toString(),
    },
    {
      title: 'special character',
      value: faker.string.symbol(10),
    },
    {
      title: 'contain spacing',
      value: '       ' + faker.string.symbol(10) + '       ',
    },
  ];

  const userStatusExpireTimeList = getEnumValues(
    V3UserStatusExpireAfterTimeEnum,
  );

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    addUserStatusReq = {
      content: faker.string.alpha(10),
      status: STATUS_EMOJI_LIST[0],
      expireAfterTime:
        V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
    };
  });

  describe('Success case', () => {
    describe('Friend', () => {
      beforeAll(async () => {
        await createAddAndAcceptFriends(sender, [receiver]);
      });

      afterEach(async () => {
        await deleteStatus([senderHeaders]);
      });

      it(`should return true when adding only content (${MAX_LENGTH_CONTENT_STATUS} char)`, async () => {
        const request = {
          ...addUserStatusReq,
          content: faker.string.alpha(MAX_LENGTH_CONTENT_STATUS),
        };
        delete request.status;

        const statusData = await expectAddUserStatus(request, senderHeaders);

        await expectStatusDataInListFriend(sender, receiverHeaders, statusData);

        await expectStatusDataInGetUser(sender, statusData);
      });

      it(`should return true when adding only content (${MAX_LENGTH_CONTENT_STATUS} emoji)`, async () => {
        const request = {
          ...addUserStatusReq,
          content: randomEmojis(MAX_LENGTH_CONTENT_STATUS),
        };
        delete request.status;

        const statusData = await expectAddUserStatus(request, senderHeaders);

        await expectStatusDataInListFriend(sender, receiverHeaders, statusData);

        await expectStatusDataInGetUser(sender, statusData);
      });

      for (const param of contentValues) {
        it(
          `should return true when adding only content is ` + param.title,
          async () => {
            const request = {
              ...addUserStatusReq,
              content: param.value,
            };
            delete request.status;

            const statusData = await expectAddUserStatus(
              request,
              senderHeaders,
            );

            await expectStatusDataInListFriend(
              sender,
              receiverHeaders,
              statusData,
            );

            await expectStatusDataInGetUser(sender, statusData);
          },
        );
      }

      it(`should return true when adding only status`, async () => {
        const request = {
          ...addUserStatusReq,
          status: '      ' + faker.internet.emoji().toString() + '       ',
        };
        delete request.content;

        const statusData = await expectAddUserStatus(request, senderHeaders);

        await expectStatusDataInListFriend(sender, receiverHeaders, statusData);

        await expectStatusDataInGetUser(sender, statusData);
      });

      for (const emoji of STATUS_EMOJI_LIST) {
        it(`should return true when adding status with ${emoji}`, async () => {
          const request = {
            ...addUserStatusReq,
            status: emoji,
          };
          await expectAddUserStatus(request, senderHeaders);
        });
      }

      it('should return true when adding both content and status', async () => {
        const statusData = await expectAddUserStatus(
          addUserStatusReq,
          senderHeaders,
        );

        await expectStatusDataInListFriend(sender, receiverHeaders, statusData);

        await expectStatusDataInGetUser(sender, statusData);
      });

      for (const expireAfterTime of userStatusExpireTimeList) {
        it(`should return true with expireTime is ${expireAfterTime}`, async () => {
          const request = {
            ...addUserStatusReq,
            expireAfterTime: expireAfterTime,
          };
          await expectAddUserStatus(request, senderHeaders);
        });
      }
    });

    describe('Not Friend', () => {
      beforeAll(async () => {
        await getResponseSuccess(
          { userId: receiver.userId },
          friendClient.unfriend,
          senderHeaders,
        );
      });
      afterEach(async () => {
        await deleteStatus([senderHeaders]);
      });

      it('should return statusData when getUser, getMe', async () => {
        const statusData = await expectAddUserStatus(
          addUserStatusReq,
          senderHeaders,
        );

        await expectStatusDataInGetUser(sender, statusData);
      });
    });
  });

  describe('Error case', () => {
    it('should return false when adding status twice', async () => {
      await getResponseSuccess(
        addUserStatusReq,
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      await expectForParamInvalid(
        addUserStatusReq,
        userProfileClient.addUserStatus,
        senderHeaders,
        {
          ...UserError.ADD_USER_STATUS,
          details: ADD_USER_STATUS_DETAILS[0],
        },
      );
      await deleteStatus([senderHeaders]);
    });

    it(`should return true when adding invalid status`, async () => {
      await expectForParamInvalid(
        {
          ...addUserStatusReq,
          status: faker.string.alpha(5),
        },
        userProfileClient.addUserStatus,
        receiverHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: STATUS_ERRORS,
        },
      );
    });

    it(`should return true when adding multiple status`, async () => {
      await expectForParamInvalid(
        {
          ...addUserStatusReq,
          status: '🥰🥲❤️',
        },
        userProfileClient.addUserStatus,
        receiverHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: [STATUS_ERRORS[1]],
        },
      );
    });
  });
});
