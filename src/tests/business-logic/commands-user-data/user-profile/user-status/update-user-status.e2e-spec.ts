import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../../utils/http-client/views-chat-client';
import { expectUpdateUserStatus } from '../../../../../expected-results/commands-user-data/user-profile';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import { expectStatusDataInListFriend } from '../../../../../expected-results/views-chat/friend-view';
import { expectStatusDataInGetUser } from '../../../../../expected-results/views-chat/user-view';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  deleteStatus,
  userProfileClient,
} from '../../../../../helpers/user-service';
import {
  HEADERS,
  MAX_LENGTH_CONTENT_STATUS,
  Methods,
  STATUS_EMOJI_LIST,
} from '../../../../const';
import {
  UPDATE_USER_STATUS_DETAILS,
  UserError,
} from '../../../../error-message';

describe(Methods.UPDATE_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_USER_STATUS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  let addUserStatusReq;

  let addUserStatusResponse;

  const contentValues = [
    {
      title: 'text',
      value: faker.string.alpha(10),
    },
    {
      value: faker.string.alpha(MAX_LENGTH_CONTENT_STATUS - 1),
      title: MAX_LENGTH_CONTENT_STATUS - 1 + ' characters',
    },
    {
      value: faker.string.alpha(MAX_LENGTH_CONTENT_STATUS),
      title: MAX_LENGTH_CONTENT_STATUS + ' characters',
    },
    {
      title: 'number',
      value: faker.number.int().toString(),
    },
    {
      title: 'special character',
      value: faker.string.symbol(10),
    },
    {
      title: 'contain spacing',
      value: '       ' + faker.string.symbol(10) + '       ',
    },
    { value: '', title: 'empty string' },
    { value: '     ', title: 'only space' },
  ];

  const statusValues = [
    {
      value: faker.internet.emoji(),
      title: 'random emoji',
    },
    {
      value: '     ' + faker.internet.emoji().toString() + '        ',
      title: 'contain spacing',
    },
    { value: '', title: 'empty string' },
    { value: '     ', title: 'only space' },
  ];

  const userStatusTestCases = [
    {
      title: 'AddUserStatus with only content',
      request: {
        content: faker.string.alpha(5),
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
      },
    },
    {
      title: 'AddUserStatus with only status',
      request: {
        status: faker.internet.emoji(),
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER,
      },
    },
    {
      title: 'AddUserStatus both status and content',
      request: {
        status: faker.internet.emoji(),
        content: faker.string.alpha(5),
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER4HOUR,
      },
    },
  ];

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await createAddAndAcceptFriends(sender, [receiver]);

    addUserStatusReq = {
      content: faker.string.alpha(5),
      status: faker.internet.emoji(),
      expireAfterTime:
        V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER8HOUR,
    };
  });
  describe('Success cases', () => {
    for (const cases of userStatusTestCases) {
      describe(cases.title, () => {
        beforeEach(async () => {
          addUserStatusResponse = await getResponseSuccess(
            cases.request,
            userProfileClient.addUserStatus,
            senderHeaders,
          );
        });

        afterEach(async () => {
          await deleteStatus([senderHeaders]);
        });

        for (const param of contentValues) {
          it(`should return true when only content is ${param.title}`, async () => {
            const request = { content: param.value };
            const statusData = await expectUpdateUserStatus(
              request,
              senderHeaders,
              addUserStatusResponse.data,
            );

            await expectStatusDataInListFriend(
              sender,
              receiverHeaders,
              statusData,
            );

            await expectStatusDataInGetUser(sender, statusData);
          });
        }

        for (const emoji of STATUS_EMOJI_LIST) {
          it(`should return true when update status with ${emoji}`, async () => {
            const statusData = await expectUpdateUserStatus(
              {
                status: emoji,
              },
              senderHeaders,
              addUserStatusResponse.data,
            );

            await expectStatusDataInListFriend(
              sender,
              receiverHeaders,
              statusData,
            );

            await expectStatusDataInGetUser(sender, statusData);
          });
        }

        for (const param of statusValues) {
          it(`should return true when only status is ${param.title}`, async () => {
            const request = { content: param.value };
            const statusData = await expectUpdateUserStatus(
              request,
              senderHeaders,
              addUserStatusResponse.data,
            );

            await expectStatusDataInListFriend(
              sender,
              receiverHeaders,
              statusData,
            );

            await expectStatusDataInGetUser(sender, statusData);
          });
        }

        it('should return true when update both content and status', async () => {
          const request = {
            content: faker.string.alpha(12),
            status: faker.internet.emoji(),
          };
          const statusData = await expectUpdateUserStatus(
            request,
            senderHeaders,
            addUserStatusResponse.data,
          );

          await expectStatusDataInListFriend(
            sender,
            receiverHeaders,
            statusData,
          );

          await expectStatusDataInGetUser(sender, statusData);
        });
      });
    }
  });

  describe('Error cases', () => {
    afterEach(async () => {
      await deleteStatus([senderHeaders]);
    });

    it('should return error when both content & status is the same current content & status', async () => {
      await getResponseSuccess(
        addUserStatusReq,
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      const request = { ...addUserStatusReq };
      delete request.expireAfterTime;

      await expectForParamInvalid(
        request,
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[0],
        },
      );

      await expectForParamInvalid(
        { content: addUserStatusReq.content },
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[1],
        },
      );

      await expectForParamInvalid(
        { status: addUserStatusReq.status },
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[2],
        },
      );
    });

    it('should return error when only content is the same current content', async () => {
      const request = { ...addUserStatusReq };
      delete request.status;

      await getResponseSuccess(
        request,
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      delete request.expireAfterTime;
      await expectForParamInvalid(
        request,
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[0],
        },
      );
    });

    it('should return error when only status is the same current status', async () => {
      const request = { ...addUserStatusReq };
      delete request.content;

      await getResponseSuccess(
        request,
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      delete request.expireAfterTime;
      await expectForParamInvalid(
        request,
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[0],
        },
      );
    });

    it('should return error when not add status before', async () => {
      const request = { ...addUserStatusReq };
      delete request.expireAfterTime;

      await expectForParamInvalid(
        request,
        userProfileClient.updateUserStatus,
        receiverHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[3],
        },
      );
    });

    it('should return error when update deleted status', async () => {
      await getResponseSuccess(
        addUserStatusReq,
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteUserStatus,
        senderHeaders,
      );

      const request = { ...addUserStatusReq };
      delete request.expireAfterTime;

      await expectForParamInvalid(
        request,
        userProfileClient.updateUserStatus,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_STATUS,
          details: UPDATE_USER_STATUS_DETAILS[3],
        },
      );
    });
  });
});
