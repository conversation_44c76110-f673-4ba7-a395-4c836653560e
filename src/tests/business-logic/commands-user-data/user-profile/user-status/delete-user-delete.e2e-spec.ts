import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../../utils/http-client/views-chat-client';
import { expectStatusDataInListFriend } from '../../../../../expected-results/views-chat/friend-view';
import {
  expectStatusDataInGetMe,
  expectStatusDataInGetUser,
  isNotExistInListUserStatus,
} from '../../../../../expected-results/views-chat/user-view';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  deleteStatus,
  userProfileClient,
} from '../../../../../helpers/user-service';
import { HEADERS, Methods, STATUS_EMOJI_LIST } from '../../../../const';

describe(Methods.DELETE_USER_STATUS, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_USER_STATUS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  let addUserStatusReq;

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await createAddAndAcceptFriends(sender, [receiver]);

    addUserStatusReq = {
      content: faker.string.alpha(10),
      status: STATUS_EMOJI_LIST[0],
      expireAfterTime:
        V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR,
    };
  });

  it('should return true when delete user status (not exist)', async () => {
    await deleteStatus([senderHeaders]);

    await expectStatusDataInListFriend(sender, receiverHeaders, null);

    await expectStatusDataInGetUser(sender, null);
  });

  it('should return true when delete user status (exist)', async () => {
    await getResponseSuccess(
      addUserStatusReq,
      userProfileClient.addUserStatus,
      senderHeaders,
    );

    await deleteStatus([senderHeaders]);

    await isNotExistInListUserStatus(sender.userId as string, [
      senderHeaders,
      receiverHeaders,
    ]);

    await expectStatusDataInGetMe(senderHeaders, null);

    await expectStatusDataInListFriend(sender, receiverHeaders, null);

    await expectStatusDataInGetUser(sender, null);
  });
});
