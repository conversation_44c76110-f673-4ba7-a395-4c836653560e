import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { V3Profile } from '../../../../../utils/http-client/views-chat-client';
import { expectAvatarFields } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  checkURLExist,
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import { userViewClient } from '../../../../helpers/user-view-service';
import {
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  Methods,
  PREFIX_HOST,
} from '../../../const';
import { NULL_UNDEFINED_ERROR } from '../../../error-message';

describe(Methods.UPDATE_USER_AVATAR, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_USER_AVATAR;
  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  const avatarPath =
    'https://fs.ugc.ziicdn.net/01JF97QHG30420ST0T3F3YJ270/a.jpg';
  let expectedAvatar;

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await mockDmMessageData(senderHeaders, receiver, 'Hello');
    await mockDmMessageData(receiverHeaders, sender, 'Hello');

    await createAddAndAcceptFriends(sender, [receiver]);

    const response = await getResponseSuccess(
      {
        avatarPath: avatarPath,
      },
      userProfileClient.updateUserAvatar,
      senderHeaders,
    );

    expect(response.data?.avatar).toContain(
      PREFIX_HOST + avatarPath.replace(CONFIG_FILE_STORE_HOST, '') + '?v=',
    );
    await checkURLExist(
      CONFIG_FILE_STORE_HOST,
      response.data?.avatar as string,
    );

    expectedAvatar = response.data?.avatar;
  });

  it('should return data in getMe', async () => {
    const response = await getResponseSuccess(
      {},
      userViewClient.getMe,
      senderHeaders,
    );
    expectAvatarFields(response?.data?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      response.data?.createTime as string,
      response.data?.updateTime as string,
    );
  });

  it('should return data in getUser', async () => {
    const response = await getResponseSuccess(
      { userId: sender.userId },
      userViewClient.getUser,
      receiverHeaders,
    );
    expectAvatarFields(response.data?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      response.data?.createTime as string,
      response.data?.updateTime as string,
    );
  });

  it('should return data in getUserByUsername', async () => {
    const response = await getResponseSuccess(
      { username: sender.username },
      userViewClient.getUserByUsername,
      receiverHeaders,
    );
    expectAvatarFields(response.data?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      response.data?.createTime as string,
      response.data?.updateTime as string,
    );
  });

  it('should return data in getDmChannel', async () => {
    //By receiver
    let response = await getResponseSuccess(
      { userId: sender.userId },
      channelViewClient.getDmChannel,
      receiverHeaders,
    );
    expectAvatarFields(
      response.data?.channel as V3Profile,
      expectedAvatar,
      undefined,
      false,
    );

    let includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      includesData?.createTime as string,
      includesData?.updateTime as string,
    );

    //By sender
    response = await getResponseSuccess(
      { userId: receiver.userId },
      channelViewClient.getDmChannel,
      senderHeaders,
    );

    includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      includesData?.createTime as string,
      includesData?.updateTime as string,
    );
  });

  for (const method of [
    channelViewClient.listAllChannels,
    channelViewClient.listDmChannels,
  ]) {
    it('should return data in ' + method.name, async () => {
      //By receiver
      let response = await getResponseSuccess({}, method, receiverHeaders);
      if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
      expectAvatarFields(
        response.data[0].channel as V3Profile,
        expectedAvatar,
        undefined,
        false,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );

      //By sender
      response = await getResponseSuccess({}, method, senderHeaders);
      if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );
    });
  }

  it('should return data in getFriend', async () => {
    //By receiver
    let response = await getResponseSuccess(
      { userId: sender.userId },
      friendViewClient.getFriend,
      receiverHeaders,
    );

    let includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

    //By sender
    response = await getResponseSuccess(
      { userId: receiver.userId },
      friendViewClient.getFriend,
      senderHeaders,
    );

    includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);
  });

  it('should return data in listFriends', async () => {
    //By receiver
    let response = await getResponseSuccess(
      {},
      friendViewClient.listFriends,
      receiverHeaders,
    );

    let includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);

    //TODO https://github.com/halonext/ziichat-issues/issues/21015
    //By sender
    response = await getResponseSuccess(
      {},
      friendViewClient.listFriends,
      senderHeaders,
    );

    includesData = response.includes?.users?.find(
      (user) => user.userId == sender.userId,
    );
    // expectAvatarFields(includesData?.profile as V3Profile, expectedAvatar);
  });

  it('should return data in listUserStatus', async () => {
    await getResponseSuccess(
      {
        status: '   🥺   ',
        expireAfterTime:
          V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER4HOUR,
      },
      userProfileClient.addUserStatus,
      senderHeaders,
    );

    const response = await getResponseSuccess(
      {},
      userViewClient.listUserStatus,
      receiverHeaders,
    );
    if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

    const user = response.data.find((user) => user.userId == sender.userId);
    expectAvatarFields(user?.profile as V3Profile, expectedAvatar);

    expectGreaterThanTime(
      user?.createTime as string,
      user?.updateTime as string,
    );
  });

  it('should return true when update avatar twice', async () => {
    let response = await getResponseSuccess(
      {
        avatarPath: avatarPath,
      },
      userProfileClient.updateUserAvatar,
      receiverHeaders,
    );

    expect(response.data?.avatar).toContain(
      PREFIX_HOST + avatarPath.replace(CONFIG_FILE_STORE_HOST, ''),
    );
    await checkURLExist(
      CONFIG_FILE_STORE_HOST,
      response.data?.avatar as string,
    );

    const getUserInfoFirst = await getResponseSuccess(
      { userId: receiver.userId },
      userViewClient.getUser,
      senderHeaders,
    );

    const secondAvatar =
      'https://fs.ugc.ziicdn.net/01JF97QHG30420ST0T3F3YJ270/secondAvatar.jpg';
    response = await getResponseSuccess(
      {
        avatarPath: secondAvatar,
      },
      userProfileClient.updateUserAvatar,
      receiverHeaders,
    );

    expect(response.data?.avatar).toContain(
      PREFIX_HOST + secondAvatar.replace(CONFIG_FILE_STORE_HOST, ''),
    );
    await checkURLExist(
      CONFIG_FILE_STORE_HOST,
      response.data?.avatar as string,
    );

    const getUserInfoSecond = await getResponseSuccess(
      { userId: receiver.userId },
      userViewClient.getUser,
      senderHeaders,
    );

    expect(getUserInfoSecond.data?.profile?.avatar).not.toEqual(
      getUserInfoFirst.data?.profile?.avatar,
    );
    expect(getUserInfoSecond.data?.updateTime).not.toEqual(
      getUserInfoFirst.data?.updateTime,
    );
  });

  it('should return true when updateUserVideoAvatar -> updateUserAvatar', async () => {
    await getResponseSuccess(
      {
        avatarPath:
          'https://fs.ugc.ziicdn.net/01JF97QHG30420ST0T3F3YJ270/videoAvatar.jpg',
      },
      userProfileClient.updateUserVideoAvatar,
      receiverHeaders,
    );

    const response = await getResponseSuccess(
      {
        avatarPath: avatarPath,
      },
      userProfileClient.updateUserAvatar,
      receiverHeaders,
    );

    expect(response.data?.avatar).toContain(
      PREFIX_HOST + avatarPath.replace(CONFIG_FILE_STORE_HOST, ''),
    );
    await checkURLExist(
      CONFIG_FILE_STORE_HOST,
      response.data?.avatar as string,
    );
  });
});
