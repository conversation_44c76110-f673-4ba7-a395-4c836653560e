import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import channelViewClient from '../../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import friendViewClient from '../../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../../helpers/message-service';
import {
  checkURLExist,
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import {
  getUserProfile,
  userViewClient,
} from '../../../../../helpers/user-view-service';
import {
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  Methods,
  PREFIX_HOST,
} from '../../../../const';
import {
  NULL_UNDEFINED_ERROR,
  UPDATE_COVER_PHOTO_DETAILS,
  UserError,
} from '../../../../error-message';

describe(Methods.UPDATE_COVER_PHOTO, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_COVER_PHOTO;

  let sender;
  let receiver;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  const coverPath = 'https://fs.ugc.ziicdn.net/' + ulid() + '/updateCover.jpg';

  let expectedCover;

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await createAddAndAcceptFriends(sender, [receiver]);

    await mockDmMessageData(senderHeaders, receiver, 'hello');
    await mockDmMessageData(receiverHeaders, sender, 'hello');
  });

  describe('Success cases', () => {
    let getInfoAfterAddCover;
    beforeEach(async () => {
      const addCover = await getResponseSuccess(
        { coverPath: 'https://fs.ugc.ziicdn.net/' + ulid() + '/addCover.jpg' },
        userProfileClient.addCoverPhoto,
        senderHeaders,
      );
      getInfoAfterAddCover = await getUserProfile(sender);

      const updateCover = await getResponseSuccess(
        { coverPath: coverPath },
        userProfileClient.updateCoverPhoto,
        senderHeaders,
      );

      expect(updateCover.data?.cover).not.toEqual(addCover.data?.cover);
      expect(updateCover.data?.cover).toContain(
        PREFIX_HOST + coverPath.replace(CONFIG_FILE_STORE_HOST, '') + '?v=',
      );
      await checkURLExist(
        CONFIG_FILE_STORE_HOST,
        updateCover.data?.cover as string,
      );

      expectedCover = updateCover.data?.cover;
    });
    afterEach(async () => {
      await getResponseSuccess(
        {},
        userProfileClient.deleteCoverPhoto,
        senderHeaders,
      );
    });

    it('should return data in getMe', async () => {
      const response = await getResponseSuccess(
        {},
        userViewClient.getMe,
        senderHeaders,
      );
      expect(response.data?.profile?.cover).toEqual(expectedCover);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
      expectGreaterThanTime(
        getInfoAfterAddCover.updateTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUser', async () => {
      const response = await getResponseSuccess(
        { userId: sender.userId },
        userViewClient.getUser,
        receiverHeaders,
      );
      expect(response.data?.profile?.cover).toEqual(expectedCover);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
      expectGreaterThanTime(
        getInfoAfterAddCover.updateTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUserByUsername', async () => {
      const response = await getResponseSuccess(
        { username: sender.username },
        userViewClient.getUserByUsername,
        receiverHeaders,
      );
      expect(response.data?.profile?.cover).toEqual(expectedCover);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
      expectGreaterThanTime(
        getInfoAfterAddCover.updateTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getDmChannel', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        channelViewClient.getDmChannel,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual(expectedCover);
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        channelViewClient.getDmChannel,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual(expectedCover);
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );
    });

    for (const method of [
      channelViewClient.listAllChannels,
      channelViewClient.listDmChannels,
    ]) {
      it('should return data in ' + method.name, async () => {
        //By receiver
        let response = await getResponseSuccess({}, method, receiverHeaders);

        let includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.cover).toEqual(expectedCover);
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );

        //By sender
        response = await getResponseSuccess({}, method, senderHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

        includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.cover).toEqual(expectedCover);
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );
      });
    }

    it('should return data in getFriend', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        friendViewClient.getFriend,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual(expectedCover);

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        friendViewClient.getFriend,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual(expectedCover);
    });

    it('should return data in listFriends', async () => {
      //By receiver
      let response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual(expectedCover);

      //TODO https://github.com/halonext/ziichat-issues/issues/21015
      //By sender
      response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      // expect(includesData?.profile?.cover).toEqual(expectedCover);
    });
  });

  describe('Error case', () => {
    it('should return error when not have cover photo before', async function () {
      await expectForParamInvalid(
        { coverPath: coverPath },
        userProfileClient.updateCoverPhoto,
        senderHeaders,
        {
          ...UserError.UPDATE_COVER_PHOTO,
          details: UPDATE_COVER_PHOTO_DETAILS[0],
        },
      );
    });
  });
});
