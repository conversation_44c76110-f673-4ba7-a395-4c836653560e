import { ulid } from 'ulid';

import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import channelViewClient from '../../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../../helpers/friend-service';
import friendViewClient from '../../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../../helpers/message-service';
import {
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import { userViewClient } from '../../../../../helpers/user-view-service';
import { HEADERS, Methods } from '../../../../const';
import {
  NULL_UNDEFINED_ERROR,
  UPDATE_COVER_PHOTO_DETAILS,
  UserError,
} from '../../../../error-message';

describe(Methods.DELETE_COVER_PHOTO, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_COVER_PHOTO;

  let sender;
  let receiver;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  const coverPath = 'https://fs.ugc.ziicdn.net/' + ulid() + '/addCover.jpg';

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await createAddAndAcceptFriends(sender, [receiver]);

    await mockDmMessageData(senderHeaders, receiver, 'hello');
    await mockDmMessageData(receiverHeaders, sender, 'hello');
  });

  describe('Success cases', () => {
    beforeEach(async () => {
      await getResponseSuccess(
        { coverPath: coverPath },
        userProfileClient.addCoverPhoto,
        senderHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteCoverPhoto,
        senderHeaders,
      );
    });

    it('should return data in getMe', async () => {
      const response = await getResponseSuccess(
        {},
        userViewClient.getMe,
        senderHeaders,
      );
      expect(response.data?.profile?.cover).toEqual('');
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUser', async () => {
      const response = await getResponseSuccess(
        { userId: sender.userId },
        userViewClient.getUser,
        receiverHeaders,
      );
      expect(response.data?.profile?.cover).toEqual('');
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUserByUsername', async () => {
      const response = await getResponseSuccess(
        { username: sender.username },
        userViewClient.getUserByUsername,
        receiverHeaders,
      );
      expect(response.data?.profile?.cover).toEqual('');
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getDmChannel', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        channelViewClient.getDmChannel,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual('');
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        channelViewClient.getDmChannel,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual('');
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );
    });

    for (const method of [
      channelViewClient.listAllChannels,
      channelViewClient.listDmChannels,
    ]) {
      it('should return data in ' + method.name, async () => {
        //By receiver
        let response = await getResponseSuccess({}, method, receiverHeaders);

        let includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.cover).toEqual('');
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );

        //By sender
        response = await getResponseSuccess({}, method, senderHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

        includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.cover).toEqual('');
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );
      });
    }

    it('should return data in getFriend', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        friendViewClient.getFriend,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual('');

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        friendViewClient.getFriend,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual('');
    });

    it('should return data in listFriends', async () => {
      //By receiver
      let response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.cover).toEqual('');

      //TODO https://github.com/halonext/ziichat-issues/issues/21015
      //By sender
      response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      // expect(includesData?.profile?.cover).toEqual('');
    });
  });

  describe('Error case', () => {
    it('should return error when not have cover photo before', async function () {
      await expectForParamInvalid(
        {},
        userProfileClient.deleteCoverPhoto,
        senderHeaders,
        {
          ...UserError.DELETE_COVER_PHOTO,
          details: UPDATE_COVER_PHOTO_DETAILS[0],
        },
      );
    });

    it('should return error when delete cover photo before', async function () {
      await getResponseSuccess(
        { coverPath: coverPath },
        userProfileClient.addCoverPhoto,
        senderHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteCoverPhoto,
        senderHeaders,
      );

      await expectForParamInvalid(
        {},
        userProfileClient.deleteCoverPhoto,
        senderHeaders,
        {
          ...UserError.DELETE_COVER_PHOTO,
          details: UPDATE_COVER_PHOTO_DETAILS[0],
        },
      );
    });
  });
});
