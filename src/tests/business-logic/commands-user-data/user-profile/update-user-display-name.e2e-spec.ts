import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UpdateUserDisplayNameRequest } from '../../../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import channelViewClient from '../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
  randomEmojis,
  wait,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import { userViewClient } from '../../../../helpers/user-view-service';
import { HEADERS, MaxLength, Methods } from '../../../const';
import {
  EXPECTED_VALIDATION_ERROR,
  NULL_UNDEFINED_ERROR,
  UPDATE_DISPLAY_NAME_DETAILS,
  UserError,
} from '../../../error-message';

describe(Methods.UPDATE_USER_DISPLAY_NAME, () => {
  const prefix = getPrefixMockUser() + Methods.UPDATE_USER_DISPLAY_NAME;
  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  const displayName = faker.person.fullName();

  const displayNameValues = [
    {
      title: 'lowercase text',
      displayName: 'display name',
    },
    {
      title: 'uppercase text',
      displayName: 'DISPLAY NAME',
    },
    {
      title: 'number',
      displayName: faker.number.int().toString(),
    },
    {
      title: 'special character',
      displayName: '!@#$%^&*()',
    },
    {
      title: 'emoji',
      displayName: randomEmojis(MaxLength.DISPLAY_NAME),
    },
    {
      title: 'having space',
      displayName: '     display name      ',
    },
    {
      title: 'accented Vietnamese',
      displayName: 'tiếng việt',
    },
    {
      title: 'accented Hindi, Chinese',
      displayName: 'स्वागत 欢迎',
    },
    {
      title: 'maxlength - ' + MaxLength.DISPLAY_NAME,
      displayName: 'a'.repeat(MaxLength.DISPLAY_NAME),
    },
    {
      title: '1 char',
      displayName: faker.string.alpha(1),
    },
    {
      title: '1 emoji',
      displayName: faker.internet.emoji().toString(),
    },
  ];

  const removeDisplayNameValues = [
    {
      title: 'without',
      displayName: undefined,
    },
    //TODO https://github.com/halonext/ziichat-issues/issues/21050
    // {
    //   title: 'null',
    //   displayName: null,
    // },
    {
      title: 'undefined',
      displayName: undefined,
    },
    {
      title: 'empty string',
      displayName: '',
    },
    {
      title: 'only spacing',
      displayName: '    ',
    },
  ];

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await mockDmMessageData(senderHeaders, receiver, 'Hello');
    await mockDmMessageData(receiverHeaders, sender, 'Hello');

    await createAddAndAcceptFriends(sender, [receiver]);

    await getResponseSuccess(
      {
        displayName: displayName,
      },
      userProfileClient.updateUserDisplayName,
      senderHeaders,
    );
  });

  describe('Success case', () => {
    for (const value of displayNameValues) {
      it('should return true when displayName is ' + value.title, async () => {
        await getResponseSuccess(
          {
            displayName: value.displayName,
          },
          userProfileClient.updateUserDisplayName,
          receiverHeaders,
        );

        await wait();

        const response = await getResponseSuccess(
          { userId: receiver.userId },
          userViewClient.getUser,
          senderHeaders,
        );
        expect(response.data?.profile?.displayName).toEqual(
          value.displayName.trim(),
        );

        expectGreaterThanTime(
          response.data?.createTime as string,
          response.data?.updateTime as string,
        );
      });
    }

    it('should return data in getMe', async () => {
      const response = await getResponseSuccess(
        {},
        userViewClient.getMe,
        senderHeaders,
      );
      expect(response.data?.profile?.displayName).toEqual(displayName);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUser', async () => {
      const response = await getResponseSuccess(
        { userId: sender.userId },
        userViewClient.getUser,
        receiverHeaders,
      );
      expect(response.data?.profile?.displayName).toEqual(displayName);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getUserByUsername', async () => {
      const response = await getResponseSuccess(
        { username: sender.username },
        userViewClient.getUserByUsername,
        receiverHeaders,
      );
      expect(response.data?.profile?.displayName).toEqual(displayName);
      expectGreaterThanTime(
        response.data?.createTime as string,
        response.data?.updateTime as string,
      );
    });

    it('should return data in getDmChannel', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        channelViewClient.getDmChannel,
        receiverHeaders,
      );
      expect(response.data?.channel?.name).toEqual(displayName);

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.displayName).toEqual(displayName);
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        channelViewClient.getDmChannel,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.displayName).toEqual(displayName);
      expectGreaterThanTime(
        includesData?.createTime as string,
        includesData?.updateTime as string,
      );
    });

    for (const method of [
      channelViewClient.listAllChannels,
      channelViewClient.listDmChannels,
    ]) {
      it('should return data in ' + method.name, async () => {
        //By receiver
        let response = await getResponseSuccess({}, method, receiverHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0]?.channel?.name).toEqual(displayName);

        let includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.displayName).toEqual(displayName);
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );

        //By sender
        response = await getResponseSuccess({}, method, senderHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

        includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expect(includesData?.profile?.displayName).toEqual(displayName);
        expectGreaterThanTime(
          includesData?.createTime as string,
          includesData?.updateTime as string,
        );
      });
    }

    it('should return data in getFriend', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        friendViewClient.getFriend,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.displayName).toEqual(displayName);

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        friendViewClient.getFriend,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.displayName).toEqual(displayName);
    });

    it('should return data in listFriends', async () => {
      //By receiver
      let response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expect(includesData?.profile?.displayName).toEqual(displayName);

      //TODO https://github.com/halonext/ziichat-issues/issues/21015
      //By sender
      response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      //expect(includesData?.profile?.displayName).toEqual(displayName);
    });

    for (const name of removeDisplayNameValues) {
      it('should remove display name when name is ' + name.title, async () => {
        await getResponseSuccess(
          {
            displayName: faker.string.alpha(5),
          },
          userProfileClient.updateUserDisplayName,
          senderHeaders,
        );

        const request: V3UpdateUserDisplayNameRequest = {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          displayName: name.displayName,
        };
        if (name.title == 'without') delete request.displayName;

        await getResponseSuccess(
          request,
          userProfileClient.updateUserDisplayName,
          senderHeaders,
        );

        const response = await getResponseSuccess(
          { userId: sender.userId },
          userViewClient.getUser,
          receiverHeaders,
        );
        expect(response.data?.profile?.displayName).toEqual('');
        expectGreaterThanTime(
          response.data?.createTime as string,
          response.data?.updateTime as string,
        );
      });
    }
  });

  describe('Error case', () => {
    it('should return error when the old name is the same as the new name', async () => {
      const displayName = 'the same name';
      await getResponseSuccess(
        {
          displayName: displayName,
        },
        userProfileClient.updateUserDisplayName,
        senderHeaders,
      );

      await expectForParamInvalid(
        {
          displayName: displayName,
        },
        userProfileClient.updateUserDisplayName,
        senderHeaders,
        {
          ...UserError.UPDATE_USER_DISPLAY_NAME,
          details: UPDATE_DISPLAY_NAME_DETAILS[0],
        },
      );
    });

    it(`should return error when displayName exceed ${MaxLength.DISPLAY_NAME} character`, async () => {
      await expectForParamInvalid(
        {
          displayName: 'a'.repeat(MaxLength.DISPLAY_NAME + 1),
        },
        userProfileClient.updateUserDisplayName,
        senderHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: UPDATE_DISPLAY_NAME_DETAILS[1],
        },
      );
    });
  });
});
