import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import { userProfileClient } from '../../../../../helpers/user-service';
import { HEADERS, Methods } from '../../../../const';

describe(Methods.CLEAR_USER_VISITED_PROFILE_NOTIFICATIONS, () => {
  const prefix =
    getPrefixMockUser() + Methods.CLEAR_USER_VISITED_PROFILE_NOTIFICATIONS;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  beforeEach(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  });

  it(`should return true when clear notifications (not exist visited user)`, async () => {
    await getResponseSuccess(
      {},
      userProfileClient.clearUserVisitedProfileNotifications,
      senderHeaders,
    );
  });

  it(`should return true when clear notifications (exist visited user)`, async () => {
    await getResponseSuccess(
      { userId: sender.userId },
      userProfileClient.visitedProfile,
      receiverHeaders,
    );

    await getResponseSuccess(
      {},
      userProfileClient.clearUserVisitedProfileNotifications,
      senderHeaders,
    );
  });

  it(`should return true when clear notifications twice`, async () => {
    await getResponseSuccess(
      {},
      userProfileClient.clearUserVisitedProfileNotifications,
      senderHeaders,
    );
    await getResponseSuccess(
      {},
      userProfileClient.clearUserVisitedProfileNotifications,
      senderHeaders,
    );
  });
});
