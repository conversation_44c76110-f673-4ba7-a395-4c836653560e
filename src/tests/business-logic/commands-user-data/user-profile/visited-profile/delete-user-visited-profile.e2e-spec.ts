import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import { isNotExistInListUserVisitedProfile } from '../../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  userProfileClient,
  userSettingClient,
} from '../../../../../helpers/user-service';
import { userViewClient } from '../../../../../helpers/user-view-service';
import { HEADERS, Methods } from '../../../../const';

describe(Methods.DELETE_USER_VISITED_PROFILE, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_USER_VISITED_PROFILE;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  beforeEach(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  });

  describe('Success case', () => {
    it('should return true when delete user who has visited profile', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.deleteUserVisitedProfile,
        receiverHeaders,
      );

      const listVisited = await getResponseSuccess(
        {},
        userViewClient.listUserVisitedProfile,
        receiverHeaders,
      );
      expect(listVisited.data).toEqual([]);
    });

    it('should return true when delete user visited profile of blocker/blocked user', async () => {
      //Visited each other's profile
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );
      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.visitedProfile,
        receiverHeaders,
      );

      //Block
      await getResponseSuccess(
        { targetUserId: receiver.userId },
        userSettingClient.blockUser,
        senderHeaders,
      );

      //Delete noti each other's
      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.deleteUserVisitedProfile,
        receiverHeaders,
      );
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.deleteUserVisitedProfile,
        senderHeaders,
      );

      await isNotExistInListUserVisitedProfile(
        sender.userId as string,
        receiverHeaders,
      );
      await isNotExistInListUserVisitedProfile(
        receiver.userId as string,
        receiverHeaders,
      );
    });
  });

  //TODO https://github.com/halonext/ziichat-issues/issues/19424
  describe.skip('Error case', () => {
    it('should return error when delete user who not visited profile', async () => {
      await expectForParamInvalid(
        { userId: receiver.userId },
        userProfileClient.deleteUserVisitedProfile,
        senderHeaders,
        {},
      );
    });

    it('should return error when delete user visited profile twice', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.deleteUserVisitedProfile,
        receiverHeaders,
      );

      await expectForParamInvalid(
        { userId: receiver.userId },
        userProfileClient.deleteUserVisitedProfile,
        senderHeaders,
        {},
      );
    });

    it('should return error when delete user visited profile with own', async () => {
      await expectForParamInvalid(
        { userId: receiver.userId },
        userProfileClient.deleteUserVisitedProfile,
        receiverHeaders,
        {},
      );
    });
  });
});
