import { getPrefixMockUser } from '../../../../../../jest-e2e';
import { V3MockedUser } from '../../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../../expected-results/expected-errors';
import {
  isExistInListUserVisitedProfile,
  isNotExistInListUserVisitedProfile,
} from '../../../../../expected-results/views-chat/user-view';
import { mockUsersData } from '../../../../../helpers/internal-faker-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../../helpers/shared/common';
import {
  userProfileClient,
  userSettingClient,
} from '../../../../../helpers/user-service';
import { HEADERS, Methods, ZIICHAT_BOT_USERID } from '../../../../const';
import { UserError, VISITED_PROFILE_DETAILS } from '../../../../error-message';

describe(Methods.VISITED_PROFILE, () => {
  const prefix = getPrefixMockUser() + Methods.VISITED_PROFILE;

  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  beforeEach(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);
  });

  describe('Success case', () => {
    it('should return true when visit user profile', async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );

      await isExistInListUserVisitedProfile(
        sender.userId as string,
        receiverHeaders,
      );

      await isNotExistInListUserVisitedProfile(
        receiver.userId as string,
        senderHeaders,
      );
    });

    it(`should return true when visit my profile`, async () => {
      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );

      await isNotExistInListUserVisitedProfile(
        receiver.userId as string,
        senderHeaders,
      );
    });

    it(`should return true when visit bot profile`, async () => {
      await getResponseSuccess(
        { userId: ZIICHAT_BOT_USERID },
        userProfileClient.visitedProfile,
        senderHeaders,
      );
    });

    it(`should return true when visit profile of blocked/blocker user`, async () => {
      await getResponseSuccess(
        { targetUserId: receiver.userId },
        userSettingClient.blockUser,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );

      await getResponseSuccess(
        { userId: sender.userId },
        userProfileClient.visitedProfile,
        receiverHeaders,
      );

      await isExistInListUserVisitedProfile(
        sender.userId as string,
        receiverHeaders,
      );

      await isExistInListUserVisitedProfile(
        receiver.userId as string,
        senderHeaders,
      );
    });
  });

  describe('Error case', () => {
    it(`should return false when visit user profile twice`, async () => {
      await getResponseSuccess(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
      );
      await expectForParamInvalid(
        { userId: receiver.userId },
        userProfileClient.visitedProfile,
        senderHeaders,
        {
          ...UserError.VISITED_PROFILE,
          details: VISITED_PROFILE_DETAILS[0],
        },
      );

      await isExistInListUserVisitedProfile(
        sender.userId as string,
        receiverHeaders,
      );
    });
  });
});
