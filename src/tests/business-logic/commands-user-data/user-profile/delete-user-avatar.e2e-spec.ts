import { getPrefixMockUser } from '../../../../../jest-e2e';
import { V3UserStatusExpireAfterTimeEnum } from '../../../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { V3UserAvatarTypeEnum } from '../../../../../utils/http-client/suggestion-client';
import { V3Profile } from '../../../../../utils/http-client/views-chat-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import { expectAvatarFields } from '../../../../expected-results/views-chat/user-view';
import channelViewClient from '../../../../helpers/channel-view-service';
import { createAddAndAcceptFriends } from '../../../../helpers/friend-service';
import friendViewClient from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import { mockDmMessageData } from '../../../../helpers/message-service';
import {
  createHeaders,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userProfileClient } from '../../../../helpers/user-service';
import { userViewClient } from '../../../../helpers/user-view-service';
import { HEADERS, Methods } from '../../../const';
import {
  DELETE_USER_AVATAR_DETAILS,
  NULL_UNDEFINED_ERROR,
  UserError,
} from '../../../error-message';

describe(Methods.DELETE_USER_AVATAR, () => {
  const prefix = getPrefixMockUser() + Methods.DELETE_USER_AVATAR;
  let sender: V3MockedUser;
  let receiver: V3MockedUser;

  let senderHeaders: HEADERS;
  let receiverHeaders: HEADERS;

  const avatarPath =
    'https://fs.ugc.ziicdn.net/01JF97QHG30420ST0T3F3YJ270/a.jpg';

  beforeAll(async () => {
    [sender, receiver] = await mockUsersData(prefix, 2);

    senderHeaders = createHeaders(sender.token as string);
    receiverHeaders = createHeaders(receiver.token as string);

    await mockDmMessageData(senderHeaders, receiver, 'Hello');
    await mockDmMessageData(receiverHeaders, sender, 'Hello');

    await createAddAndAcceptFriends(sender, [receiver]);

    await getResponseSuccess(
      {
        avatarPath: avatarPath,
      },
      userProfileClient.updateUserAvatar,
      senderHeaders,
    );

    await getResponseSuccess(
      {},
      userProfileClient.deleteUserAvatar,
      senderHeaders,
    );
  });

  describe('Success case', () => {
    it('should return data in getMe', async () => {
      const response = await getResponseSuccess(
        {},
        userViewClient.getMe,
        senderHeaders,
      );
      expectAvatarFields(
        response?.data?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    it('should return data in getUser', async () => {
      const response = await getResponseSuccess(
        { userId: sender.userId },
        userViewClient.getUser,
        receiverHeaders,
      );
      expectAvatarFields(
        response.data?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    it('should return data in getUserByUsername', async () => {
      const response = await getResponseSuccess(
        { username: sender.username },
        userViewClient.getUserByUsername,
        receiverHeaders,
      );
      expectAvatarFields(
        response.data?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    it('should return data in getDmChannel', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        channelViewClient.getDmChannel,
        receiverHeaders,
      );
      expectAvatarFields(
        response.data?.channel as V3Profile,
        '',
        undefined,
        false,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(
        includesData?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        channelViewClient.getDmChannel,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(
        includesData?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    for (const method of [
      channelViewClient.listAllChannels,
      channelViewClient.listDmChannels,
    ]) {
      it('should return data in ' + method.name, async () => {
        //By receiver
        let response = await getResponseSuccess({}, method, receiverHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expectAvatarFields(
          response.data[0].channel as V3Profile,
          '',
          undefined,
          false,
        );

        let includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expectAvatarFields(
          includesData?.profile as V3Profile,
          '',
          V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
        );

        //By sender
        response = await getResponseSuccess({}, method, senderHeaders);
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

        includesData = response.includes?.users?.find(
          (user) => user.userId == sender.userId,
        );
        expectAvatarFields(
          includesData?.profile as V3Profile,
          '',
          V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
        );
      });
    }

    it('should return data in getFriend', async () => {
      //By receiver
      let response = await getResponseSuccess(
        { userId: sender.userId },
        friendViewClient.getFriend,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(
        includesData?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );

      //By sender
      response = await getResponseSuccess(
        { userId: receiver.userId },
        friendViewClient.getFriend,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(
        includesData?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    it('should return data in listFriends', async () => {
      //By receiver
      let response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        receiverHeaders,
      );

      let includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      expectAvatarFields(
        includesData?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );

      //By sender
      response = await getResponseSuccess(
        {},
        friendViewClient.listFriends,
        senderHeaders,
      );

      includesData = response.includes?.users?.find(
        (user) => user.userId == sender.userId,
      );
      //TODO https://github.com/halonext/ziichat-issues/issues/21015
      // expectAvatarFields(includesData?.profile as V3Profile, '');
    });

    it('should return data in listUserStatus', async () => {
      await getResponseSuccess(
        {
          status: '   🥺   ',
          expireAfterTime:
            V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER4HOUR,
        },
        userProfileClient.addUserStatus,
        senderHeaders,
      );

      const response = await getResponseSuccess(
        {},
        userViewClient.listUserStatus,
        receiverHeaders,
      );
      if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);

      const user = response.data.find((user) => user.userId == sender.userId);
      expectAvatarFields(
        user?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
    });

    it('should return true when updateUserVideoAvatar -> updateUserAvatar -> deleteUserAvatar', async () => {
      await getResponseSuccess(
        {
          avatarPath: avatarPath,
        },
        userProfileClient.updateUserVideoAvatar,
        senderHeaders,
      );

      await getResponseSuccess(
        {
          avatarPath: avatarPath,
        },
        userProfileClient.updateUserAvatar,
        senderHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteUserAvatar,
        senderHeaders,
      );

      const response = await getResponseSuccess(
        { userId: sender.userId },
        userViewClient.getUser,
        receiverHeaders,
      );
      expectAvatarFields(
        response.data?.profile as V3Profile,
        '',
        V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
      );
      expect(response.data?.profile?.videoAvatar).toEqual('');
    });
  });

  describe('Error case', () => {
    it('should return false when user not have avatar', async () => {
      await getResponseSuccess(
        {
          avatarPath: avatarPath,
        },
        userProfileClient.updateUserAvatar,
        receiverHeaders,
      );

      await getResponseSuccess(
        {},
        userProfileClient.deleteUserAvatar,
        receiverHeaders,
      );

      await expectForParamInvalid(
        {},
        userProfileClient.deleteUserAvatar,
        receiverHeaders,
        {
          ...UserError.DELETE_USER_AVATAR,
          details: DELETE_USER_AVATAR_DETAILS[0],
        },
      );
    });

    it('should return false when updateUserVideoAvatar -> deleteUserAvatar', async () => {
      await getResponseSuccess(
        {
          avatarPath: avatarPath,
        },
        userProfileClient.updateUserVideoAvatar,
        receiverHeaders,
      );

      await expectForParamInvalid(
        {},
        userProfileClient.deleteUserAvatar,
        receiverHeaders,
        {
          ...UserError.DELETE_USER_AVATAR,
          details: DELETE_USER_AVATAR_DETAILS[0],
        },
      );
    });
  });
});
