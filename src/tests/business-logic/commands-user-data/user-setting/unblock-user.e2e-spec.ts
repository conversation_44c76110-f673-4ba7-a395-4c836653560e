import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3AddMessageReactionRequest,
  V3AttachmentTypeEnum,
  V3ReportCategory,
  V3SendDmMessageMediaRequest,
  V3SendMessageMediaRequest,
} from '../../../../../utils/http-client/commands-message-client';
import { isNotExistInListBlockedUsers } from '../../../../expected-results/commands-user-data/user-setting';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import { isExistInListChannels } from '../../../../expected-results/views-chat/channel-view';
import {
  isExistInListFriend,
  isExistInListInComingFriendRequests,
  isExistInListOutGoingFriendRequests,
} from '../../../../expected-results/views-chat/friend-view';
import { mockChannelData } from '../../../../helpers/channel-service';
import channelViewService from '../../../../helpers/channel-view-service';
import channelViewClient from '../../../../helpers/channel-view-service';
import friendClient, {
  createAddAndAcceptFriends,
} from '../../../../helpers/friend-service';
import friendViewService from '../../../../helpers/friend-view-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import invitationClient from '../../../../helpers/invitation-service';
import messageClient, {
  mockDmMessageData,
} from '../../../../helpers/message-service';
import messageViewClient from '../../../../helpers/message-view-service';
import { searchClient } from '../../../../helpers/search-service';
import {
  createHeaders,
  getResponseSuccess,
  wait,
} from '../../../../helpers/shared/common';
import {
  userReportClient,
  userSettingClient,
} from '../../../../helpers/user-service';
import { userViewClient } from '../../../../helpers/user-view-service';
import {
  HEADERS,
  Methods,
  STATUS_EMOJI_LIST,
  SystemMessage,
  WORKSPACE_ID,
  ZIICHAT_BOT_USERID,
} from '../../../const';
import {
  BLOCK_USER_DETAILS,
  NULL_UNDEFINED_ERROR,
  UserError,
} from '../../../error-message';

describe(Methods.UNBLOCK_USER, () => {
  const prefix = getPrefixMockUser() + Methods.UNBLOCK_USER;
  let blocker;
  let blockedUser;
  let stranger;

  let blockerHeaders: HEADERS;
  let blockedUserHeaders: HEADERS;
  let strangerHeaders: HEADERS;

  let dmChannel;

  const expectedBusinessError = {
    ...UserError.UNBLOCK_USER,
    details: BLOCK_USER_DETAILS[0],
  };

  const createListUsers = async () => {
    [blocker, blockedUser, stranger] = await mockUsersData(prefix, 3);

    blockerHeaders = createHeaders(blocker.token as string);
    blockedUserHeaders = createHeaders(blockedUser.token as string);
    strangerHeaders = createHeaders(stranger.token as string);
  };

  describe('Success cases', () => {
    describe('After unblock user', () => {
      beforeAll(async () => {
        await createListUsers();

        await createAddAndAcceptFriends(blocker, [blockedUser]);

        dmChannel = await mockDmMessageData(
          blockerHeaders,
          blockedUser,
          'hello',
        );
        await mockDmMessageData(blockedUserHeaders, blocker, 'hello');

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );
      });
      it('should not display blockedUser in listBlock of blocker', async () => {
        await isNotExistInListBlockedUsers(blockedUser, blockerHeaders);
      });

      it('should display dmChannel after unblockUser', async () => {
        await isExistInListChannels(
          dmChannel.channelId,
          [blocker, blockedUser],
          [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
        );

        await getResponseSuccess(
          { userId: blockedUser.userId },
          channelViewService.getDmChannel,
          blockerHeaders,
        );

        await getResponseSuccess(
          { userId: blocker.userId },
          channelViewService.getDmChannel,
          blockedUserHeaders,
        );
      });

      it(`should return true when listDmMessages`, async () => {
        await getResponseSuccess(
          { userId: blockedUser.userId },
          messageViewClient.listDmMessages,
          blockerHeaders,
        );

        await getResponseSuccess(
          { userId: blocker.userId },
          messageViewClient.listDmMessages,
          blockedUserHeaders,
        );
      });
      //TODO
      it.skip(`should return data when SearchUsers`, async () => {
        let response = await getResponseSuccess(
          { keyword: blockedUser.username },
          searchClient.searchSearchUsers,
          blockerHeaders,
        );

        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0].userId).toEqual(blockedUser.userId);

        response = await getResponseSuccess(
          { keyword: blocker.username },
          searchClient.searchSearchUsers,
          blockedUserHeaders,
        );
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0].userId).toEqual(blockedUser.userId);
      });

      it.skip('should return data when SearchFriends', async () => {});

      it(`should return true when getUser`, async () => {
        let response = await getResponseSuccess(
          { userId: blockedUser.userId },
          userViewClient.getUser,
          blockerHeaders,
        );
        expect(response.data?.blocked).toBeFalsy();

        response = await getResponseSuccess(
          { userId: blocker.userId },
          userViewClient.getUser,
          blockedUserHeaders,
        );
        expect(response.data?.blocked).toBeFalsy();
      });

      it(`should return true when getUserByUsername`, async () => {
        let response = await getResponseSuccess(
          { username: blockedUser.username },
          userViewClient.getUserByUsername,
          blockerHeaders,
        );
        expect(response.data?.blocked).toBeFalsy();

        response = await getResponseSuccess(
          { username: blocker.username },
          userViewClient.getUserByUsername,
          blockedUserHeaders,
        );
        expect(response.data?.blocked).toBeFalsy();
      });

      it(`should return true when reportUser`, async () => {
        await getResponseSuccess(
          {
            userId: blockedUser.userId,
            reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
          },
          userReportClient.reportUser,
          blockerHeaders,
        );

        await getResponseSuccess(
          {
            userId: blocker.userId,
            reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
          },
          userReportClient.reportUser,
          blockedUserHeaders,
        );
      });

      it(`should receive invitation message`, async () => {
        //Send by blocker
        const channelOfBlocker = await mockChannelData(
          { name: 'channel', workspaceId: WORKSPACE_ID },
          [blocker],
        );
        await getResponseSuccess(
          {
            userIds: [blockedUser.userId],
            invitationLink: channelOfBlocker.invitationLink,
          },
          invitationClient.sendInvitation,
          blockerHeaders,
        );
        await wait();
        let response = await getResponseSuccess(
          { userId: ZIICHAT_BOT_USERID },
          messageViewClient.listDmMessages,
          blockedUserHeaders,
        );
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0].message?.content).toEqual(
          `${SystemMessage.SEND_INVITATION}${channelOfBlocker.invitationLink}`,
        );

        //Send by blockedUser
        const channelOfBlockedUser = await mockChannelData(
          { name: 'channel', workspaceId: WORKSPACE_ID },
          [blockedUser],
        );
        await getResponseSuccess(
          {
            userIds: [blocker.userId],
            invitationLink: channelOfBlockedUser.invitationLink,
          },
          invitationClient.sendInvitation,
          blockedUserHeaders,
        );
        await wait();

        response = await getResponseSuccess(
          { userId: ZIICHAT_BOT_USERID },
          messageViewClient.listDmMessages,
          blockerHeaders,
        );
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0].message?.content).toEqual(
          `${SystemMessage.SEND_INVITATION}${channelOfBlockedUser.invitationLink}`,
        );
      });

      it('should return true when listFriends', async () => {
        await isExistInListFriend(blocker, blockedUser, blockerHeaders);
        await isExistInListFriend(blocker, blockedUser, blockedUserHeaders);
      });

      it('should return true when getFriend', async () => {
        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendViewService.getFriend,
          blockerHeaders,
        );
        await getResponseSuccess(
          { userId: blocker.userId },
          friendViewService.getFriend,
          blockedUserHeaders,
        );
      });
    });

    describe('Not friend', () => {
      beforeEach(async () => {
        await createListUsers();
      });

      it('should return true when send friend request after unblock user', async () => {
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );

        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendClient.addFriend,
          blockerHeaders,
        );
      });

      it('should return true when acceptFriendRequest after unblock user', async () => {
        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendClient.addFriend,
          blockerHeaders,
        );

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );

        await getResponseSuccess(
          { userId: blocker.userId },
          friendClient.acceptFriendRequest,
          blockedUserHeaders,
        );
      });

      it('should return true when deleteFriendRequest, cancelFriendRequest after unblock user', async () => {
        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendClient.addFriend,
          blockerHeaders,
        );

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );

        await getResponseSuccess(
          { userId: blocker.userId },
          friendClient.deleteFriendRequest,
          blockedUserHeaders,
        );

        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendClient.cancelFriendRequest,
          blockerHeaders,
        );
      });

      it('should still display dmChannel in listInComingFriendRequests, listOutGoingFriendRequests after unblock user', async () => {
        await getResponseSuccess(
          { userId: blockedUser.userId },
          friendClient.addFriend,
          blockerHeaders,
        );

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );
        await isExistInListInComingFriendRequests(
          blocker,
          blockedUser,
          blockedUserHeaders,
        );
        await isExistInListOutGoingFriendRequests(
          blocker,
          blockedUser,
          blockerHeaders,
        );
      });
    });

    describe('Message', () => {
      let msgInfo;
      describe('Message options', () => {
        beforeAll(async () => {
          await createListUsers();

          msgInfo = await mockDmMessageData(
            blockerHeaders,
            blockedUser,
            'hello',
          );

          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgInfo.messageId,
              emoji: STATUS_EMOJI_LIST[0],
            },
            messageClient.addDmMessageReaction,
            blockerHeaders,
          );

          await getResponseSuccess(
            { targetUserId: blockedUser.userId },
            userSettingClient.blockUser,
            blockerHeaders,
          );
          await wait(5000);
          await getResponseSuccess(
            { targetUserId: blockedUser.userId },
            userSettingClient.unblockUser,
            blockerHeaders,
          );
        });

        it('should return true when sendDmMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              content: faker.string.alpha(10),
              ref: 'ref',
            },
            messageClient.sendDmMessage,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              content: faker.string.alpha(10),
              ref: 'ref',
            },
            messageClient.sendDmMessage,
            blockedUserHeaders,
          );
        });

        it('should return true when sendDmLocation after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              longitude: '11',
              latitude: '11',
              ref: 'ref',
            },
            messageClient.sendDmLocation,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              longitude: '11',
              latitude: '11',
              ref: 'ref',
            },
            messageClient.sendDmLocation,
            blockedUserHeaders,
          );
        });

        it('should return true when sendPokeMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              ref: 'ref',
            },
            messageClient.sendPokeMessage,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              ref: 'ref',
            },
            messageClient.sendPokeMessage,
            blockedUserHeaders,
          );
        });

        it('should return true when sendDmMessageMedia after unblock user', async () => {
          const request: V3SendDmMessageMediaRequest = {
            ref: '1',
            mediaObjects: [
              {
                attachmentType: 1,
                fileUrl:
                  'https://fs.ugc.ziicdn.net/01JEZJ0GPMSHH2RVX9QTSJJNRX/heic_300kB.png',
                fileMetadata: {
                  mimetype: 'text/plain',
                  filename: 'text.txt',
                  extension: 'text.txt',
                },
              },
            ],
            attachmentType: V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_PHOTO,
            userId: blockedUser.userId,
          };
          await getResponseSuccess(
            request,
            messageClient.sendDmMessageMedia,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              ...request,
              userId: blocker.userId,
            },
            messageClient.sendDmMessageMedia,
            blockedUserHeaders,
          );
        });

        it.skip('should return true when sendDmStickerMessage after unblock user', async () => {});

        it('should return true when updateDmMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              content: faker.string.alpha(10),
              ref: 'ref',
              messageId: msgInfo.messageId,
            },
            messageClient.updateDmMessage,
            blockerHeaders,
          );
        });

        it('should return true when addDmMessageReaction after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgInfo.messageId,
              emoji: STATUS_EMOJI_LIST[0],
            },
            messageClient.addDmMessageReaction,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              messageId: msgInfo.messageId,
              emoji: STATUS_EMOJI_LIST[0],
            },
            messageClient.addDmMessageReaction,
            blockedUserHeaders,
          );
        });

        it('should return true when revokeDmMessageReaction after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgInfo.messageId,
              emoji: STATUS_EMOJI_LIST[0],
            },
            messageClient.revokeDmMessageReaction,
            blockerHeaders,
          );
        });

        it('should return true when quoteDmMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgInfo.messageId,
              content: 'quote msg',
              ref: 'ref',
            },
            messageClient.quoteDmMessage,
            blockerHeaders,
          );
        });

        it('should return true when pinUnpinDmMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgInfo.messageId,
              status: true,
            },
            messageClient.pinUnpinDmMessage,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              messageId: msgInfo.messageId,
              status: true,
            },
            messageClient.pinUnpinDmMessage,
            blockedUserHeaders,
          );
        });

        it('should return true when forwardMessagesToDmChannel after unblock user', async () => {
          let msgInfo = await mockDmMessageData(
            strangerHeaders,
            blocker,
            'hello',
          );

          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              originalMessageIds: [msgInfo.messageId],
            },
            messageClient.forwardMessagesToDmChannel,
            blockerHeaders,
          );

          msgInfo = await mockDmMessageData(
            strangerHeaders,
            blockedUser,
            'hello',
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              originalMessageIds: [msgInfo.messageId],
            },
            messageClient.forwardMessagesToDmChannel,
            blockedUserHeaders,
          );
        });
      });

      describe('Delete Message', () => {
        let msgByBlocker;
        let msgByBlockedUser;
        beforeEach(async () => {
          await createListUsers();

          msgByBlocker = await mockDmMessageData(
            blockerHeaders,
            blockedUser,
            'hello',
          );
          msgByBlockedUser = await mockDmMessageData(
            blockedUserHeaders,
            blocker,
            'hello',
          );

          await getResponseSuccess(
            { targetUserId: blockedUser.userId },
            userSettingClient.blockUser,
            blockerHeaders,
          );
          await wait(5000);
          await getResponseSuccess(
            { targetUserId: blockedUser.userId },
            userSettingClient.unblockUser,
            blockerHeaders,
          );
        });

        it('should return true when deleteDmMessagesForEveryone after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageIds: [msgByBlockedUser.messageId as string],
            },
            messageClient.deleteDmMessagesForEveryone,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              messageIds: [msgByBlocker.messageId],
            },
            messageClient.deleteDmMessagesForEveryone,
            blockedUserHeaders,
          );
        });

        it('should return true when deleteDmMessagesOnlyMe after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageIds: [msgByBlockedUser.messageId as string],
            },
            messageClient.deleteDmMessagesOnlyMe,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
              messageIds: [msgByBlocker.messageId],
            },
            messageClient.deleteDmMessagesOnlyMe,
            blockedUserHeaders,
          );
        });

        it('should return true when deleteAllDmMessagesForEveryone by blocker', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
            },
            messageClient.deleteAllDmMessagesForEveryone,
            blockerHeaders,
          );
        });

        it('should return true when deleteAllDmMessagesOnlyMe after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
            },
            messageClient.deleteAllDmMessagesOnlyMe,
            blockerHeaders,
          );

          await getResponseSuccess(
            {
              userId: blocker.userId,
            },
            messageClient.deleteAllDmMessagesOnlyMe,
            blockedUserHeaders,
          );
        });

        it('should return true when reportDmMessage after unblock user', async () => {
          await getResponseSuccess(
            {
              userId: blockedUser.userId,
              messageId: msgByBlockedUser.messageId,
              reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
            },
            messageClient.reportDmMessage,
            blockerHeaders,
          );
        });
      });
    });

    describe('Interact in general group', () => {
      let msgInfo;
      let channel;

      beforeAll(async () => {
        await createListUsers();

        channel = await mockChannelData(
          {
            name: 'channel',
            workspaceId: WORKSPACE_ID,
          },
          [blocker, blockedUser],
        );

        const response = await getResponseSuccess(
          {
            content: 'content',
            ref: 'ref',
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
          },
          messageClient.sendMessage,
          blockerHeaders,
        );
        msgInfo = response.data?.message;

        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.blockUser,
          blockerHeaders,
        );
        await wait(5000);
        await getResponseSuccess(
          { targetUserId: blockedUser.userId },
          userSettingClient.unblockUser,
          blockerHeaders,
        );
      });

      it('should return true when sendMessage', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              content: faker.string.alpha(10),
              ref: 'ref',
            },
            messageClient.sendMessage,
            header,
          );
        }
      });

      it('should return true when sendLocation', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              longitude: '11',
              latitude: '11',
              ref: 'ref',
            },
            messageClient.sendLocation,
            header,
          );
        }
      });

      it('should return true when sendMessageMedia', async () => {
        const request: V3SendMessageMediaRequest = {
          ref: '1',
          mediaObjects: [
            {
              attachmentType: 1,
              fileUrl:
                'https://fs.ugc.ziicdn.net/01JEZJ0GPMSHH2RVX9QTSJJNRX/heic_300kB.png',
              fileMetadata: {
                mimetype: 'text/plain',
                filename: 'text.txt',
                extension: 'text.txt',
              },
            },
          ],
          attachmentType: V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_PHOTO,
          channelId: channel.channelId,
          workspaceId: channel.workspaceId,
        };
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            request,
            messageClient.sendMessageMedia,
            header,
          );
        }
      });

      it.skip('should return true when sendStickerMessage', async () => {});

      it('should return true when updateMessage', async () => {
        await getResponseSuccess(
          {
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
            content: faker.string.alpha(10),
            ref: 'ref',
            messageId: msgInfo.messageId,
          },
          messageClient.updateMessage,
          blockerHeaders,
        );
      });

      it('should return true when addMessageReaction, revokeDmMessageReaction', async () => {
        const request: V3AddMessageReactionRequest = {
          channelId: channel.channelId,
          workspaceId: channel.workspaceId,
          messageId: msgInfo.messageId,
          emoji: STATUS_EMOJI_LIST[0],
        };

        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            request,
            messageClient.addMessageReaction,
            header,
          );

          await getResponseSuccess(
            request,
            messageClient.revokeMessageReaction,
            header,
          );
        }
      });

      it('should return true when quoteMessage', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              messageId: msgInfo.messageId,
              content: 'quote msg',
              ref: 'ref',
            },
            messageClient.quoteMessage,
            header,
          );
        }
      });

      it('should return true when pinUnpinMessage', async () => {
        await getResponseSuccess(
          {
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
            messageId: msgInfo.messageId,
            status: true,
          },
          messageClient.pinUnpinMessage,
          blockerHeaders,
        );
      });

      it('should return true when forwardMessagesToChannel', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              originalMessageIds: [msgInfo.messageId],
            },
            messageClient.forwardMessagesToChannel,
            header,
          );
        }
      });

      it('should return true when deleteMessagesForEveryone', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          const response = await getResponseSuccess(
            {
              content: 'content',
              ref: 'ref',
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
            },
            messageClient.sendMessage,
            header,
          );

          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              messageIds: [response.data?.message?.messageId as string],
            },
            messageClient.deleteMessagesForEveryone,
            header,
          );
        }
      });

      it('should return true when deleteMessagesOnlyMe', async () => {
        const response = await getResponseSuccess(
          {
            content: 'content',
            ref: 'ref',
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
          },
          messageClient.sendMessage,
          blockerHeaders,
        );

        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
              messageIds: [response.data?.message?.messageId as string],
            },
            messageClient.deleteMessagesOnlyMe,
            header,
          );
        }
      });

      it('should return true when deleteAllMessagesOnlyMe', async () => {
        for (const header of [blockerHeaders, blockedUserHeaders]) {
          await getResponseSuccess(
            {
              channelId: channel.channelId,
              workspaceId: channel.workspaceId,
            },
            messageClient.deleteAllMessagesOnlyMe,
            header,
          );
        }
      });

      it('should return true when reportMessage', async () => {
        const response = await getResponseSuccess(
          {
            content: 'content',
            ref: 'ref',
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
          },
          messageClient.sendMessage,
          blockerHeaders,
        );

        await getResponseSuccess(
          {
            channelId: channel.channelId,
            workspaceId: channel.workspaceId,
            messageId: response.data?.message?.messageId as string,
            reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
          },
          messageClient.reportMessage,
          blockedUserHeaders,
        );
      });
    });
  });

  describe('Error cases', () => {
    beforeEach(async () => {
      await createListUsers();
    });
    it('should return false when unblock user twice', async () => {
      await getResponseSuccess(
        { targetUserId: blockedUser.userId },
        userSettingClient.blockUser,
        blockerHeaders,
      );

      await getResponseSuccess(
        { targetUserId: blockedUser.userId },
        userSettingClient.unblockUser,
        blockerHeaders,
      );

      await expectForParamInvalid(
        { targetUserId: blockedUser.userId },
        userSettingClient.unblockUser,
        blockerHeaders,
        expectedBusinessError,
      );
    });

    it('should return false when blockedUser unblock blocker', async () => {
      await getResponseSuccess(
        { targetUserId: blockedUser.userId },
        userSettingClient.blockUser,
        blockerHeaders,
      );

      await expectForParamInvalid(
        { targetUserId: blocker.userId },
        userSettingClient.unblockUser,
        blockedUserHeaders,
        expectedBusinessError,
      );
    });

    it('should return false when unblock ZiiChat bot', async () => {
      await expectForParamInvalid(
        { targetUserId: ZIICHAT_BOT_USERID },
        userSettingClient.unblockUser,
        blockedUserHeaders,
        expectedBusinessError,
      );
    });

    it('should return false when blocker unblock their own', async () => {
      await expectForParamInvalid(
        { targetUserId: blocker.userId },
        userSettingClient.unblockUser,
        blockerHeaders,
        expectedBusinessError,
      );
    });
  });
});
