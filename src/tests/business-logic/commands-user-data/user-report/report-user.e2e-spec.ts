import { faker } from '@faker-js/faker';

import { getPrefixMockUser } from '../../../../../jest-e2e';
import {
  V3PretendingTo,
  V3ReportCategory,
  V3ReportUserRequestRequest,
} from '../../../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../../../utils/http-client/faker-client';
import { expectForParamInvalid } from '../../../../expected-results/expected-errors';
import { mockChannelData } from '../../../../helpers/channel-service';
import { mockUsersData } from '../../../../helpers/internal-faker-service';
import {
  mockDmMessageData,
  mockMessageData,
} from '../../../../helpers/message-service';
import { searchClient } from '../../../../helpers/search-service';
import {
  createHeaders,
  getEnumValues,
  getRequireMsgError,
  getResponseSuccess,
} from '../../../../helpers/shared/common';
import { userReportClient } from '../../../../helpers/user-service';
import { userViewClient } from '../../../../helpers/user-view-service';
import {
  HEADERS,
  MaxLength,
  Methods,
  WORKSPACE_ID,
  ZIICHAT_BOT_USERID,
  ZIICHAT_GHOST_USERID,
} from '../../../const';
import {
  EXPECTED_VALIDATION_ERROR,
  NULL_UNDEFINED_ERROR,
  REPORT_USER_DETAILS,
  UserError,
} from '../../../error-message';

describe(Methods.REPORT_USER, () => {
  const prefix = getPrefixMockUser() + Methods.REPORT_USER;

  let reportedUser: V3MockedUser;
  let reporter: V3MockedUser;

  let reporterHeaders: HEADERS;
  let reportedUserHeaders: HEADERS;

  let reportUserRequest: V3ReportUserRequestRequest;

  const reportCategoryType = getEnumValues(V3ReportCategory, [
    V3ReportCategory.REPORT_CATEGORY_OTHER,
    V3ReportCategory.REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE,
  ]);

  const reportReasonValues = [
    {
      title: 'number',
      value: faker.number.int().toString(),
    },
    {
      title: 'special character',
      value: '!@#$%^&*()',
    },
    //TODO https://github.com/halonext/ziichat-issues/issues/22049
    // {
    //   title: 'emoji',
    //   value: randomEmojis(MaxLength.REPORT_REASON),
    // },
    {
      title: 'having space',
      value: '     report reason     ',
    },
    {
      title: 'accented Vietnamese',
      value: 'tiếng việt',
    },
    {
      title: 'accented Hindi, Chinese',
      value: 'स्वागत 欢迎',
    },
    {
      title: 'maxlength: ' + MaxLength.REPORT_REASON,
      value: 'a'.repeat(MaxLength.REPORT_REASON),
    },
    {
      title: '1 char',
      value: faker.string.alpha(1),
    },
    {
      title: '1 emoji',
      value: faker.internet.emoji().toString(),
    },
  ];
  //TODO https://github.com/halonext/ziichat-issues/issues/21886
  //Change to beforeAll
  beforeEach(async () => {
    [reporter, reportedUser] = await mockUsersData(prefix, 2);

    reporterHeaders = createHeaders(reporter.token as string);
    reportedUserHeaders = createHeaders(reportedUser.token as string);

    reportUserRequest = {
      reportCategory: V3ReportCategory.REPORT_CATEGORY_SCAMS,
      userId: reportedUser.userId as string,
    };
  });
  describe('Validate case', () => {
    //TODO https://github.com/halonext/ziichat-issues/issues/22050
    it.skip('should return error when reportCategory is OTHERS without REASON', async () => {
      await expectForParamInvalid(
        {
          reportCategory: V3ReportCategory.REPORT_CATEGORY_OTHER,
          userId: reportedUser.userId as string,
        },
        userReportClient.reportUser,
        reporterHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: getRequireMsgError(['reportReason']),
        },
      );
    });

    it.skip('should return error when reportCategory is PRETENDING_TO_BE_SOMEONE without PRETENDING_TO', async () => {
      await expectForParamInvalid(
        {
          reportCategory:
            V3ReportCategory.REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE,
          userId: reportedUser.userId as string,
        },
        userReportClient.reportUser,
        reporterHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: getRequireMsgError(['pretendingTo']),
        },
      );
    });

    it.skip('should return error when pretendingTo is PRETENDING_TO_UNSPECIFIED', async () => {
      await expectForParamInvalid(
        {
          reportCategory:
            V3ReportCategory.REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE,
          pretendingTo: V3PretendingTo.PRETENDING_TO_UNSPECIFIED,
          userId: reportedUser.userId as string,
        },
        userReportClient.reportUser,
        reporterHeaders,
        {
          ...EXPECTED_VALIDATION_ERROR,
          details: getRequireMsgError(['pretendingTo']),
        },
      );
    });
  });

  describe('Success case', () => {
    for (const category of reportCategoryType) {
      it(`should return true when reportCategory is ${V3ReportCategory[category]}`, async () => {
        await getResponseSuccess(
          {
            userId: reportedUser.userId as string,
            reportCategory: category,
          },
          userReportClient.reportUser,
          reporterHeaders,
        );
      });
    }

    for (const value of getEnumValues(V3PretendingTo, [
      V3PretendingTo.PRETENDING_TO_UNSPECIFIED,
    ])) {
      it(`should return true when reportCategory is REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE, pretendingTo ${V3PretendingTo[value]}`, async () => {
        await getResponseSuccess(
          {
            userId: reportedUser.userId as string,
            reportCategory:
              V3ReportCategory.REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE,
            pretendingTo: value,
          },
          userReportClient.reportUser,
          reporterHeaders,
        );
      });
    }

    for (const value of reportReasonValues) {
      it(`should return true when reportCategory is REPORT_CATEGORY_OTHER, reportReason is ${value.title}`, async () => {
        await getResponseSuccess(
          {
            userId: reportedUser.userId as string,
            reportCategory: V3ReportCategory.REPORT_CATEGORY_OTHER,
            reportReason: value.value,
          },
          userReportClient.reportUser,
          reporterHeaders,
        );
      });
    }

    describe('After report', () => {
      //TODO https://github.com/halonext/ziichat-issues/issues/21886
      //TODO change to beforeAll
      beforeEach(async () => {
        await getResponseSuccess(
          reportUserRequest,
          userReportClient.reportUser,
          reporterHeaders,
        );
      });
      //TODO https://github.com/halonext/ziichat-issues/issues/21884
      it.skip('should return data when search users after report', async () => {
        const response = await getResponseSuccess(
          { keyword: reportedUser.username },
          searchClient.searchSearchUsers,
          reporterHeaders,
        );
        if (!response.data) throw new Error(NULL_UNDEFINED_ERROR);
        expect(response.data[0].userId).toEqual(reportedUser.userId);
      });
      //TODO https://github.com/halonext/ziichat-issues/issues/21885
      it.skip('should return true when getUserByUsername after report', async () => {
        await getResponseSuccess(
          { username: reportedUser.username },
          userViewClient.getUserByUsername,
          reporterHeaders,
        );
        await getResponseSuccess(
          { username: reporter.username },
          userViewClient.getUserByUsername,
          reportedUserHeaders,
        );
      });

      it('should return true when getUser after report', async () => {
        await getResponseSuccess(
          { userId: reportedUser.userId },
          userViewClient.getUser,
          reporterHeaders,
        );
        await getResponseSuccess(
          { userId: reporter.userId },
          userViewClient.getUser,
          reportedUserHeaders,
        );
      });

      it('should return true when sendDmMessage after report', async () => {
        await mockDmMessageData(reporterHeaders, reportedUser, 'hello');
        await mockDmMessageData(reportedUserHeaders, reporter, 'hello');
      });

      it('should return true when sendMessage to general group', async () => {
        const channel = await mockChannelData(
          { name: 'channel', workspaceId: WORKSPACE_ID },
          [reporter, reportedUser],
        );

        await mockMessageData(channel, reporter, 'hello');
        await mockMessageData(channel, reportedUser, 'hello');
      });
    });
  });

  describe('Error case', () => {
    it('should return error when self-report', async () => {
      await expectForParamInvalid(
        {
          userId: reporter.userId as string,
          reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
        },
        userReportClient.reportUser,
        reporterHeaders,
        { ...UserError.REPORT_USER, details: REPORT_USER_DETAILS[0] },
      );
    });

    it('should return error when report Bot', async () => {
      await expectForParamInvalid(
        {
          userId: ZIICHAT_BOT_USERID,
          reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
        },
        userReportClient.reportUser,
        reporterHeaders,
        { ...UserError.REPORT_USER, details: REPORT_USER_DETAILS[1] },
      );
    });

    it('should return error when report ghost user', async () => {
      await expectForParamInvalid(
        {
          userId: ZIICHAT_GHOST_USERID,
          reportCategory: V3ReportCategory.REPORT_CATEGORY_COPYRIGHT,
        },
        userReportClient.reportUser,
        reporterHeaders,
        { ...UserError.REPORT_USER, details: REPORT_USER_DETAILS[2] },
      );
    });
  });
});
