import { MaxLength } from './const';

export const NULL_UNDEFINED_ERROR =
  'Argument of type must not be [null] or [undefined]!';

export const ChannelError = {
  CREATE_CHANNEL: {
    code: 3000,
    message: 'Failed to create channel',
  },
  UPDATE_CHANNEL: {
    code: 3001,
    message: 'Failed to update channel',
  },
  DELETE_CHANNEL: {
    code: 3002,
    message: 'Failed to delete channel',
  },
  GET_CHANNEL: {
    code: 3003,
    message: 'Failed to get channel',
  },
  TYPING_IN_CHANNEL: {
    code: 3004,
    message: 'Failed to typing in channel',
  },
  RESEND_MESSAGE_REQUEST: {
    code: 3005,
    message: 'Failed to resend message request',
  },
  ACCEPT_MESSAGE_REQUEST: {
    code: 3006,
    message: 'Failed to accept message request',
  },
  REJECT_MESSAGE_REQUEST: {
    code: 3007,
    message: 'Failed to reject message request',
  },
  LIST_INCOMING_MESSAGE_REQUESTS: {
    code: 3008,
    message: 'Failed to get incoming message requests',
  },
  LIST_OUTGOING_MESSAGE_REQUESTS: {
    code: 3009,
    message: 'Failed to get outgoing message requests',
  },
  SEARCH_CHANNELS: {
    code: 3010,
    message: 'Failed to search channels',
  },
  DELETE_CHANNEL_AVATAR: {
    code: 3011,
    message: 'Failed to delete channel avatar',
  },
};

export const FriendError = {
  ADD_NEW_FRIEND: {
    code: 3300,
    message: 'Failed to add new friend',
  },
  UNFRIEND: {
    code: 3301,
    message: 'Failed to unfriend',
  },
  ACCEPT_FRIEND_REQUEST: {
    code: 3302,
    message: 'Failed to accept friend request',
  },
  CANCEL_FRIEND_REQUEST: {
    code: 3303,
    message: 'Failed to cancel friend request',
  },
  DELETE_FRIEND_REQUEST: {
    code: 3304,
    message: 'Failed to delete friend request',
  },
  GET_FRIEND_REQUEST: {
    code: 3305,
    message: 'Failed to get friend request',
  },
  GET_FRIEND: {
    code: 3306,
    message: 'Failed to get friend',
  },
  MARK_ALL_AS_READ: {
    code: 3307,
    message: 'Failed to mark all as read friend request',
  },
  SEARCH_FRIENDS: {
    code: 3308,
    message: 'Failed to search friends',
  },
};

export const InvitationError = {
  CREATE_INVITATION: {
    code: 3200,
    message: 'Failed to create invitation',
  },
  UPDATE_INVITATION: {
    code: 3201,
    message: 'Failed to update invitation',
  },
  DELETE_INVITATION: {
    code: 3202,
    message: 'Failed to delete invitation',
  },
  GET_INVITATION: {
    code: 3203,
    message: 'Failed to get invitation',
  },
  SEND_INVITATION: {
    code: 3204,
    message: 'Failed to send invitation',
  },
  ACCEPT_INVITATION: {
    code: 3205,
    message: 'Failed to accept invitation',
  },
  LIST_INVITABLE_USERS: {
    code: 3206,
    message: 'Failed to get list invitable users',
  },
};

export const MemberError = {
  CREATE_MEMBER: {
    code: 3100,
    message: 'Failed to create member',
  },
  UPDATE_MEMBER: {
    code: 3101,
    message: 'Failed to update member',
  },
  DELETE_MEMBER: {
    code: 3102,
    message: 'Failed to delete member',
  },
  GET_MEMBER: {
    code: 3103,
    message: 'Failed to get member',
  },
  LEAVE_CHANNEL: {
    code: 3104,
    message: 'Failed to leave channel',
  },
  REMOVE_FROM_CHANNEL: {
    code: 3105,
    message: 'Failed to remove from channel',
  },
  ASSIGN_AS_ADMIN: {
    code: 3106,
    message: 'Failed to assign as admin',
  },
  DISMISS_AS_ADMIN: {
    code: 3107,
    message: 'Failed to dismiss as admin',
  },
  TRANSFER_OWNERSHIP_CHANNEL: {
    code: 3108,
    message: 'Failed to transfer ownership channel',
  },
  BAN_MEMBER: {
    code: 3109,
    message: 'Failed to ban member',
  },
  UNBAN_MEMBER: {
    code: 3110,
    message: 'Failed to unban member',
  },
};

export const MessageError = {
  CREATE_MESSAGE: {
    code: 6000,
    message: 'Failed to create message',
  },
  UPDATE_MESSAGE: {
    code: 6001,
    message: 'Failed to update message',
  },
  DELETE_MESSAGE: {
    code: 6002,
    message: 'Failed to delete message',
  },
  GET_MESSAGE: {
    code: 6003,
    message: 'Failed to get message',
  },
  REPLY_MESSAGE: {
    code: 6004,
    message: 'Failed to reply message',
  },
  ADD_MESSAGE_REACTION: {
    code: 6005,
    message: 'Failed to add message reaction',
  },
  REVOKE_MESSAGE_REACTION: {
    code: 6006,
    message: 'Failed to revoke message reaction',
  },
  GET_REACTION: {
    code: 6007,
    message: 'Failed to get reaction',
  },
  SEND_ATTACHMENT_MESSAGE: {
    code: 6008,
    message: 'Failed to send attachment message',
  },
  FORWARD_MESSAGE: {
    code: 6009,
    message: 'Failed to forward message',
  },
  REPORT_MESSAGE: {
    code: 6010,
    message: 'Failed to report message',
  },
  DELETE_MESSAGES: {
    code: 6011,
    message: 'Failed to delete messages',
  },
  UPDATE_ATTACHMENT: {
    code: 6012,
    message: 'Failed to update attachment',
  },
  SEND_STICKER: {
    code: 6013,
    message: 'Failed to send sticker',
  },
  QUOTE_MESSAGE: {
    code: 6014,
    message: 'Failed to quote message',
  },
  PERMISSION_TIME_LIMIT_EXCEEDED: {
    code: 6015,
    message: 'The time limit for permission has been exceeded',
  },
  SPEECH_TO_TEXT: {
    code: 6016,
    message: 'Failed to transcribe speech to text',
  },
  TRANSLATE_TEXT: {
    code: 6017,
    message: 'Failed to translate text',
  },
  TEXT_TO_SPEECH: {
    code: 6018,
    message: 'Failed to transcribe text to speech',
  },
  SEND_LOCATION_MESSAGE: {
    code: 6019,
    message: 'Failed to send location message',
  },
  SEND_POKE_MESSAGE: {
    code: 6020,
    message: 'Failed to send poke message',
  },
  PIN_MESSAGE: {
    code: 6021,
    message: 'Failed to pin message',
  },
  UNPIN_MESSAGE: {
    code: 6022,
    message: 'Failed to unpin message',
  },
  MAX_MESSAGE_LIMIT_FOR_STRANGERS: {
    code: 6023,
    message:
      'You’ve reached the maximum message limit for strangers. Please wait for this person to accept your message before continuing the conversation',
  },
  MARK_AS_READ: {
    code: 6024,
    message: 'Failed to mark as read',
  },
  MARK_ALL_CHANNELS_AS_READ: {
    code: 6025,
    message: 'Failed to mark all channels as read',
  },
};

export const StickerError = {
  CREATE_COLLECTION: {
    code: 6100,
    message: 'Failed to create collection',
  },
  UPDATE_COLLECTION: {
    code: 6101,
    message: 'Failed to update collection',
  },
  DELETE_COLLECTION: {
    code: 6102,
    message: 'Failed to delete collection',
  },
  GET_COLLECTION: {
    code: 6103,
    message: 'Failed to get collection',
  },
  LIST_COLLECTIONS: {
    code: 6104,
    message: 'Failed to get list collections',
  },
  CREATE_STICKER: {
    code: 6105,
    message: 'Failed to create sticker',
  },
  UPDATE_STICKER: {
    code: 6106,
    message: 'Failed to update sticker',
  },
  DELETE_STICKER: {
    code: 6107,
    message: 'Failed to delete sticker',
  },
  GET_STICKER: {
    code: 6108,
    message: 'Failed to get sticker',
  },
  LIST_STICKERS: {
    code: 6109,
    message: 'Failed to get list stickers',
  },
  SEARCH_STICKERS: {
    code: 6110,
    message: 'Failed to search stickers',
  },
};

export const UserError = {
  GET_USER: {
    code: 7000,
    message: 'Failed to get user',
  },
  REPORT_USER: {
    code: 7050,
    message: 'Failed to report user',
  },
  UPDATE_USER_AVATAR: {
    code: 7051,
    message: 'Failed to update user avatar',
  },
  UPDATE_USER_DISPLAY_NAME: {
    code: 7052,
    message: 'Failed to update user display name',
  },
  GENERATE_USER_CONNECT_LINK: {
    code: 7053,
    message: 'Failed to generate a user connect link',
  },
  DECODE_USER_CONNECT_LINK: {
    code: 7054,
    message: 'Failed to decode user connect link',
  },
  CREATE_USER_DATA_RECORD: {
    code: 7055,
    message: 'Failed to create a user data record',
  },
  REMOVE_USER_DATA_RECORD: {
    code: 7056,
    message: 'Failed to remove user data record',
  },
  BLOCK_USER: {
    code: 7057,
    message: 'Failed to block user',
  },
  UNBLOCK_USER: {
    code: 7058,
    message: 'Failed to unblock user',
  },
  LIST_BLOCKED_USERS: {
    code: 7059,
    message: 'Failed to get list blocked users',
  },
  SEARCH_USERS: {
    code: 7060,
    message: 'Failed to search users',
  },
  UPDATE_RECOVERY_CODE_SETTING: {
    code: 7061,
    message: 'Failed to update recovery code setting',
  },
  UPDATE_SMART_OTP_SETTING: {
    code: 7062,
    message: 'Failed to update smart OTP setting',
  },
  UPDATE_USER_PHONE: {
    code: 7063,
    message: 'Failed to update user phone',
  },
  UPDATE_USER_EMAIL: {
    code: 7064,
    message: 'Failed to update user email',
  },
  UPDATE_MEDIA_PERMISSION_SETTING: {
    code: 7065,
    message: 'Failed to update media permission setting',
  },
  USER_REMOVED: {
    code: 7100,
    message: 'This user has been removed',
  },
  USER_BANNED: {
    code: 7101,
    message: 'This user has been banned',
  },
  GET_PRIVATE_USER_DATA: {
    code: 7102,
    message: 'Failed to get private user data',
  },
  ADD_USER_STATUS: {
    code: 7103,
    message: 'Failed to add user status',
  },
  UPDATE_USER_STATUS: {
    code: 7104,
    message: 'Failed to update user status',
  },
  DELETE_USER_STATUS: {
    code: 7105,
    message: 'Failed to delete user status',
  },
  LIST_USER_STATUS: {
    code: 7106,
    message: 'Failed to list user status',
  },
  LIST_VIEWED_PROFILE: {
    code: 7107,
    message: 'Failed to list user viewed profile',
  },
  CREATE_RINGBACK_TONE: {
    code: 7108,
    message: 'Failed to create ringback tone',
  },
  RENAME_RINGBACK_TONE: {
    code: 7109,
    message: 'Failed to rename ringback tone',
  },
  DELETE_RINGBACK_TONE: {
    code: 7110,
    message: 'Failed to delete ringback tone',
  },
  SET_RINGBACK_TONE: {
    code: 7111,
    message: 'Failed to set ringback tone',
  },
  LIST_RINGBACK_TONES: {
    code: 7112,
    message: 'Failed to list ringback tones',
  },
  GET_RINGBACK_TONE: {
    code: 7113,
    message: 'Failed to get ringback tone',
  },
  CREATE_COVER_PHOTO: {
    code: 7114,
    message: 'Failed to create cover photo',
  },
  UPDATE_COVER_PHOTO: {
    code: 7115,
    message: 'Failed to update cover photo',
  },
  DELETE_COVER_PHOTO: {
    code: 7116,
    message: 'Failed to delete cover photo',
  },
  DELETE_USER_AVATAR: {
    code: 7117,
    message: 'Failed to delete user avatar',
  },
  UPDATE_USER_VIDEO_AVATAR: {
    code: 7118,
    message: 'Failed to update user video avatar',
  },
  DELETE_USER_VIDEO_AVATAR: {
    code: 7119,
    message: 'Failed to delete user video avatar',
  },
  CREATE_AVATAR_FRAME: {
    code: 7120,
    message: 'Failed to create avatar frame',
  },
  DELETE_AVATAR_FRAME: {
    code: 7121,
    message: 'Failed to delete avatar frame',
  },
  UPLOAD_DECORATED_AVATAR: {
    code: 7122,
    message: 'Failed to upload decorated avatar',
  },
  GET_AVATAR_FRAME: {
    code: 7123,
    message: 'Failed to get avatar frame',
  },
  LIST_AVATAR_FRAME: {
    code: 7124,
    message: 'Failed to list avatar frame',
  },
  REMOVE_DECORATED_AVATAR: {
    code: 7125,
    message: 'Failed to remove decorated avatar',
  },
  UPDATE_USER_SCOPE_CALL: {
    code: 7126,
    message: 'Failed to update user scope for call',
  },
  UPDATE_USER_SCOPE_MESSAGE: {
    code: 7127,
    message: 'Failed to update user scope for message',
  },
  LIST_PRIVATE_USER_DATA: {
    code: 7128,
    message: 'Failed to list private user data',
  },
  VISITED_PROFILE: {
    code: 7129,
    message: 'Failed to visited profile',
  },
  DELETE_VISITED_PROFILE: {
    code: 7130,
    message: 'Failed to delete user visited profile',
  },
  CLEAR_VISITED_PROFILE_NOTIFICATION: {
    code: 7131,
    message: 'Failed to clear user visited profile notification',
  },
};

export const INVALID_ARGUMENT = 'Invalid argument';
export const UNAUTHORIZED_REQUEST = 'Unauthorized request';
export const CHANNEL_INVALID = ['Channel invalid'];

export const EXPECTED_VALIDATION_ERROR = {
  code: 1000,
  message: INVALID_ARGUMENT,
  details: [] as string[],
};

export const UPDATE_DISPLAY_NAME_DETAILS = [
  ['Unable update with the same name'],
  [`displayName max ${MaxLength.DISPLAY_NAME} length`],
];

export const DELETE_USER_AVATAR_DETAILS = [['Not found avatar']];
export const DELETE_USER_VIDEO_AVATAR_DETAILS = [['Not found video avatar']];

export const VISITED_PROFILE_DETAILS = [
  ['visited profile limit time has been exceeded'],
];

export const ADD_USER_STATUS_DETAILS = [['User status are already exists']];

export const UPDATE_USER_STATUS_DETAILS = [
  ['Unable update with the same content and status'],
  ['Unable update with the same content'],
  ['Unable update with the same status'],
  ['User status does not exist'],
];

export const STATUS_ERRORS = [
  'status invalid Emoji format',
  'status must to length equals 1',
];

export const CREATE_CHANNEL_DETAILS = [
  ['Can not create DM Channel'],
  ['Only users with a new badge can create broadcast channel'],
];

export const UPDATE_CHANNEL_DETAILS = [
  ['Invalid user permission'],
  ['Unable update with the same name'],
];
export const DELETE_CHANNEL_AVATAR_DETAILS = [['Channel avatar not found']];

export const ADD_COVER_PHOTO_DETAILS = [['Cover is exists']];
export const UPDATE_COVER_PHOTO_DETAILS = [['Cover is not exists']];

export const MESSAGE_REQUEST_DETAILS = [
  ['Message request not exists'],
  ['Unable to update the contact dm channel'],
];

export const REPORT_USER_DETAILS = [
  ['Not allowed to self-report'],
  ["Can't report bot user"],
  ["Can't report ghost user"],
];

export const GET_DM_CHANNEL_DETAILS = [['This dm channel has not been exists']];

export const BLOCK_USER_DETAILS = [
  ['Invalid target user'],
  ['Not allowed to self-block'],
];

export const GET_USER_DETAILS = [
  ['You cannot get yourself'],
  ['Username invalid'],
];

export const ADD_FRIEND_DETAILS = [['Invalid User']];
export const ACCEPT_FRIEND_REQUEST_DETAILS = [
  ['Both of you are already friends'],
];
export const CANCEL_FRIEND_REQUEST_DETAILS = [
  ['Could not cancel this friend request'],
];
export const DELETE_FRIEND_REQUEST_DETAILS = [
  ['Could not delete this friend request'],
];
export const UNFRIEND_DETAILS = [['userId cannot unfriend this user.']];

export const GET_FRIEND_DETAILS = [['Friend data not found']];
