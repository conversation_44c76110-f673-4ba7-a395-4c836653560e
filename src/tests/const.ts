import { V3ChannelPermissionsEnum } from '../../utils/http-client/commands-chat-client';

export enum Environment {
  SB = 'Sandbox',
  LIVE = 'Live',
}

export type HEADERS = Record<string, unknown>;

// Config host
export const PREFIX_HOST = 'zc://';
export const CONFIG_AVATAR_HOST = 'https://avatars-sb.ziicdn.net/';
export const CONFIG_STICKER_HOST = 'https://stickers-sb.ziicdn.net/';
export const CONFIG_FILE_STORE_HOST = 'https://fs.ugc.ziicdn.net/';
export const PREFIX_SHORT_LINK = 'https://sb11.zii.chat/s/';
export const PREFIX_INVITATION_LINK = 'https://zii.chat/i/';
export const PREFIX_MENTION_LINK = 'https://sb.zii.chat/u/';
export const PREFIX_CONNECT_LINK = 'https://zii.chat/connect//';

// ZiiChat bot

export const ZIICHAT_BOT_USERID = '01GNNA1J000000000000000000';
export const ZIICHAT_BOT_USERNAME = 'ZiiChat';
export const WELCOME_MESSAGE =
  "Welcome to ZiiChat! We're glad to meet you here. ZiiChat offers a unique experience which enables you to share your moments promptly through short-form videos in a safer and easier way. Also, QR codes make it simple and quick to meet new people.";

// ZiiChat ghost
export const ZIICHAT_GHOST_USERID = '01GNNA1J000000000000000001';

export const REDIRECTION_MESSAGES = 'REDIRECTION_MESSAGES';

// CloudEvent Websocket
export enum cloudEventType {
  // Gateway
  GATEWAY_CONNECTED_EVENT = 'com.halome.realtime.v3.gateway.connected',
  // Resume websocket
  RECONNECT_STARTED_EVENT = 'com.halome.websocket.v3.reconnection_started',
  RECONNECT_ENDED_EVENT = 'com.halome.websocket.v3.reconnection_ended',
  // Message request
  INCOMING_MESSAGE_REQUEST_CREATED_EVENT = 'com.halome.chat.v3.incoming_message_request.created',
  OUTGOING_MESSAGE_REQUEST_CREATED_EVENT = 'com.halome.chat.v3.outgoing_message_request.created',
  INCOMING_MESSAGE_REQUEST_ACCEPTED_EVENT = 'com.halome.chat.v3.incoming_message_request.accepted',
  OUTGOING_MESSAGE_REQUEST_ACCEPTED_EVENT = 'com.halome.chat.v3.outgoing_message_request.accepted',
  MESSAGE_REQUEST_ACCEPTED_EVENT = 'com.halome.chat.v3.message_request.accepted', // api changed logic, remove this event
  MESSAGE_REQUEST_REJECTED_EVENT = 'com.halome.chat.v3.message_request.rejected',
  // Message
  MESSAGE_CREATED_EVENT = 'com.halome.chat.v3.message.created',
  MESSAGE_UPDATED_EVENT = 'com.halome.chat.v3.message.updated',
  MESSAGES_DELETED_EVENT = 'com.halome.chat.v3.message.deleted',
  USER_MESSAGE_DELETED_EVENT = 'com.halome.chat.v3.user_message.deleted', // delete messages only me
  ALL_MESSAGES_DELETED_EVENT = 'com.halome.chat.v3.all_messages.deleted',
  ALL_USER_MESSAGES_DELETED_EVENT = 'com.halome.chat.v3.all_user_messages.deleted', // delete all messages only me
  MESSAGE_REACTION_UPDATED_EVENT = 'com.halome.chat.v3.message.reaction_updated',
  USER_MESSAGE_REACTION_UPDATED_EVENT = 'com.halome.chat.v3.message.user_reaction_updated',
  UNREAD_MESSAGES_UPDATED_EVENT = 'com.halome.chat.v3.unread_messages.updated',
  // Friend request
  INCOMING_FRIEND_REQUEST_DELETED_EVENT = 'com.halome.chat.v3.incoming_friend_request.deleted',
  OUTGOING_FRIEND_REQUEST_DELETED_EVENT = 'com.halome.chat.v3.outgoing_friend_request.deleted',
  INCOMING_FRIEND_REQUEST_CANCELED_EVENT = 'com.halome.chat.v3.incoming_friend_request.canceled',
  OUTGOING_FRIEND_REQUEST_CANCELED_EVENT = 'com.halome.chat.v3.outgoing_friend_request.canceled',
  INCOMING_FRIEND_REQUEST_CREATED_EVENT = 'com.halome.chat.v3.incoming_friend_request.created',
  OUTGOING_FRIEND_REQUEST_CREATED_EVENT = 'com.halome.chat.v3.outgoing_friend_request.created',
  INCOMING_FRIEND_REQUEST_ACCEPTED_EVENT = 'com.halome.chat.v3.incoming_friend_request.accepted',
  OUTGOING_FRIEND_REQUEST_ACCEPTED_EVENT = 'com.halome.chat.v3.outgoing_friend_request.accepted',

  // Friend
  FRIEND_UNFRIENDED_EVENT = 'com.halome.chat.v3.friend.unfriended',
  // Channel
  CHANNEL_CREATED_EVENT = 'com.halome.chat.v3.channel.created',
  DM_CHANNEL_CREATED_EVENT = 'com.halome.chat.v3.dm_channel.created',
  CHANNEL_DELETED_EVENT = 'com.halome.chat.v3.channel.deleted',
  CHANNEL_UPDATED_EVENT = 'com.halome.chat.v3.channel.updated',
  DM_CHANNEL_MEDIA_PERMISSION_SETTING_UPDATED = 'com.halome.chat.v3.dm_channel.updated',
  // Invitation
  INVITATION_SENT_EVENT = 'com.halome.chat.v3.invitation.sent',
  // Member
  MEMBER_REMOVED_EVENT = 'com.halome.chat.v3.member.removed',
  MEMBER_JOINED_EVENT = 'com.halome.chat.v3.member.joined',
  MEMBER_ROLE_UPDATED_EVENT = 'com.halome.chat.v3.member.role_updated',
  MEMBER_NICKNAME_UPDATED_EVENT = 'com.halome.chat.v3.member.nickname_updated',
  MEMBER_BANNED_EVENT = 'com.halome.chat.v3.member.banned',
  MEMBER_ROLE_REVOKED_EVENT = 'com.halome.chat.v3.member.role_revoked',
  MEMBER_LEFT_EVENT = 'com.halome.chat.v3.member.left',
  // User
  USER_AVATAR_UPDATED_EVENT = 'com.halome.user.v3.profile.avatar_updated',
  USER_AVATAR_DELETED_EVENT = 'com.halome.user.v3.profile.avatar_deleted',
  USER_BLOCKED_EVENT = 'com.halome.iam.v3.user.blocked',
  USER_UNBLOCKED_EVENT = 'com.halome.iam.v3.user.unblocked',
  PROFILE_DISPLAY_NAME_UPDATED_EVENT = 'com.halome.user.v3.profile.display_name_updated',
  USER_GLOBAL_MEDIA_PERMISSION_SETTING_UPDATED_EVENT = 'com.halome.user.v3.setting.global_media_permission_setting_updated',
  PROFILE_USER_STATUS_CREATED = 'com.halome.user.v3.profile.user_status_created',
  PROFILE_USER_STATUS_UPDATED = 'com.halome.user.v3.profile.user_status_updated',
  PROFILE_USER_STATUS_DELETED = 'com.halome.user.v3.profile.user_status_deleted',
  VISITED_PROFILE_EVENT = 'com.halome.user.v3.profile.visited_profile',
  VISITED_PROFILE_DELETED_EVENT = 'com.halome.user.v3.profile.visited_profile_deleted',
  CLEAR_USER_VISITED_PROFILE_NOTIFICATIONS_EVENT = 'com.halome.user.v3.profile.clear_user_visited_profile_notifications',
  COVER_CREATED_EVENT = 'com.halome.user.v3.profile.cover_created',
  COVER_UPDATED_EVENT = 'com.halome.user.v3.profile.cover_updated',
  COVER_DELETED_EVENT = 'com.halome.user.v3.profile.cover_deleted',
  USER_SCOPE_FOR_CALL_UPDATED = 'com.halome.user.v3.setting.user_scope_for_call_updated',
  USER_SCOPE_FOR_MESSAGE_UPDATED = 'com.halome.user.v3.setting.user_scope_for_message_updated',
  // Presence_data
  PRESENCE_UPDATED_EVENT = 'com.halome.realtime.v3.presence.updated',

  // Get Private Data
  PRIVATE_DATA_SYNC_EVENT = 'com.halome.user.v3.private_data_sync',

  // WS File Upload
  FILE_STORE_INIT_FILE_UPLOAD = 'com.halome.file_store.initFileUpload',
  FILE_STORE_COMPLETE_FILE_UPLOAD = 'com.halome.file_store.completeFileUpload',
  FILE_STORE_INIT_FILE_CHUNK_UPLOAD = 'com.halome.file_store.initFileChunkUpload',
  FILE_STORE_FILE_CHUNK_UPLOAD = 'com.halome.file_store.fileChunkUpload',
  FILE_STORE_COMPLETE_CHUNK_UPLOAD = 'com.halome.file_store.completeFileChunkUpload',
  FILE_STORE_GET_UPLOAD_CHUNKS = 'com.halome.file_store.getUploadChunks',

  // Avatar Frame
  DECORATED_AVATAR_UPLOADED_EVENT = 'com.halome.user.v3.profile.decorated_avatar_uploaded',
  DECORATED_AVATAR_REMOVE_EVENT = 'com.halome.user.v3.profile.decorated_avatar_removed',

  // Pinned message
  MESSAGE_PINNED_EVENT = 'com.halome.chat.v3.message.pinned',
  MESSAGE_UNPINNED_EVENT = 'com.halome.chat.v3.message.unpinned',
}

// CloudEvent Source
export const CLOUD_EVENT_SOURCE_SYSTEM =
  'messages.v3.halome.com/cloudevents/system';

export enum Methods {
  //Faker
  MOCK_USERS = 'MockUsers',

  //User profile
  UPDATE_USER_AVATAR = 'UpdateUserAvatar',
  DELETE_USER_AVATAR = 'DeleteUserAvatar',
  UPDATE_USER_DISPLAY_NAME = 'UpdateUserDisplayName',
  CLEAR_USER_VISITED_PROFILE_NOTIFICATIONS = 'ClearUserVisitedProfileNotifications',
  DELETE_USER_VISITED_PROFILE = 'DeleteUserVisitedProfile',
  VISITED_PROFILE = 'VisitedProfile',
  ADD_USER_STATUS = 'AddUserStatus',
  UPDATE_USER_STATUS = 'UpdateUserStatus',
  DELETE_USER_STATUS = 'DeleteUserStatus',
  UPDATE_USER_VIDEO_AVATAR = 'UpdateUserVideoAvatar',
  DELETE_USER_VIDEO_AVATAR = 'DeleteUserVideoAvatar',
  ADD_COVER_PHOTO = 'AddCoverPhoto',
  UPDATE_COVER_PHOTO = 'UpdateCoverPhoto',
  DELETE_COVER_PHOTO = 'DeleteCoverPhoto',

  //User report
  REPORT_USER = 'ReportUser',

  //User setting
  BLOCK_USER = 'BlockUser',
  UNBLOCK_USER = 'UnblockUser',

  //User view
  GET_ME = 'GetMe',
  GET_USER = 'GetUser',
  GET_USER_BY_USERNAME = 'GetUserByUsername',
  LIST_BLOCKED_USERS = 'ListBlockedUsers',
  SYNC_USERS = 'SyncUsers',

  //Avatar frame
  UPLOAD_DECORATED_AVATAR = 'UploadDecoratedAvatar',
  REMOVE_DECORATED_AVATAR = 'RemoveDecoratedAvatar',

  // Channel
  CREATE_CHANNEL = 'CreateChannel',
  UPDATE_CHANNEL_NAME = 'UpdateChannelName',
  UPDATE_CHANNEL_AVATAR = 'UpdateChannelAvatar',
  DELETE_CHANNEL_AVATAR = 'DeleteChannelAvatar',
  DELETE_CHANNEL = 'DeleteChannel',
  ACCEPT_MESSAGE_REQUEST = 'AcceptMessageRequest',
  REJECT_MESSAGE_REQUEST = 'RejectMessageRequest',

  // ChannelView
  GET_CHANNEL = 'GetChannel',
  GET_DM_CHANNEL = 'GetDMChannel',
  LIST_DM_CHANNELS = 'ListDMChannels',
  LIST_CHANNELS = 'ListChannels',
  LIST_ALL_CHANNELS = 'ListAllChannels',
  LIST_INCOMING_MESSAGE_REQUESTS = 'ListInComingMessageRequests',
  LIST_OUTGOING_MESSAGE_REQUESTS = 'ListOutGoingMessageRequests',

  // Invitation
  CREATE_INVITATION = 'CreateInvitation',
  REVOKE_INVITATION = 'RevokeInvitation',
  SEND_INVITATION = 'SendInvitation',
  ACCEPT_INVITATION = 'AcceptInvitation',

  // InvitationView
  GET_INVITATION = 'GetInvitation',
  LIST_INVITABLE_USERS = 'ListInvitableUsers',
  LIST_INVITATIONS = 'ListInvitations',

  // Member
  UPDATE_NICKNAME = 'UpdateNickname',
  LEAVE_CHANNEL = 'LeaveChannel',
  REMOVE_FROM_CHANNEL = 'RemoveFromChannel',
  ASSIGN_AS_ADMIN = 'AssignAsAdmin',
  DISMISS_AS_ADMIN = 'DismissAsAdmin',
  BAN_FROM_CHANNEL = 'BanFromChannel',
  UNBAN_FROM_CHANNEL = 'UnBanFromChannel',
  TRANSFER_OWNERSHIP = 'TransferOwnership',
  TRANSFER_OWNERSHIP_AND_LEAVE_CHANNEL = 'TransferOwnershipAndLeaveChannel',

  // MemberView
  GET_MEMBER = 'GetMember',
  LIST_MEMBERS = 'ListMembers',
  LIST_BANNED_USERS = 'ListBannedUsers',

  // Friend
  ADD_FRIEND = 'AddFriend',
  ACCEPT_FRIEND_REQUEST = 'AcceptFriendRequest',
  CANCEL_FRIEND_REQUEST = 'CancelFriendRequest',
  DELETE_FRIEND_REQUEST = 'DeleteFriendRequest',
  UNFRIEND = 'Unfriend',
  MARK_ALL_AS_READ = 'MarkAllAsRead',

  // FriendView
  GET_FRIEND = 'GetFriend',
  LIST_FRIENDS = 'ListFriends',
  LIST_INCOMING_FRIEND_REQUESTS = 'ListInComingFriendRequests',
  LIST_OUTGOING_FRIEND_REQUESTS = 'ListOutGoingFriendRequests',

  // Message
  SEND_MESSAGE = 'SendMessage',
  SEND_DM_MESSAGE = 'SendDMMessage',
  UPDATE_MESSAGE = 'UpdateMessage',
  UPDATE_DM_MESSAGE = 'UpdateDMMessage',
  ADD_MESSAGE_REACTION = 'AddMessageReaction',
  ADD_DM_MESSAGE_REACTION = 'AddDMMessageReaction',
  REVOKE_MESSAGE_REACTION = 'RevokeMessageReaction',
  REVOKE_DM_MESSAGE_REACTION = 'RevokeDMMessageReaction',
  SEND_MESSAGE_MEDIA = 'SendMessageMedia',
  SEND_DM_MESSAGE_MEDIA = 'SendDmMessageMedia',
  UPDATE_MEDIA_ATTACHMENTS = 'UpdateMediaAttachments',
  UPDATE_DM_MEDIA_ATTACHMENTS = 'UpdateDmMediaAttachments',
  QUOTE_MESSAGE = 'QuoteMessage',
  QUOTE_DM_MESSAGE = 'QuoteDMMessage',
  FORWARD_MESSAGES_TO_CHANNEL = 'ForwardMessagesToChannel',
  FORWARD_MESSAGES_TO_DM_CHANNEL = 'ForwardMessagesToDMChannel',
  REPORT_MESSAGE = 'ReportMessage',
  REPORT_DM_MESSAGE = 'ReportDMMessage',
  MARK_AS_READ = 'MarkAsRead',
  MARK_DM_AS_READ = 'MarkDMAsRead',
  SEND_MESSAGE_STICKER = 'SendMessageSticker',
  SEND_DM_MESSAGE_STICKER = 'SendDMMessageSticker',
  SEND_LOCATION = 'SendLocation',
  SEND_DM_LOCATION = 'SendDMLocation',
  SEND_POKE_MESSAGE = 'SendPokeMessage',
  DELETE_MESSAGES_FOR_EVERYONE = 'DeleteMessagesForEveryone',
  DELETE_MESSAGES_ONLY_ME = 'DeleteMessagesOnlyMe',
  DELETE_ALL_MESSAGES_ONLY_ME = 'DeleteAllMessagesOnlyMe',
  DELETE_ALL_DM_MESSAGES_FOR_EVERYONE = 'DeleteAllDMMessagesForEveryone',
  DELETE_ALL_DM_MESSAGES_ONLY_ME = 'DeleteAllDMMessagesOnlyMe',
  CLEAR_DM_MESSAGES_FOR_EVERYONE = 'ClearDMMessagesForEveryone',
  CLEAR_DM_MESSAGES_ONLY_ME = 'ClearDMMessagesOnlyMe',
  DELETE_DM_MESSAGES_FOR_EVERYONE = 'DeleteDMMessagesForEveryone',
  DELETE_DM_MESSAGES_ONLY_ME = 'DeleteDMMessagesOnlyMe',
  PIN_UNPIN_MESSAGE = 'PinUnpinMessage',
  PIN_UNPIN_DM_MESSAGE = 'PinUnpinDMMessage',

  // MessageView
  GET_MESSAGE = 'GetMessage',
  GET_DM_MESSAGE = 'GetDMMessage',
  LIST_MESSAGES = 'ListMessages',
  LIST_DM_MESSAGES = 'ListDMMessages',
  JUMP_TO_MESSAGE = 'JumpToMessage',
  JUMP_TO_DM_MESSAGE = 'JumpToDMMessage',
  LIST_MESSAGE_REACTIONS = 'ListMessageReactions',
  LIST_DM_MESSAGE_REACTIONS = 'ListDMMessageReactions',
  LIST_MESSAGE_FRAGMENTS = 'ListMessageFragments',
  LIST_DM_MESSAGE_FRAGMENTS = 'ListDMMessageFragments',
  GET_PINNED_MESSAGE = 'GetPinnedMessage',
  GET_PINNED_DM_MESSAGE = 'GetPinnedDMMessage',

  // StickerView
  GET_STICKER = 'GetSticker',
  LIST_STICKERS = 'ListStickers',
  LIST_ALL_STICKER_COLLECTIONS = 'ListAllStickerCollections',
  GET_STICKER_COLLECTION = 'GetStickerCollection',
  SYNC_STICKER_COLLECTIONS = 'SyncStickerCollections',
}

export enum MaxLength {
  DISPLAY_NAME = 50,
  CHANNEL_NAME = 50,
  REPORT_REASON = 255,
}

export enum MinLength {
  CHANNEL_NAME = 3,
}

export const MAX_LENGTH_CONTENT_STATUS = 50;

export const STATUS_EMOJI_LIST = [
  '❤️',
  '😂',
  '🥲',
  '😡',
  '😮',
  '🥰',
  '🔥',
  '😋',
  '😈',
];

export const WORKSPACE_ID = '0';

export const OWNER_PERMISSIONS = [
  V3ChannelPermissionsEnum.OWNER,
  V3ChannelPermissionsEnum.CHANNELS__VIEW_CHANNEL,
  V3ChannelPermissionsEnum.CHANNELS__MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__MEMBERS_MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__STICKERS_MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__INVITATIONS_MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__INVITATIONS_CREATE,
  V3ChannelPermissionsEnum.CHANNELS__VIEW_AUDIT_LOGS,
  V3ChannelPermissionsEnum.MESSAGES__MANAGE,
  V3ChannelPermissionsEnum.MESSAGES__VIEW,
  V3ChannelPermissionsEnum.MESSAGES__SEND_MESSAGE,
  V3ChannelPermissionsEnum.MESSAGES__SEND_ATTACHMENTS,
  V3ChannelPermissionsEnum.MESSAGES__EMBED_LINKS,
  V3ChannelPermissionsEnum.MESSAGES__MENTION_EVERYONE,
];

export const ADMIN_PERMISSIONS = [
  V3ChannelPermissionsEnum.CHANNELS__VIEW_CHANNEL,
  V3ChannelPermissionsEnum.CHANNELS__MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__MEMBERS_MANAGE,
  V3ChannelPermissionsEnum.CHANNELS__INVITATIONS_CREATE,
  V3ChannelPermissionsEnum.CHANNELS__VIEW_AUDIT_LOGS,
  V3ChannelPermissionsEnum.MESSAGES__MANAGE,
  V3ChannelPermissionsEnum.MESSAGES__VIEW,
  V3ChannelPermissionsEnum.MESSAGES__SEND_MESSAGE,
  V3ChannelPermissionsEnum.MESSAGES__SEND_ATTACHMENTS,
  V3ChannelPermissionsEnum.MESSAGES__EMBED_LINKS,
  V3ChannelPermissionsEnum.MESSAGES__MENTION_EVERYONE,
];

export const MEMBER_PERMISSIONS = [
  V3ChannelPermissionsEnum.CHANNELS__VIEW_CHANNEL,
  V3ChannelPermissionsEnum.CHANNELS__INVITATIONS_CREATE,
  V3ChannelPermissionsEnum.MESSAGES__VIEW,
  V3ChannelPermissionsEnum.MESSAGES__SEND_MESSAGE,
  V3ChannelPermissionsEnum.MESSAGES__SEND_ATTACHMENTS,
  V3ChannelPermissionsEnum.MESSAGES__EMBED_LINKS,
];

export const MEMBER_BROADCAST_PERMISSIONS = [
  V3ChannelPermissionsEnum.CHANNELS__VIEW_CHANNEL,
  V3ChannelPermissionsEnum.MESSAGES__VIEW,
];

export enum SystemMessage {
  CREATE_CHANNEL = '%s created this channel',
  CREATE_BROADCAST_CHANNEL = 'Channel created',
  UPDATE_CHANNEL_NAME = '%s changed this channel name to %s',
  UPDATE_CHANNEL_AVATAR = '%s changed this channel avatar',
  ASSIGN_ADMIN = '%s assigned %s as an admin',
  DISMISS_ADMIN = '%s removed the admin right of %s',
  REMOVE_FROM_CHANNEL = '%s removed %s from this channel',
  LEFT_CHANNEL = '%s left this channel',
  TRANSFER_OWNERSHIP = '%s transferred channel ownership to %s',
  REMOVE_THE_NICKNAME = '%s removed the nickname of %s',
  SET_NICKNAME = '%s set a nickname for %s to %s',
  SEND_INVITATION = '%s sent an invitation ',
  JOINED_THIS_CHANNEL = '%s joined this channel',
  DELETE_CHANNEL_AVATAR = "%s removed the channel's profile avatar",
  PINNED_MESSAGE = '%s pinned a message.',
  UNPINNED_MESSAGE = '%s unpinned a message.',
}

export const CONTENT_LOCALE_DEFAULT = 'UNS';
export const CONTENT_LOCALE_EN = 'en';
export const CONTENT_LOCALE_EN_US = 'en-US';

export const LOCATION_DEFAULT_CONTENT = '📍 Location';
export const PHOTO_DEFAULT_CONTENT = '📷 Photo(s)';
export const VIDEO_DEFAULT_CONTENT = '📷 Video(s)';
export const AUDIO_DEFAULT_CONTENT = '🔊 Audio';
export const ZIIVOICE_DEFAULT_CONTENT = '🎙️Ziivoice';
export const ZIISHORT_DEFAULT_CONTENT = '🎬 Ziishort';
export const MEDIA_DEFAULT_CONTENT = '📷 Photos and videos';
export const FILE_DEFAULT_CONTENT = '📄 File';
export const POKE_DEFAULT_CONTENT = '👉 Poked';

export const WS_VERSION = '2.0';

//Limit
export const MAX_LIMIT = 500;
export const MIN_LIMIT = 1;

export const ListTimeSync = [
  '(update_time_after < updateTime)',
  '(update_time_after = updateTime)',
  '(update_time_after > updateTime)',
];

export const ListTimeSyncWithoutEqual = [
  '(update_time_after < updateTime)',
  '(update_time_after > updateTime)',
];

// Invitation
export const EXPIRES_IN_DEFAULT = 86400;
export const MAX_USES_DEFAULT = 1000;
