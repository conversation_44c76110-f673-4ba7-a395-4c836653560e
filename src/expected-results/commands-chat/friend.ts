import {
  V3<PERSON>riend,
  V3FriendStatusEnum,
  V3User,
} from '../../../utils/http-client/cloudevent-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import friendClient from '../../helpers/friend-service';
import {
  assertIsDate,
  assertIsString,
  createHeaders,
  getResponseSuccess,
} from '../../helpers/shared/common';
import { FriendDataWS, UserWS } from '../../helpers/websocket-service';
import { cloudEventType, WS_VERSION } from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';
import {
  expectFriendRequestAcceptedEventData,
  expectFriendRequestCanceledEventData,
  expectFriendRequestCreatedEventData,
  expectGatewayConnectedEvent,
  expectResumeWS,
  expectSourceFromUser,
} from '../realtime/websocket';
import { expectIncludesUsers } from '../views-chat/user-view';

export const expectAddFriendEvents = async (
  member: UserWS,
  actorSource: string,
  getFriendResList: FriendDataWS[],
  expectedEvents: cloudEventType[],
): Promise<void> => {
  const INDEX_EVENT_TWICE = 3;
  const { user, events } = member;

  expect(events).toHaveLength(expectedEvents.length);
  for (const [index, event] of events.entries()) {
    expect(event.type).toEqual(expectedEvents[index]);
    expect(event.version).toEqual(WS_VERSION);

    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(user, event.data);
        break;

      case cloudEventType.OUTGOING_FRIEND_REQUEST_CREATED_EVENT:
      case cloudEventType.INCOMING_FRIEND_REQUEST_CREATED_EVENT:
        expectSourceFromUser(actorSource, event.source);
        await expectFriendRequestCreatedEventData(
          index == INDEX_EVENT_TWICE
            ? getFriendResList[1]
            : getFriendResList[0],
          event.data,
        );
        break;

      case cloudEventType.OUTGOING_FRIEND_REQUEST_CANCELED_EVENT:
      case cloudEventType.INCOMING_FRIEND_REQUEST_CANCELED_EVENT:
        expectSourceFromUser(actorSource, event.source);
        getFriendResList[0].friendRequest.status =
          V3FriendStatusEnum.FRIEND_STATUS_ENUM_NOT_FRIEND;
        await expectFriendRequestCanceledEventData(
          getFriendResList[0],
          event.data,
        );
        break;

      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }

  await expectResumeWS([member]);
};

export const expectAcceptFriendRequestEvents = async (
  member: UserWS,
  actorSource: string,
  requesterGetFriend: FriendDataWS,
  expectedEvents: cloudEventType[],
): Promise<void> => {
  const { user, events } = member;
  expect(events).toHaveLength(expectedEvents.length);

  for (const [index, event] of events.entries()) {
    expect(event.type).toEqual(expectedEvents[index]);
    expect(event.version).toEqual(WS_VERSION);

    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(user, event.data);
        break;

      case cloudEventType.OUTGOING_FRIEND_REQUEST_ACCEPTED_EVENT:
      case cloudEventType.INCOMING_FRIEND_REQUEST_ACCEPTED_EVENT:
        expectSourceFromUser(actorSource, event.source);
        await expectFriendRequestAcceptedEventData(
          requesterGetFriend,
          event.data,
        );
        break;

      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }

  await expectResumeWS([member]);
};

export const expectFriendData = (
  expectedData: V3Friend,
  actualData: V3Friend,
  isDeleteFriendRequest = false,
): void => {
  if (!actualData.createTime) throw new Error(NULL_UNDEFINED_ERROR);
  if (!actualData.updateTime) throw new Error(NULL_UNDEFINED_ERROR);

  expect(actualData.requestedFromUserId).toEqual(
    expectedData.requestedFromUserId,
  );
  expect(actualData.requestedToUserId).toEqual(expectedData.requestedToUserId);
  expect(actualData.status).toEqual(expectedData.status);

  assertIsDate(actualData.createTime);
  assertIsDate(actualData.updateTime);

  if (expectedData.status == V3FriendStatusEnum.FRIEND_STATUS_ENUM_FRIEND) {
    expect(actualData.friendId).toEqual(expectedData.friendId);

    if (!actualData.readTime) throw new Error(NULL_UNDEFINED_ERROR);
    if (!actualData.acceptTime) throw new Error(NULL_UNDEFINED_ERROR);
    assertIsDate(actualData.readTime);
    assertIsDate(actualData.acceptTime);
    expect(
      new Date(actualData.readTime) > new Date(actualData.createTime),
    ).toBe(true);
    expect(
      new Date(actualData.acceptTime) > new Date(actualData.createTime),
    ).toBe(true);
  } else if (isDeleteFriendRequest) {
    if (!actualData.deleteTime) throw new Error(NULL_UNDEFINED_ERROR);
    assertIsDate(actualData.deleteTime);
    expect(actualData.deleteTime).toEqual(actualData.readTime);
  } else {
    assertIsString(actualData.friendId as string);
    expect(actualData.readTime).toBeUndefined();
  }
  expect(actualData.participantIds?.length).toBe(2);
  expect(actualData.participantIds).toEqual(
    expect.arrayContaining(expectedData.participantIds as string[]),
  );
};

export const expectAddFriendResponse = async (
  actor: V3MockedUser,
  recipient: V3MockedUser,
  expectedData: V3Friend,
): Promise<void> => {
  const result = await getResponseSuccess(
    { userId: recipient.userId },
    friendClient.addFriend,
    createHeaders(actor.token as string),
  );

  const { data, includes } = result;

  expectFriendData(expectedData, data?.friend as V3Friend);

  await expectIncludesUsers([actor, recipient], includes?.users as V3User[]);
};

export const expectAcceptFriendResponse = async (
  actor: V3MockedUser,
  recipient: V3MockedUser,
  expectedData: V3Friend,
): Promise<void> => {
  const result = await getResponseSuccess(
    { userId: recipient.userId },
    friendClient.acceptFriendRequest,
    createHeaders(actor.token as string),
  );

  const { data, includes } = result;

  expectFriendData(expectedData, data?.friend as V3Friend);

  await expectIncludesUsers([actor, recipient], includes?.users as V3User[]);
};
