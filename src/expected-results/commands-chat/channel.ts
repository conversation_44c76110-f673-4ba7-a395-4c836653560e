import { faker } from '@faker-js/faker';

import { V3DirectMessageStatusEnum } from '../../../utils/http-client/cloudevent-client';
import {
  V3AcceptMessageRequestResponse,
  V3Channel,
  V3ChannelData,
  V3ChannelMetadata,
  V3ChannelPermissionsEnum,
  V3ChannelTypeEnum,
  V3CreateChannelRequest,
  V3DataInclude,
  V3DeleteChannelAvatarRequest,
  V3Member,
  V3UpdateChannelAvatarRequest,
  V3UpdateChannelNameRequest,
  V3User,
} from '../../../utils/http-client/commands-chat-client';
import { V3Message } from '../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import {
  V3GetChannelRequest,
  V3GetChannelResponse,
} from '../../../utils/http-client/views-chat-client';
import channelClient from '../../helpers/channel-service';
import channelViewClient from '../../helpers/channel-view-service';
import messageViewClient from '../../helpers/message-view-service';
import {
  assertIsString,
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
  randomEmojis,
  wait,
} from '../../helpers/shared/common';
import { getUserProfile } from '../../helpers/user-view-service';
import {
  CONFIG_FILE_STORE_HOST,
  HEADERS,
  MaxLength,
  MinLength,
  OWNER_PERMISSIONS,
  PREFIX_HOST,
  PREFIX_INVITATION_LINK,
  SystemMessage,
  ZIICHAT_BOT_USERID,
} from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';
import {
  expectChannelInListIncoming,
  expectChannelInListOutGoing,
  expectForGetChannel,
  expectForListChannel,
  isExistInListChannels,
  isNotExistInListChannels,
} from '../views-chat/channel-view';
import { expectMemberDataList } from '../views-chat/member-view';
import { expectForUserDataList } from '../views-chat/user-view';

export const CHANNEL_NAME_CASES = [
  {
    title: 'lowercase text',
    name: 'name',
  },
  {
    title: 'uppercase text',
    name: 'NAME',
  },
  {
    title: 'number',
    name: faker.number.int().toString(),
  },
  {
    title: 'special character',
    name: '!@#$%^&*()',
  },

  {
    title: 'having space',
    name: '     channel name      ',
  },
  {
    title: 'accented Vietnamese',
    name: 'Tiếng Việt',
  },
  {
    title: 'Hindi, Chinese',
    name: 'स्वागत 欢迎',
  },
  {
    title: `${MaxLength.CHANNEL_NAME} emojis`,
    name: randomEmojis(MaxLength.CHANNEL_NAME),
  },
  {
    title: `${MaxLength.CHANNEL_NAME} chars`,
    name: 'a'.repeat(MaxLength.CHANNEL_NAME),
  },
  {
    title: `${MinLength.CHANNEL_NAME} char`,
    name: faker.string.alpha(MinLength.CHANNEL_NAME),
  },
  {
    title: `${MinLength.CHANNEL_NAME} emoji`,
    name: randomEmojis(MinLength.CHANNEL_NAME),
  },
];

export const expectForCreateChannel = async (
  request: V3CreateChannelRequest,
  owner: V3MockedUser,
): Promise<V3Channel> => {
  const createChannelRes = await getResponseSuccess(
    request,
    channelClient.createChannel,
    createHeaders(owner.token as string),
  );
  const { data, includes } = createChannelRes;

  if (!data) throw new Error(NULL_UNDEFINED_ERROR);
  if (!includes) throw new Error(NULL_UNDEFINED_ERROR);

  const { users, members, messages, channels, channelMetadata } = includes;
  const { channel } = data;

  if (!channel) throw new Error(NULL_UNDEFINED_ERROR);
  if (!channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
  if (!messages) throw new Error(NULL_UNDEFINED_ERROR);
  if (!members) throw new Error(NULL_UNDEFINED_ERROR);

  const channelReq = {
    workspaceId: channel.workspaceId,
    channelId: channel.channelId,
  };

  // Expect ChannelData
  await expectChannelData(request, channel, owner);

  // Expect DataInclude
  const userIds = [owner.userId as string];
  expect(channels).toBeUndefined();
  await expectForUserDataList(users as V3User[], userIds);

  if (!createChannelRes?.includes?.members)
    throw new Error(NULL_UNDEFINED_ERROR);
  delete createChannelRes.includes.members[0].nickname; // nickname: ''

  await expectMemberDataList(owner, members, userIds);

  // Expect ChannelMetadata
  expectChannelMetadata(
    channelMetadata[0] as V3ChannelMetadata,
    channel,
    messages[0].messageId,
    0, // unreadCount
    OWNER_PERMISSIONS,
  );

  // System msg
  let systemMsg = SystemMessage.CREATE_CHANNEL;
  if (request.channelType == V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST) {
    systemMsg = SystemMessage.CREATE_BROADCAST_CHANNEL;
  }
  await expectSystemMsgInChannel(
    systemMsg,
    [owner.username as string],
    messages[0],
    owner,
  );

  // GetChannel
  await expectForGetChannel(
    channelReq as V3GetChannelRequest,
    createChannelRes as V3GetChannelResponse,
    owner,
  );

  // ListChannels, ListAllChannels
  await expectForListChannel(createChannelRes as V3GetChannelResponse, owner);

  return createChannelRes.data?.channel as V3Channel;
};

export const expectChannelData = async (
  request: V3CreateChannelRequest,
  channel: V3Channel,
  owner: V3MockedUser,
): Promise<void> => {
  const {
    workspaceId,
    channelId,
    userId,
    type,
    avatar,
    name,
    isPrivate,
    totalMembers,
    invitationLink,
    createTime,
    updateTime,
    participantIds,
  } = channel;

  let isPrivateExpect = true;
  let channelTypeExpect = V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL;
  let avatarExpect: string | undefined;

  if (request.channelType == V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST) {
    isPrivateExpect = false;
    channelTypeExpect = request.channelType;
  }

  if (request.avatarPath) {
    avatarExpect =
      request.avatarPath.replace(CONFIG_FILE_STORE_HOST, PREFIX_HOST) + '?v=';
    expect(avatar).toContain(avatarExpect);
  } else expect(avatar).toBeUndefined();

  expect(workspaceId).toEqual(request.workspaceId);
  assertIsString(channelId as string);

  expect(userId).toEqual(owner.userId);
  expect(type).toEqual(channelTypeExpect);
  expect(name).toEqual(request.name.trim());
  expect(isPrivate).toEqual(isPrivateExpect);
  expect(totalMembers).toEqual(1);
  expect(participantIds).toEqual([]);

  expect(invitationLink).toContain(PREFIX_INVITATION_LINK);
  expectGreaterThanTime(createTime as string, updateTime as string);
};

export const expectChannelMetadata = (
  channelMetadata: V3ChannelMetadata,
  channel: V3Channel,
  lastMsgId: string | undefined,
  unreadCountExp = 0,
  permissionsExp: V3ChannelPermissionsEnum[] | undefined,
): void => {
  expect(channelMetadata?.workspaceId).toEqual(channel.workspaceId);
  expect(channelMetadata?.channelId).toEqual(channel.channelId);
  expect(channelMetadata?.notificationStatus).toEqual(true);
  expect(channelMetadata?.unreadCount).toEqual(unreadCountExp);
  expect(channelMetadata?.permissions).toEqual(permissionsExp);
  expect(channelMetadata.lastMessageId).toEqual(lastMsgId);
};

export const expectForUpdateChannelName = async (
  request: V3UpdateChannelNameRequest,
  requester: V3MockedUser,
  channelExpected: V3Channel,
  permissionsExpected = OWNER_PERMISSIONS,
): Promise<void> => {
  const updateChannelNameRes = await getResponseSuccess(
    request,
    channelClient.updateChannelName,
    createHeaders(requester.token as string),
  );

  const { data, includes } = updateChannelNameRes;
  const { channel } = data as V3ChannelData;
  expect(channel?.name).toEqual(request.name.trim());
  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.updateTime as string,
  );

  expect(channel).toEqual({
    ...channelExpected,
    updateTime: channel?.updateTime,
    name: channel?.name,
  });

  // System msg
  if (!includes?.messages) throw new Error(NULL_UNDEFINED_ERROR);
  await expectSystemMsgInChannel(
    SystemMessage.UPDATE_CHANNEL_NAME,
    [requester.username as string, request.name.trim() as string],
    includes?.messages[0],
    requester,
  );

  const userIds = [requester.userId as string];
  await expectDataIncludeForUpdateChannel(
    updateChannelNameRes as V3GetChannelResponse,
    requester,
    userIds,
    permissionsExpected,
  );
};

export const expectSystemMsgInChannel = async (
  contentSystem: string,
  contentArguments: string[],
  message: V3Message,
  requester: V3MockedUser,
): Promise<void> => {
  const listMsg = await getResponseSuccess(
    {
      channelId: message.channelId,
      workspaceId: message.workspaceId,
    },
    messageViewClient.listMessages,
    createHeaders(requester.token as string),
  );
  if (!listMsg.data) throw new Error(NULL_UNDEFINED_ERROR);
  const lastMsg = listMsg.data[0].message;

  expect(lastMsg?.messageId).toEqual(message.messageId);
  expect(lastMsg?.content).toEqual(contentSystem);
  expect(lastMsg?.contentArguments).toEqual(contentArguments);

  expect(lastMsg).toEqual(message);
};

export const expectForUpdateChannelAvatar = async (
  request: V3UpdateChannelAvatarRequest,
  requester: V3MockedUser,
  channelExpected: V3Channel,
  permissionsExpected = OWNER_PERMISSIONS,
): Promise<V3Channel> => {
  const avatar =
    request.avatarPath.replace(CONFIG_FILE_STORE_HOST, PREFIX_HOST) + `?v=`;
  const updateChannelAvtRes = await getResponseSuccess(
    request,
    channelClient.updateChannelAvatar,
    createHeaders(requester.token as string),
  );

  await expectChannelAvatar(
    updateChannelAvtRes as V3GetChannelResponse,
    channelExpected,
    avatar,
  );

  // System msg
  const { includes } = updateChannelAvtRes;
  if (!includes?.messages) throw new Error(NULL_UNDEFINED_ERROR);
  await expectSystemMsgInChannel(
    SystemMessage.UPDATE_CHANNEL_AVATAR,
    [requester.username as string],
    includes?.messages[0],
    requester,
  );

  const userIds = [requester.userId as string];
  await expectDataIncludeForUpdateChannel(
    updateChannelAvtRes as V3GetChannelResponse,
    requester,
    userIds,
    permissionsExpected,
  );
  return updateChannelAvtRes?.data?.channel as V3Channel;
};

export const expectForDeleteChannelAvatar = async (
  request: V3DeleteChannelAvatarRequest,
  requester: V3MockedUser,
  channelExpected: V3Channel,
  permissionsExpected = OWNER_PERMISSIONS,
): Promise<V3Channel> => {
  const deleteChannelAvtRes = await getResponseSuccess(
    request,
    channelClient.deleteChannelAvatar,
    createHeaders(requester.token as string),
  );

  await expectChannelAvatar(
    deleteChannelAvtRes as V3GetChannelResponse,
    channelExpected,
    '',
  );
  const { includes } = deleteChannelAvtRes;

  // System msg
  if (!includes?.messages) throw new Error(NULL_UNDEFINED_ERROR);
  await expectSystemMsgInChannel(
    SystemMessage.DELETE_CHANNEL_AVATAR,
    [requester.username as string],
    includes?.messages[0],
    requester,
  );

  const userIds = [requester.userId as string];
  await expectDataIncludeForUpdateChannel(
    deleteChannelAvtRes as V3GetChannelResponse,
    requester,
    userIds,
    permissionsExpected,
  );

  return deleteChannelAvtRes?.data?.channel as V3Channel;
};

const expectDataIncludeForUpdateChannel = async (
  channelResponse: V3GetChannelResponse,
  requester: V3MockedUser,
  userIds: string[],
  permissionsExpected = OWNER_PERMISSIONS,
  unreadCount = 0,
) => {
  const { data, includes } = channelResponse;

  // Expect DataInclude
  const { users, members, messages, channels, channelMetadata } =
    includes as V3DataInclude;
  if (!messages) throw new Error(NULL_UNDEFINED_ERROR);
  if (!members) throw new Error(NULL_UNDEFINED_ERROR);
  if (!channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);

  expect(channels).toBeUndefined();
  await expectForUserDataList(users as V3User[], userIds);

  // TODO https://github.com/halonext/ziichat-issues/issues/21789
  // await expectMemberDataList(requester, members as V3Member[], userIds);

  expectChannelMetadata(
    channelMetadata[0] as V3ChannelMetadata,
    data?.channel as V3Channel,
    messages[0].messageId,
    unreadCount,
    permissionsExpected,
  );

  // GetChannel
  const channelReq = {
    workspaceId: data?.channel?.workspaceId,
    channelId: data?.channel?.channelId,
  };
  // TODO https://github.com/halonext/ziichat-issues/issues/21826
  // await expectForGetChannel(
  //   channelReq as V3GetChannelRequest,
  //   updateChannelNameRes as V3GetChannelResponse,
  //   requester,
  // );

  // ListChannels, ListAllChannels
  await expectForListChannel(
    channelResponse as V3GetChannelResponse,
    requester,
  );
};

const expectChannelAvatar = async (
  channelRes: V3GetChannelResponse,
  channelExpected: V3Channel,
  avatar = '',
) => {
  const { data } = channelRes;
  const { channel } = data as V3ChannelData;

  if (avatar != '') {
    expect(channel?.avatar).toContain(avatar);
    expect(channel?.originalAvatar).toContain(avatar);
  } else {
    expect(channel?.avatar).toEqual(avatar);
    expect(channel?.originalAvatar).toEqual(avatar);
  }

  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.updateTime as string,
  );

  expect(channel).toEqual({
    ...channelExpected,
    updateTime: channel?.updateTime,
    name: channel?.name,
    avatar: channel?.avatar,
    originalAvatar: channel?.originalAvatar,
  });
};

/**
 *
 * @param userAccept
 * @param userSendMsg
 * @param msgSend
 * @param unreadCount
 */
export const expectForAcceptMessageRequest = async (
  userAccept: V3MockedUser,
  userSendMsg: V3MockedUser,
  msgSend: V3Message,
  unreadCount = 1,
): Promise<V3AcceptMessageRequestResponse> => {
  // Expect before accept
  const { channelId } = msgSend;
  await expectChannelInListIncoming(channelId as string, userAccept);
  await expectChannelInListOutGoing(channelId as string, userAccept, false);

  await expectChannelInListIncoming(channelId as string, userSendMsg, false);
  await expectChannelInListOutGoing(channelId as string, userSendMsg);

  await isNotExistInListChannels(
    channelId as string,
    [userAccept],
    [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
  );

  await isExistInListChannels(
    channelId as string,
    [userSendMsg],
    [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
  );

  const participants = [
    userSendMsg.userId as string,
    userAccept.userId as string,
  ];
  const channelRes = await getResponseSuccess(
    { userId: userSendMsg.userId },
    channelClient.acceptMessageRequest,
    createHeaders(userAccept.token as string),
  );
  const { data, includes } = channelRes;
  if (!data) throw new Error(NULL_UNDEFINED_ERROR);
  const { channel } = data;
  await expectForAcceptMsgRequestData(
    channel as V3Channel,
    userSendMsg,
    msgSend,
    participants,
  );

  // Expect Data include
  expect(includes?.members).toBeUndefined();
  expect(includes?.channels).toBeUndefined();

  await expectForUserDataList(includes?.users as V3User[], participants);

  if (!includes?.messages) throw new Error(NULL_UNDEFINED_ERROR);
  // TODO https://discord.com/channels/720906494442471504/1107893472045846528/1373865359710355526
  delete msgSend.ref;
  expect(includes?.messages[0]).toEqual(msgSend);

  if (!includes?.channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
  expectChannelMetadata(
    includes?.channelMetadata[0],
    channel as V3Channel,
    msgSend.messageId,
    unreadCount,
    undefined, // permissions
  );

  // Expect after accept
  await expectChannelInListIncoming(channelId as string, userAccept, false);
  await expectChannelInListOutGoing(channelId as string, userAccept, false);

  await expectChannelInListIncoming(channelId as string, userSendMsg, false);
  await expectChannelInListOutGoing(channelId as string, userSendMsg, false);

  await isExistInListChannels(
    channelId as string,
    [userAccept, userSendMsg],
    [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
  );
  return channelRes;
};

const expectForAcceptMsgRequestData = async (
  channel: V3Channel,
  userSendMsg: V3MockedUser,
  msgSend: V3Message,
  participants: string[],
): Promise<void> => {
  const userSendMsgProfile = await getUserProfile(userSendMsg);

  expect(channel?.workspaceId).toEqual(msgSend.workspaceId);
  expect(channel?.channelId).toEqual(msgSend.channelId);
  expect(channel?.userId).toEqual(userSendMsg.userId);
  expect(channel?.isPrivate).toBeTruthy();
  expect(channel?.type).toEqual(V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM);
  expect(channel?.name).toEqual(userSendMsgProfile.profile?.displayName);
  expect(channel?.avatar).toEqual(userSendMsgProfile.profile?.avatar);
  expect(channel?.originalAvatar).toEqual(
    userSendMsgProfile.profile?.originalAvatar,
  );
  expect(channel?.rejectTime).toEqual('');
  expect(channel?.dmStatus).toEqual(
    V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
  );
  expect(channel?.participantIds).toEqual(participants);

  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.updateTime as string,
  );
  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.acceptTime as string,
  );
};

export const expectForRejectMessageRequest = async (
  userReject: V3MockedUser,
  userSendMsg: V3MockedUser,
  msgSend: V3Message,
): Promise<void> => {
  const userRejectHeaders = createHeaders(userReject.token as string);

  await getResponseSuccess(
    { userId: userSendMsg.userId },
    channelClient.rejectMessageRequest,
    userRejectHeaders,
  );
  await wait();
  const channelId = msgSend.channelId as string;

  // Expect after reject
  await expectChannelInListIncoming(channelId as string, userReject, false);
  await expectChannelInListOutGoing(channelId as string, userReject, false);

  await expectChannelInListIncoming(channelId as string, userSendMsg, false);
  await expectChannelInListOutGoing(channelId as string, userSendMsg);

  await isExistInListChannels(
    channelId as string,
    [userSendMsg],
    [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
  );
  await isNotExistInListChannels(
    channelId as string,
    [userReject],
    [channelViewClient.listDmChannels, channelViewClient.listAllChannels],
  );

  // Expect getDMChannel after reject
  const participants = [
    userSendMsg.userId as string,
    userReject.userId as string,
  ];

  await expectForRejectMsgRequestData(
    userReject,
    userSendMsg,
    msgSend,
    participants,
  );

  await expectForRejectMsgRequestData(
    userSendMsg,
    userReject,
    msgSend,
    participants,
  );
};

const expectForRejectMsgRequestData = async (
  requester: V3MockedUser,
  recipient: V3MockedUser,
  msgSend: V3Message,
  participants: string[],
): Promise<void> => {
  const channelRes = await getResponseSuccess(
    { userId: recipient.userId },
    channelViewClient.getDmChannel,
    createHeaders(requester.token as string),
  );

  const channel = channelRes?.data?.channel as V3Channel;
  const recipientProfile = await getUserProfile(recipient);

  expect(channel?.workspaceId).toEqual(msgSend.workspaceId);
  expect(channel?.channelId).toEqual(msgSend.channelId);
  expect(channel?.userId).toEqual(msgSend.userId);
  expect(channel?.isPrivate).toBeTruthy();
  expect(channel?.type).toEqual(V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM);
  expect(channel?.name).toEqual(recipientProfile.profile?.displayName);
  expect(channel?.avatar).toEqual(recipientProfile.profile?.avatar);
  expect(channel?.originalAvatar).toEqual(
    recipientProfile.profile?.originalAvatar,
  );
  expect(channel?.acceptTime).toBeUndefined();
  expect(channel?.dmStatus).toEqual(
    V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
  );
  expect(channel?.participantIds).toEqual(participants);

  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.updateTime as string,
  );
  expectGreaterThanTime(
    channel?.createTime as string,
    channel?.rejectTime as string,
  );

  // ChannelMetadata
  if (!channelRes?.includes?.channelMetadata)
    throw new Error(NULL_UNDEFINED_ERROR);
  // because user reject deleteAllMessagesOnlyMe
  let lastMessageId: string | undefined = undefined;
  if (requester.userId == msgSend.userId) {
    lastMessageId = msgSend.messageId as string;
  }

  expectChannelMetadata(
    channelRes?.includes?.channelMetadata[0],
    channel,
    lastMessageId, // lastMessageId
    0, // unreadCount
    [], // permissions
  );
};

export const expectListAllChannels = async (
  requester: V3MockedUser,
  headers: HEADERS,
  channelListExpected: V3Channel[],
  memberIds: string[],
  method = channelViewClient.listAllChannels,
): Promise<void> => {
  let isExistBotChat = false;
  let isExistDmChannel = false;
  let isExistChannel = false;
  let isExistBrChannel = false;

  const userIds: string[] = [requester.userId as string];
  const msgIds: string[] = [];
  const msgDMIds: string[] = [];

  const listAllChannelsResponse = await getResponseSuccess({}, method, headers);
  const { data, includes } = listAllChannelsResponse;
  const { users, messages, members, channels, channelMetadata } =
    includes as V3DataInclude;

  if (!data) throw new Error(NULL_UNDEFINED_ERROR);
  expect(data).toHaveLength(channelListExpected.length);

  for (const { channel } of data) {
    const channelExpected = channelListExpected.find(
      (c) => c.channelId == channel?.channelId,
    );
    // TODO https://github.com/halonext/ziichat-issues/issues/21254
    delete channelExpected?.totalMembers;
    expect(channel).toEqual(channelExpected);

    switch (channel?.type) {
      case V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM: {
        isExistDmChannel = true;
        const recipientId = channel.participantIds?.find(
          (userId) => userId != requester.userId,
        );
        userIds.push(recipientId as string);

        if (recipientId == ZIICHAT_BOT_USERID) isExistBotChat = true;

        // TODO https://github.com/halonext/ziichat-issues/issues/22025
        // await expectChannelMetadataList(
        //   requester,
        //   { userId: recipientId },
        //   channelMetadata as V3ChannelMetadata[],
        //   channelViewClient.getDmChannel,
        // );
        break;
      }
      case V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL: {
        isExistChannel = true;
        // await expectChannelMetadataList(
        //   requester,
        //   { channelId: channel.channelId, workspaceId: channel.workspaceId },
        //   channelMetadata as V3ChannelMetadata[],
        //   channelViewClient.getChannel,
        // );

        const memberExpect = members?.filter(
          (m) => m.channelId == channel.channelId,
        );
        expect(memberExpect).toBeDefined();
        await expectMemberDataList(
          requester,
          memberExpect as V3Member[],
          memberIds,
        );
        break;
      }
      case V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST: {
        isExistBrChannel = true;
        // await expectChannelMetadataList(
        //   requester,
        //   { channelId: channel.channelId, workspaceId: channel.workspaceId },
        //   channelMetadata as V3ChannelMetadata[],
        //   channelViewClient.getChannel,
        // );
        break;
      }
      default:
        throw new Error(
          'ChannelType have not been handled yet: ' + channel?.type,
        );
    }
  }

  switch (method) {
    case channelViewClient.listAllChannels:
      expect(isExistBotChat).toBeTruthy();
      expect(isExistChannel).toBeTruthy();
      expect(isExistDmChannel).toBeTruthy();
      expect(isExistBrChannel).toBeTruthy();
      break;
    case channelViewClient.listChannels:
      expect(isExistBotChat).toBeFalsy();
      expect(isExistDmChannel).toBeFalsy();

      expect(isExistChannel).toBeTruthy();
      expect(isExistBrChannel).toBeTruthy();
      break;
    case channelViewClient.listDmChannels:
      expect(isExistBotChat).toBeTruthy();
      expect(isExistDmChannel).toBeTruthy();

      expect(isExistChannel).toBeFalsy();
      expect(isExistBrChannel).toBeFalsy();
      break;
  }

  // Expected Data includes
  expect(channels).toEqual([]);
  await expectForUserDataList(users as V3User[], userIds);
};
