import {
  V3<PERSON>hannel,
  V3CreateInvitationRequest,
  V3User,
} from '../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import {
  V3GetInvitationRequest,
  V3InvitationData,
  V3InvitationDataChannelData,
  V3InvitationStatusEnum,
} from '../../../utils/http-client/views-chat-client';
import invitationClient from '../../helpers/invitation-service';
import invitationViewClient from '../../helpers/invitation-view-service';
import memberViewClient from '../../helpers/member-view-service';
import {
  assertIsDate,
  assertIsString,
  createHeaders,
  deletePresenceDataObj,
  getResponseSuccess,
} from '../../helpers/shared/common';
import {
  EXPIRES_IN_DEFAULT,
  MAX_USES_DEFAULT,
  WORKSPACE_ID,
} from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';

export const expectForCreateInvitation = async (
  request: V3CreateInvitationRequest,
  actor: V3MockedUser,
  channel: V3Channel,
): Promise<void> => {
  const actorHeaders = createHeaders(actor.token as string);
  const expiresIn = !request?.expiresIn
    ? EXPIRES_IN_DEFAULT
    : request?.expiresIn;
  const maxUses = !request?.maxUses ? MAX_USES_DEFAULT : request?.maxUses;

  const createInvitationRes = await getResponseSuccess(
    request,
    invitationClient.createInvitation,
    actorHeaders,
  );

  const { data } = createInvitationRes;
  if (!data) throw new Error(NULL_UNDEFINED_ERROR);

  expect(data?.workspaceId).toEqual(request.workspaceId);
  expect(data?.channelId).toEqual(request.channelId);

  assertIsString(data?.code as string);
  expect(data?.expiresIn).toEqual(expiresIn);
  expect(data?.maxUses).toEqual(maxUses);
  expect(data?.status).toEqual(
    V3InvitationStatusEnum.INVITATION_STATUS_ENUM_ACTIVE,
  );

  const createTime = new Date(data?.createTime as string);
  const expectedExpireTime = new Date(createTime.getTime() + expiresIn * 1000);
  expect(data?.expireTime).toContain(
    expectedExpireTime.toISOString().slice(0, 18),
  );
  assertIsDate(data?.createTime as string);
  assertIsDate(data?.updateTime as string);
  expect(data?.createTime).toEqual(data?.updateTime);

  const expectedData: V3InvitationData = {
    ...data,
    isJoined: true,
    isExpired: false,
    channel: {
      channelId: channel.channelId,
      workspaceId: channel.workspaceId,
      name: channel.name,
      avatar: !channel.avatar ? '' : channel.avatar,
      totalMembers: channel.totalMembers,
    },
  };

  await expectForGetInvitation(
    actor,
    { code: data.code as string },
    expectedData,
  );
};

export const expectForGetInvitation = async (
  actor: V3MockedUser,
  request: V3GetInvitationRequest,
  expectedData: V3InvitationData,
): Promise<void> => {
  const actorHeaders = createHeaders(actor.token as string);
  const getInvitationRes = await getResponseSuccess(
    request,
    invitationViewClient.getInvitation,
    actorHeaders,
  );
  const { data } = getInvitationRes;

  const {
    code,
    isExpired,
    expireTime,
    isJoined,
    invitationLink,
    createTime,
    updateTime,
    channel,
  } = data as V3InvitationData;

  assertIsString(createTime as string);
  assertIsString(updateTime as string);

  expect(code).toEqual(expectedData.code);
  expect(invitationLink).toEqual(expectedData.invitationLink);
  expect(expireTime).toEqual(expectedData.expireTime);

  expect(isJoined).toEqual(expectedData.isJoined);
  expect(isExpired).toEqual(expectedData.isExpired);

  // channel
  expect(channel).toMatchObject(
    expectedData.channel as V3InvitationDataChannelData,
  );

  const lisMembersRes = await getResponseSuccess(
    {
      channelId: expectedData.channel?.channelId,
      workspaceId: WORKSPACE_ID, // error by interface workspaceId: '0'
    },
    memberViewClient.listMembers,
    actorHeaders,
  );
  const members = lisMembersRes?.includes?.users?.map((user) =>
    deletePresenceDataObj(user),
  ) as V3User[];

  expect(channel?.members).toHaveLength(members.length);

  channel?.members?.forEach((user) => {
    const userExpected = members?.find((mem) => mem.userId == user.userId);
    deletePresenceDataObj(user);
    expect(user).toEqual(userExpected);
  });
};
