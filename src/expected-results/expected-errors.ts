import { StatusCodes } from 'http-status-codes';

import { BaseResponse, ClientMethod } from '../helpers/shared/common';
import { HEADERS } from '../tests/const';

export const expectForParamInvalid = async <TReq, TRes>(
  request: TReq,
  method: ClientMethod<TReq, TRes>,
  metadata: HEADERS,
  errors: object,
): Promise<void> => {
  const response = await method(request, metadata);

  const { data, status } = response;
  expect(status).toEqual(StatusCodes.OK);
  expect((data as BaseResponse).ok).toBeFalsy();
  expect((data as BaseResponse).error).toEqual(errors);
};

export const expectForParamOnGateway = async <TReq, TRes>(
  request: TReq,
  method: ClientMethod<TReq, TRes>,
  metadata: HEADERS,
  error: string,
): Promise<void> => {
  const response = await method(request, metadata);

  const { status } = response;
  expect(status).toEqual(StatusCodes.FORBIDDEN);
  expect(await (response as any).text()).toEqual(error);
};
