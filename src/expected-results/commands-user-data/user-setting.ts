import { V3MockedUser } from '../../../utils/http-client/faker-client';
import { getResponseSuccess } from '../../helpers/shared/common';
import { userViewClient } from '../../helpers/user-view-service';
import { HEADERS } from '../../tests/const';

export const isExistInListBlockedUsers = async (
  findingUser: V3MockedUser,
  metadata: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    userViewClient.listBlockedUsers,
    metadata,
  );

  const hasUser = result.data?.some(
    ({ userId }) => userId === findingUser.userId,
  );
  expect(hasUser).toBeTruthy();
};

export const isNotExistInListBlockedUsers = async (
  findingUser: V3MockedUser,
  metadata: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    userViewClient.listBlockedUsers,
    metadata,
  );

  if (result.data) {
    const hasUser = result.data?.some(
      ({ userId }) => userId === findingUser.userId,
    );
    expect(hasUser).toBeFalsy();
  } else expect(result.data).toStrictEqual([]);
};
