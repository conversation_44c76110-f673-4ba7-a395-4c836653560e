import {
  V3AddUserStatusRequest,
  V3UpdateUserStatusRequest,
  V3UserStatusData,
} from '../../../utils/http-client/commands-user-data-client';
import { V3UserStatusExpireAfterTimeEnum } from '../../../utils/http-client/views-chat-client';
import {
  assertIsDate,
  expectGreaterThanTime,
  getResponseSuccess,
} from '../../helpers/shared/common';
import { userProfileClient } from '../../helpers/user-service';
import { userViewClient } from '../../helpers/user-view-service';
import { HEADERS } from '../../tests/const';
import {
  expectStatusDataInGetMe,
  isExistInListUserStatus,
} from '../views-chat/user-view';

export const getEndTimeStatus = async (
  startTime: string,
  expireTimeEnum: V3UserStatusExpireAfterTimeEnum,
): Promise<string | null> => {
  let endTime: Date;
  const currentDate = new Date(startTime);

  switch (expireTimeEnum) {
    case V3UserStatusExpireAfterTimeEnum.USER_STATUS_EXPIRES_AFTER_TIME_ENUM_UNSPECIFIED:
      endTime = new Date(currentDate.setHours(currentDate.getHours() + 24));
      break;

    case V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER1HOUR:
      endTime = new Date(currentDate.setHours(currentDate.getHours() + 1));
      break;

    case V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER4HOUR:
      endTime = new Date(currentDate.setHours(currentDate.getHours() + 4));
      break;

    case V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER8HOUR:
      endTime = new Date(currentDate.setHours(currentDate.getHours() + 8));
      break;

    case V3UserStatusExpireAfterTimeEnum.USERSTATUSEXPIRESAFTERTIMEENUMAFTER24HOUR:
      endTime = new Date(currentDate.setHours(currentDate.getHours() + 24));
      break;

    case V3UserStatusExpireAfterTimeEnum.USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER:
      endTime = new Date(
        currentDate.setFullYear(currentDate.getFullYear() + 100),
      );
      break;

    default:
      return null;
  }

  return endTime.toISOString();
};

export const expectAddUserStatus = async (
  request: V3AddUserStatusRequest,
  requesterMetadata: HEADERS,
): Promise<V3UserStatusData> => {
  const addStatusData = await getResponseSuccess(
    request,
    userProfileClient.addUserStatus,
    requesterMetadata,
  );
  if (request.content) request.content = request.content.trim();
  if (request.status) request.status = request.status.trim();

  const actualData = addStatusData.data as V3UserStatusData;

  expect(actualData).toMatchObject(request);

  assertIsDate(actualData.createTime as string);
  assertIsDate(actualData.updateTime as string);
  assertIsDate(actualData.endTime as string);
  expect(actualData.createTime).toEqual(actualData.updateTime);

  const endTimeExpected = await getEndTimeStatus(
    actualData.createTime as string,
    request.expireAfterTime as number,
  );
  expect(actualData.endTime).toEqual(endTimeExpected);

  await isExistInListUserStatus(actualData, [requesterMetadata]);
  await expectStatusDataInGetMe(requesterMetadata, actualData);
  return addStatusData.data as V3UserStatusData;
};

export const expectUpdateUserStatus = async (
  request: V3UpdateUserStatusRequest,
  requesterMetadata: HEADERS,
  addStatusRes: V3UserStatusData,
): Promise<V3UserStatusData | null> => {
  const updateStatusRes = await getResponseSuccess(
    request,
    userProfileClient.updateUserStatus,
    requesterMetadata,
  );

  const emptyContent = request.content?.trim().length === 0;
  const emptyStatus = request.status?.trim().length === 0;

  if (
    (emptyContent && !('status' in addStatusRes)) ||
    (emptyStatus && !('content' in addStatusRes))
  ) {
    expect(updateStatusRes.data).toBeUndefined();

    const response = await getResponseSuccess(
      {},
      userViewClient.listUserStatus,
      requesterMetadata,
    );
    expect(response.data).toStrictEqual([]);
    return null;
  }

  const actualData = updateStatusRes.data as V3UserStatusData;

  const expectedData: V3UserStatusData = {
    ...addStatusRes,
    updateTime: actualData.updateTime as string,
  };

  if (request.content?.trim().length === 0) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    delete expectedData.content;
  } else if (request.content) {
    expectedData.content = request.content?.trim();
  }

  if (request.status?.trim().length === 0) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    delete expectedData.status;
  } else if (request.status) {
    expectedData.status = request.status?.trim();
  }

  expectGreaterThanTime(
    actualData.createTime as string,
    actualData.updateTime as string,
  );

  expect(actualData).toMatchObject(expectedData);

  await isExistInListUserStatus(actualData, [requesterMetadata]);
  await expectStatusDataInGetMe(requesterMetadata, actualData);

  return actualData;
};
