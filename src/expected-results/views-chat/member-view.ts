import { V3Member } from '../../../utils/http-client/commands-chat-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import { GetMemberParams } from '../../../utils/http-client/views-chat-client';
import memberViewService from '../../helpers/member-view-service';
import { createHeaders, getResponseSuccess } from '../../helpers/shared/common';
import { WORKSPACE_ID } from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';

export const expectMemberDataList = async (
  userGetMember: V3MockedUser,
  members: V3Member[],
  memberIds: string[],
): Promise<void> => {
  const request: GetMemberParams = {
    workspaceId: WORKSPACE_ID,
    channelId: members[0].channelId,
  };
  for (const memberId of memberIds) {
    request.userId = memberId;
    const memberInfo = await getResponseSuccess(
      request,
      memberViewService.getMember,
      createHeaders(userGetMember.token as string),
    );
    const actualMember = members.find((member) => {
      return member.userId == memberId && member.channelId == request.channelId;
    });

    if (!actualMember) throw new Error(NULL_UNDEFINED_ERROR);
    expect(memberInfo.data?.member).toMatchObject(actualMember);
  }
};
