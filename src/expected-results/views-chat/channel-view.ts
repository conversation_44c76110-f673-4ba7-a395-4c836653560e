import {
  V3ChannelData,
  V3ChannelMetadata,
  V3ChannelTypeEnum,
  V3DataInclude,
  V3DirectMessageStatusEnum,
} from '../../../utils/http-client/commands-chat-client';
import { V3Message } from '../../../utils/http-client/commands-message-client';
import {
  HttpResponse,
  V3MockedUser,
} from '../../../utils/http-client/faker-client';
import {
  ListAllChannelsParams,
  ListInComingMessageRequestsParams,
  ListOutGoingFriendRequestsParams,
  V3Channel,
  V3GetChannelRequest,
  V3GetChannelResponse,
  V3GetDMChannelRequest,
  V3GetDMChannelResponse,
  V3ListAllChannelsRequest,
  V3ListAllChannelsResponse,
  V3User,
} from '../../../utils/http-client/views-chat-client';
import channelViewClient from '../../helpers/channel-view-service';
import channelViewService from '../../helpers/channel-view-service';
import {
  BaseResponse,
  ClientMethod,
  createHeaders,
  expectGreaterThanTime,
  getResponseSuccess,
} from '../../helpers/shared/common';
import { getUserProfile } from '../../helpers/user-view-service';
import { HEADERS, ZIICHAT_BOT_USERID } from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';
import { expectChannelMetadata } from '../commands-chat/channel';
import { expectForUserDataList } from './user-view';

export const expectForGetChannel = async (
  request: V3GetChannelRequest,
  expectedChannel: V3GetChannelResponse,
  requester: V3MockedUser,
): Promise<void> => {
  const res = await getResponseSuccess(
    request,
    channelViewClient.getChannel,
    createHeaders(requester.token as string),
  );

  expect(res).toMatchObject(expectedChannel);
};

export const expectForGetDMChannel = async (
  request: V3GetDMChannelRequest,
  expectedChannel: V3GetDMChannelResponse,
  requester: V3MockedUser,
): Promise<void> => {
  const res = await getResponseSuccess(
    request,
    channelViewClient.getDmChannel,
    createHeaders(requester.token as string),
  );

  expect(res).toMatchObject(expectedChannel);
};

export const expectForListChannel = async (
  expectedChannel: V3GetChannelResponse,
  requester: V3MockedUser,
  request: V3ListAllChannelsRequest = {},
): Promise<void> => {
  for (const method of [
    channelViewClient.listChannels,
    channelViewService.listAllChannels,
  ]) {
    const res = await getResponseSuccess(
      request,
      method,
      createHeaders(requester.token as string),
    );

    if (!res.data || !expectedChannel?.data?.channel)
      throw new Error(NULL_UNDEFINED_ERROR);
    // TODO https://github.com/halonext/ziichat-issues/issues/21254
    delete expectedChannel?.data?.channel.totalMembers;
    expect(res.data[0].channel).toMatchObject(expectedChannel?.data?.channel);
  }
};

export const expectForListDMChannel = async (
  expectedChannel: V3GetDMChannelResponse,
  requester: V3MockedUser,
  request: V3ListAllChannelsRequest = {},
): Promise<void> => {
  for (const method of [
    channelViewClient.listDmChannels,
    channelViewService.listAllChannels,
  ]) {
    const res = await getResponseSuccess(
      request,
      method,
      createHeaders(requester.token as string),
    );

    if (!res.data || !expectedChannel?.data?.channel)
      throw new Error(NULL_UNDEFINED_ERROR);
    expect(res.data[0].channel).toMatchObject(expectedChannel?.data?.channel);
  }
};

/**
 * Check in ListChannels, ListAllChannels
 * @param channelId
 * @param users
 * @param methods
 * @param request
 */
export const isNotExistInListChannels = async (
  channelId: string,
  users: V3MockedUser[],
  methods = [
    channelViewClient.listChannels,
    channelViewService.listAllChannels,
  ],
  request: V3ListAllChannelsRequest = {},
): Promise<void> => {
  for (const user of users) {
    for (const method of methods) {
      const res = await getResponseSuccess(
        request,
        method,
        createHeaders(user.token as string),
      );

      const { data, paging } = res;
      expect(paging?.hasNext).toBeFalsy();
      expect(paging?.hasPrev).toBeFalsy();

      data?.map((channelInList) => {
        expect(channelInList?.channel?.channelId).not.toEqual(channelId);
      });
    }
  }
};

export const isExistInListChannels = async (
  channelId: string,
  users: V3MockedUser[],
  methods = [
    channelViewClient.listChannels,
    channelViewService.listAllChannels,
  ],
  request: V3ListAllChannelsRequest = {},
): Promise<void> => {
  for (const user of users) {
    for (const method of methods) {
      const res = await getResponseSuccess(
        request,
        method,
        createHeaders(user.token as string),
      );

      const { data, paging } = res;
      expect(paging?.hasNext).toBeFalsy();
      expect(paging?.hasPrev).toBeFalsy();

      expect(
        data?.some(
          (channelInList) => channelInList?.channel?.channelId == channelId,
        ),
      ).toBeTruthy();
    }
  }
};

export const expectChannelInListIncoming = async (
  channelId: string,
  user: V3MockedUser,
  isExist = true,
): Promise<void> => {
  const res = await getResponseSuccess(
    {},
    channelViewClient.listInComingMessageRequests,
    createHeaders(user.token as string),
  );

  const { data, paging } = res;
  expect(paging?.hasNext).toBeFalsy();
  expect(paging?.hasPrev).toBeFalsy();

  if (isExist) {
    const channelRes = data?.filter(
      (channelInList) => channelInList?.channel?.channelId == channelId,
    );

    if (!channelRes) throw new Error(NULL_UNDEFINED_ERROR);
    expect(channelRes?.length).toEqual(1);
    expect(channelRes[0].channel?.dmStatus).toEqual(
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
    );
    expect(channelRes[0].channel?.type).toEqual(
      V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM,
    );
    return;
  }

  return expect(
    data?.some(
      (channelInList) => channelInList?.channel?.channelId == channelId,
    ),
  ).toBeFalsy();
};

export const expectChannelInListOutGoing = async (
  channelId: string,
  user: V3MockedUser,
  isExist = true,
): Promise<void> => {
  const res = await getResponseSuccess(
    {},
    channelViewClient.listOutGoingMessageRequests,
    createHeaders(user.token as string),
  );

  const { data, paging } = res;
  expect(paging?.hasNext).toBeFalsy();
  expect(paging?.hasPrev).toBeFalsy();

  if (isExist) {
    const channelRes = data?.filter(
      (channelInList) => channelInList?.channel?.channelId == channelId,
    );

    if (!channelRes) throw new Error(NULL_UNDEFINED_ERROR);
    expect(channelRes?.length).toEqual(1);
    expect(channelRes[0].channel?.dmStatus).toEqual(
      V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
    );
    return;
  }

  return expect(
    data?.some(
      (channelInList) => channelInList?.channel?.channelId == channelId,
    ),
  ).toBeFalsy();
};

export const expectGetDMChannelData = async (
  userSendMsgFirst: V3MockedUser,
  lastMsg: V3Message,
  userGetDM: V3MockedUser,
  userOther: V3MockedUser,
  dmStatus = V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED,
  unreadCount = 0,
): Promise<void> => {
  const userSendMsgProfile = await getUserProfile(userOther);
  const participants = [userSendMsgFirst.userId as string];
  if (userGetDM.userId != userSendMsgFirst.userId) {
    participants.push(userGetDM.userId as string);
  } else {
    participants.push(userOther.userId as string);
  }
  const getChannelRes = await getResponseSuccess(
    { userId: userOther.userId },
    channelViewClient.getDmChannel,
    createHeaders(userGetDM.token as string),
  );

  const channel = getChannelRes?.data?.channel as V3Channel;

  expect(channel?.workspaceId).toEqual(lastMsg.workspaceId);
  expect(channel?.channelId).toEqual(lastMsg.channelId);
  expect(channel?.userId).toEqual(userSendMsgFirst.userId);
  expect(channel?.isPrivate).toBeTruthy();
  expect(channel?.type).toEqual(V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM);
  expect(channel?.name).toEqual(userSendMsgProfile.profile?.displayName);
  expect(channel?.avatar).toEqual(userSendMsgProfile.profile?.avatar);
  expect(channel?.originalAvatar).toEqual(
    userSendMsgProfile.profile?.originalAvatar,
  );
  expect(channel?.participantIds).toEqual(participants);
  expect(channel?.dmStatus).toEqual(dmStatus);

  if (userOther.userId != ZIICHAT_BOT_USERID) {
    expectGreaterThanTime(
      channel?.createTime as string,
      channel?.updateTime as string,
    );
    if (
      dmStatus == V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_CONTACTED
    ) {
      expectGreaterThanTime(
        channel?.createTime as string,
        channel?.acceptTime as string,
      );
    }
  } else expect(channel?.createTime).toEqual(channel?.updateTime);

  // Data includes
  const includes = getChannelRes?.includes as V3DataInclude;
  const { channels, members, users, channelMetadata, messages } = includes;
  expect(channels).toEqual([]);
  expect(members).toEqual([]);
  await expectForUserDataList(users as V3User[], participants);

  if (!messages) throw new Error(NULL_UNDEFINED_ERROR);

  delete lastMsg.ref; // TODO remove after add ref
  expect(messages[0]).toEqual(lastMsg);

  if (!channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
  expectChannelMetadata(
    channelMetadata[0],
    channel,
    lastMsg.messageId,
    unreadCount,
    [],
  );
};

export const expectChannelMetadataList = async <TReq, TRes>(
  requester: V3MockedUser,
  request: TReq,
  channelMetadata: V3ChannelMetadata[],
  method: ClientMethod<TReq, TRes>,
): Promise<void> => {
  const res = await getResponseSuccess(
    request,
    method,
    createHeaders(requester.token as string),
  );

  const getChannelRes = res as BaseResponse;
  if (!getChannelRes?.includes?.channelMetadata)
    throw new Error(NULL_UNDEFINED_ERROR);
  const channelMetadataExpected = getChannelRes?.includes?.channelMetadata[0];

  const channelMetadataActual = channelMetadata.find(
    (c) => c.channelId == channelMetadataExpected?.channelId,
  );
  if (!channelMetadataActual) throw new Error(NULL_UNDEFINED_ERROR);
  expect(channelMetadataActual).toEqual(channelMetadataExpected);
};

interface CheckPaginationView {
  options: {
    quantity: number;
    limit: number;
  };
  originalList: V3ChannelData[];
  headers: HEADERS;
  fetchList: (
    request:
      | ListAllChannelsParams
      | ListInComingMessageRequestsParams
      | ListOutGoingFriendRequestsParams,
    headers: HEADERS,
  ) => Promise<HttpResponse<V3ListAllChannelsResponse, any>>;
}

export const checkPaginationChannelView = async ({
  fetchList,
  headers,
  options,
  originalList,
}: CheckPaginationView): Promise<void> => {
  const { quantity, limit } = options;
  let nextPageToken: string | undefined = '';
  let prevPageToken: string | undefined = '';
  let hasNextPage: boolean | undefined = true;
  let hasPreviousPage: boolean | undefined = false;
  let totalFetchedChannels = 0;

  // First next from start to end list
  while (hasNextPage) {
    const request = nextPageToken ? { limit, nextPageToken } : { limit };

    const fetchedListChannels = await getResponseSuccess(
      request,
      fetchList,
      headers,
    );

    if (!fetchedListChannels.data || !fetchedListChannels.paging)
      throw new Error(NULL_UNDEFINED_ERROR);
    expect(fetchedListChannels.data.length).toBeLessThanOrEqual(limit);

    hasNextPage = fetchedListChannels.paging.hasNext;
    hasPreviousPage = fetchedListChannels.paging.hasPrev;
    nextPageToken = fetchedListChannels.paging.nextPageToken;
    prevPageToken = fetchedListChannels.paging.prevPageToken;

    fetchedListChannels.data.forEach((fetchedChannel, index) => {
      const originalChannel: V3ChannelData =
        originalList[totalFetchedChannels + index];
      expect(originalChannel).toMatchObject(fetchedChannel);
    });

    totalFetchedChannels += fetchedListChannels.data.length;
    const isLastPage = totalFetchedChannels === quantity;

    if (hasNextPage) {
      expect(fetchedListChannels.paging.nextPageToken).toBeDefined();

      // TODO https://github.com/halonext/ziichat-issues/issues/22025
      // const lastFetchedChannel =
      //   fetchedListChannels.data[fetchedListChannels.data.length - 1];
      //
      // const channelMetadataList =
      //   fetchedListChannels.includes as V3ChannelMetadata[];
      //
      // const channelMetadata = channelMetadataList.find(
      //   (channel) => channel.channelId == lastFetchedChannel.channel?.channelId,
      // );
      //
      // const lastMsgId = channelMetadata?.lastMessageId;
      // expect(lastMsgId).toEqual(nextPageToken);
    }

    if (hasPreviousPage) {
      expect(fetchedListChannels.paging.prevPageToken).toBeDefined();

      // const firstFetchedChannel = fetchedListChannels.data[0];
      // const lastMsgId = firstFetchedChannel.channelMetadata.lastMessageId;
      // expect(lastMsgId).toEqual(prevPageToken);
    }

    if (isLastPage) {
      expect(totalFetchedChannels).toEqual(quantity);
      totalFetchedChannels -= fetchedListChannels.data.length;

      expect(fetchedListChannels.paging.hasPrev).toEqual(true);
      expect(fetchedListChannels.paging.hasNext).toEqual(false);
      expect(fetchedListChannels.paging.nextPageToken).toBeUndefined();
    }
  }

  // First prev from end to start list
  while (hasPreviousPage) {
    const request = prevPageToken ? { limit, prevPageToken } : { limit };

    const fetchedListChannels = await getResponseSuccess(
      request,
      fetchList,
      headers,
    );

    if (!fetchedListChannels.data || !fetchedListChannels.paging)
      throw new Error(NULL_UNDEFINED_ERROR);
    expect(fetchedListChannels.data.length).toBeLessThanOrEqual(limit);

    hasPreviousPage = fetchedListChannels.paging.hasPrev;
    hasNextPage = fetchedListChannels.paging.hasNext;
    prevPageToken = fetchedListChannels.paging.prevPageToken;
    nextPageToken = fetchedListChannels.paging.nextPageToken;
    totalFetchedChannels -= fetchedListChannels.data.length;

    fetchedListChannels.data.forEach((fetchedChannel, index) => {
      const originalChannel = originalList[totalFetchedChannels + index];
      expect(originalChannel).toMatchObject(fetchedChannel);
    });

    const isFirstPage = totalFetchedChannels === 0;

    if (hasNextPage) {
      expect(fetchedListChannels.paging.nextPageToken).toBeDefined();

      // const lastFetchedChannel =
      //   fetchedListChannels.data[fetchedListChannels.data.length - 1];
      // const lastMsgId = lastFetchedChannel.channelMetadata?.lastMessageId;
      // expect(lastMsgId).toEqual(nextPageToken);
    }

    if (hasPreviousPage) {
      expect(fetchedListChannels.paging.prevPageToken).toBeDefined();

      // const firstFetchedChannel = fetchedListChannels.data[0];
      // const lastMsgId = firstFetchedChannel.channelMetadata?.lastMessageId;
      // expect(lastMsgId).toEqual(prevPageToken);
    }

    if (isFirstPage) {
      expect(totalFetchedChannels).toEqual(0);
      totalFetchedChannels += fetchedListChannels.data.length;

      expect(fetchedListChannels.paging.hasPrev).toEqual(false);
      expect(fetchedListChannels.paging.hasNext).toEqual(true);
      expect(fetchedListChannels.paging.prevPageToken).toBeUndefined();
    }
  }

  // Second next from start to end list
  while (hasNextPage) {
    const request = nextPageToken ? { limit, nextPageToken } : { limit };

    const fetchedListChannels = await getResponseSuccess(
      request,
      fetchList,
      headers,
    );

    if (!fetchedListChannels.data || !fetchedListChannels.paging)
      throw new Error(NULL_UNDEFINED_ERROR);
    expect(fetchedListChannels.data.length).toBeLessThanOrEqual(limit);

    hasNextPage = fetchedListChannels.paging.hasNext;
    hasPreviousPage = fetchedListChannels.paging.hasPrev;
    nextPageToken = fetchedListChannels.paging.nextPageToken;
    prevPageToken = fetchedListChannels.paging.prevPageToken;

    fetchedListChannels.data.forEach((fetchedChannel, index) => {
      const originalChannel = originalList[totalFetchedChannels + index];
      expect(originalChannel).toMatchObject(fetchedChannel);
    });

    totalFetchedChannels += fetchedListChannels.data.length;

    const isLastPage = totalFetchedChannels === quantity;

    if (hasNextPage) {
      expect(fetchedListChannels.paging.nextPageToken).toBeDefined();

      // const lastFetchedChannel =
      //   fetchedListChannels.data[fetchedListChannels.data.length - 1];
      // const lastMsgId = lastFetchedChannel.channelMetadata?.lastMessageId;
      // expect(lastMsgId).toEqual(nextPageToken);
    }

    if (hasPreviousPage) {
      expect(fetchedListChannels.paging.prevPageToken).toBeDefined();

      // const firstFetchedChannel = fetchedListChannels.data[0];
      // const lastMsgId = firstFetchedChannel.channelMetadata?.lastMessageId;
      // expect(lastMsgId).toEqual(prevPageToken);
    }

    if (isLastPage) {
      expect(totalFetchedChannels).toEqual(quantity);
      totalFetchedChannels -= fetchedListChannels.data.length;

      expect(fetchedListChannels.paging.hasPrev).toEqual(true);
      expect(fetchedListChannels.paging.hasNext).toEqual(false);
      expect(fetchedListChannels.paging.nextPageToken).toBeUndefined();
    }
  }
};

export const expectDMStatus = async (
  requestedUser: V3MockedUser,
  requester: V3MockedUser,
  directMessageStatus: V3DirectMessageStatusEnum,
): Promise<void> => {
  const response = await getResponseSuccess(
    { userId: requestedUser.userId },
    channelViewClient.getDmChannel,
    createHeaders(requester.token as string),
  );

  expect(response.data?.channel?.dmStatus).toBe(directMessageStatus);
};
