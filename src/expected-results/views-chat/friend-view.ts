import {
  V3Friend,
  V3FriendStatusEnum,
  V3User,
} from '../../../utils/http-client/cloudevent-client';
import { V3UserStatusData } from '../../../utils/http-client/commands-user-data-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import friendViewClient from '../../helpers/friend-view-service';
import { createHeaders, getResponseSuccess } from '../../helpers/shared/common';
import { HEADERS } from '../../tests/const';
import { expectFriendData } from '../commands-chat/friend';
import { expectIncludesUsers } from './user-view';

export const expectStatusDataInListFriend = async (
  requestedUser: V3MockedUser,
  requesterMetadata: HEADERS,
  statusData: V3UserStatusData | undefined | null = undefined,
): Promise<void> => {
  const listFriendsRes = await getResponseSuccess(
    {},
    friendViewClient.listFriends,
    requesterMetadata,
  );
  const friend = listFriendsRes.includes?.users?.find(
    (friend) => friend.userId === requestedUser.userId,
  );
  expect(friend?.statusData).toEqual(statusData);
};

export const isExistInListFriend = async (
  sender: V3MockedUser,
  receiver: V3MockedUser,
  headers: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    friendViewClient.listFriends,
    headers,
  );
  const hasFriendRequest = result.data?.some((item) => {
    return (
      item?.friend?.requestedFromUserId === sender.userId &&
      item?.friend?.requestedToUserId === receiver.userId
    );
  });
  expect(hasFriendRequest).toBeTruthy();
};

export const isNotExistInListFriend = async (
  sender: V3MockedUser,
  receiver: V3MockedUser,
  headers: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    friendViewClient.listFriends,
    headers,
  );
  const hasFriendRequest = result.data?.some((item) => {
    return (
      item?.friend?.requestedFromUserId === sender.userId &&
      item?.friend?.requestedToUserId === receiver.userId
    );
  });
  expect(hasFriendRequest).toBeFalsy();
};

export const isExistInListInComingFriendRequests = async (
  sender: V3MockedUser,
  receiver: V3MockedUser,
  headers: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    friendViewClient.listInComingFriendRequests,
    headers,
  );

  const hasFriendRequest = result.data?.some((item) => {
    return (
      item.friend?.requestedFromUserId === sender.userId &&
      item.friend?.requestedToUserId === receiver.userId &&
      item.friend?.status ===
        V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_RECEIVED
    );
  });
  expect(hasFriendRequest).toBeTruthy();
};

export const isExistInListOutGoingFriendRequests = async (
  sender: V3MockedUser,
  receiver: V3MockedUser,
  headers: HEADERS,
): Promise<void> => {
  const result = await getResponseSuccess(
    {},
    friendViewClient.listOutGoingFriendRequests,
    headers,
  );

  const hasFriendRequest = result.data?.some((item) => {
    return (
      item.friend?.requestedFromUserId === sender.userId &&
      item.friend?.requestedToUserId === receiver.userId &&
      item.friend?.status === V3FriendStatusEnum.FRIEND_STATUS_ENUM_REQUEST_SENT
    );
  });
  expect(hasFriendRequest).toBeTruthy();
};

export const expectEmptyListFriendRequests = async (
  headers: HEADERS,
  method:
    | typeof friendViewClient.listInComingFriendRequests
    | typeof friendViewClient.listOutGoingFriendRequests,
): Promise<void> => {
  const result = await getResponseSuccess({}, method, headers);
  expect(result.data).toStrictEqual([]);
};

export const expectGetFriendResponse = async (
  actor: V3MockedUser,
  recipient: V3MockedUser,
  expectedData: V3Friend,
  isDeleteFriendRequest = false,
): Promise<void> => {
  const result = await getResponseSuccess(
    { userId: recipient.userId },
    friendViewClient.getFriend,
    createHeaders(actor.token as string),
  );

  const { data, includes } = result;

  expectFriendData(
    expectedData,
    data?.friend as V3Friend,
    isDeleteFriendRequest,
  );

  await expectIncludesUsers([actor, recipient], includes?.users as V3User[]);
};
