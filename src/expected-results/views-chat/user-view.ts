import { getPrefixMockUser } from '../../../jest-e2e';
import {
  V3FriendStatusEnum,
  V3MediaPermissionSettingEnum,
  V3UserScopeEnum,
  V3UserTypeEnum,
} from '../../../utils/http-client/cloudevent-client';
import { V3UserStatusData } from '../../../utils/http-client/commands-user-data-client';
import {
  V3MockedUser,
  V3UserBadgeTypeEnum,
} from '../../../utils/http-client/faker-client';
import { V3UserAvatarTypeEnum } from '../../../utils/http-client/suggestion-client';
import {
  HttpResponse,
  ListBlockedUsersParams,
  ListUserVisitedProfileParams,
  V3GetUserByUserIdRequest,
  V3GetUserByUsernameRequest,
  V3ListBlockedUsersResponse,
  V3ListUserVisitedProfileResponse,
  V3MeResponse,
  V3Profile,
  V3SyncUsersResponse,
  V3User,
  V3UserIdentification,
  V3UserView,
  V3VisitedProfileData,
} from '../../../utils/http-client/views-chat-client';
import { mockUsersData } from '../../helpers/internal-faker-service';
import {
  assertIsDate,
  assertIsString,
  createHeaders,
  deletePresenceDataObj,
  expectGreaterThanTime,
  getResponseSuccess,
  removeFields,
} from '../../helpers/shared/common';
import {
  getUserProfile,
  userViewClient,
} from '../../helpers/user-view-service';
import {
  HEADERS,
  PREFIX_CONNECT_LINK,
  ZIICHAT_BOT_USERID,
  ZIICHAT_BOT_USERNAME,
} from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';

export const isExistInListUserStatus = async (
  expectedStatusData: V3UserStatusData,
  metadataList: HEADERS[],
  userId: string | null = null,
): Promise<void> => {
  for (const metadata of metadataList) {
    const listUserStatusRes = await getResponseSuccess(
      {},
      userViewClient.listUserStatus,
      metadata,
    );

    if (userId) {
      const user = listUserStatusRes.data?.find(
        (user: V3User) => user.userId === userId,
      );
      expect(user?.statusData).toEqual(expectedStatusData);
    } else {
      if (!listUserStatusRes.data) throw new Error(NULL_UNDEFINED_ERROR);
      //TODO https://github.com/halonext/ziichat-issues/issues/21098
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      delete listUserStatusRes.data[0].statusData?.userId;

      expect(listUserStatusRes.data[0].statusData).toEqual(expectedStatusData);
    }
  }
};

export const expectStatusDataInGetMe = async (
  requesterMetadata: HEADERS,
  statusData: V3UserStatusData | undefined | null = undefined,
): Promise<void> => {
  const getMeData = await getResponseSuccess(
    {},
    userViewClient.getMe,
    requesterMetadata,
  );
  expect(getMeData.data?.statusData).toEqual(statusData);

  expectGreaterThanTime(
    getMeData.data?.createTime as string,
    getMeData.data?.updateTime as string,
  );
};

export const expectStatusDataInGetUser = async (
  requestedUser: V3MockedUser,
  statusData: V3UserStatusData | undefined | null = undefined,
): Promise<void> => {
  const newUser = await mockUsersData(getPrefixMockUser(), 1);
  const headers = createHeaders(newUser[0].token as string);

  let getUserData = await getResponseSuccess(
    { userId: requestedUser.userId },
    userViewClient.getUser,
    headers,
  );
  expect(getUserData.data?.statusData).toEqual(statusData);
  expectGreaterThanTime(
    getUserData.data?.createTime as string,
    getUserData.data?.updateTime as string,
  );

  getUserData = await getResponseSuccess(
    { username: requestedUser.username },
    userViewClient.getUserByUsername,
    headers,
  );
  expect(getUserData.data?.statusData).toEqual(statusData);
  expectGreaterThanTime(
    getUserData.data?.createTime as string,
    getUserData.data?.updateTime as string,
  );
};

export const isNotExistInListUserStatus = async (
  userId: string,
  metadataList: HEADERS[],
): Promise<void> => {
  for (const metadata of metadataList) {
    const listUserStatusRes = await getResponseSuccess(
      {},
      userViewClient.listUserStatus,
      metadata,
    );

    expect(
      listUserStatusRes.data?.some((user: V3User) => user.userId === userId),
    ).toBeFalsy();
  }
};

export const isExistInListUserVisitedProfile = async (
  userId: string,
  metadata: HEADERS,
): Promise<void> => {
  const response = await getResponseSuccess(
    {},
    userViewClient.listUserVisitedProfile,
    metadata,
  );

  const { data, paging } = response;
  expect(paging?.hasNext).toBeFalsy();
  expect(paging?.hasPrev).toBeFalsy();
  expect(data).toBeDefined();

  expect(
    data?.some((userInList) => userInList.user?.userId == userId),
  ).toBeTruthy();
};

export const isNotExistInListUserVisitedProfile = async (
  userId: string,
  metadata: HEADERS,
): Promise<void> => {
  const response = await getResponseSuccess(
    {},
    userViewClient.listUserVisitedProfile,
    metadata,
  );

  const { data, paging } = response;
  expect(paging?.hasNext).toBeFalsy();
  expect(paging?.hasPrev).toBeFalsy();

  data
    ? expect(
        data.every((userInList) => userInList.user?.userId !== userId),
      ).toBeTruthy()
    : expect(data).toBeUndefined();
};

export const expectAvatarFields = (
  profile: V3Profile,
  expectedAvatar: string,
  avatarType = V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
  haveAvatarType = true,
): void => {
  expect(profile.avatar).toEqual(expectedAvatar);
  expect(profile.originalAvatar).toEqual(expectedAvatar);

  if (haveAvatarType) {
    expect(profile.avatarType).toEqual(avatarType);
  }
};

export const expectUpdateUserVideoAvatar = (
  user: V3User,
  avatarPath: string,
): void => {
  expect(user.profile?.videoAvatar).toEqual(avatarPath);
  expect(user.profile?.avatarType).toEqual(
    V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_VIDEO,
  );

  expectGreaterThanTime(user.createTime as string, user.updateTime as string);
};

export const expectDeleteUserVideoAvatar = (profile: V3Profile): void => {
  expect(profile?.videoAvatar).toEqual('');
  expect(profile?.avatarType).toEqual(
    V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
  );
};

export const expectForUserDataList = async (
  users: V3User[],
  userIds: string[],
): Promise<void> => {
  const [userNew] = await mockUsersData(getPrefixMockUser(), 1);
  for (const userId of userIds) {
    const userInfo = await getResponseSuccess(
      { userId: userId },
      userViewClient.getUser,
      createHeaders(userNew.token as string),
    );

    const actualUser = users.find((user) => {
      return user.userId == userId;
    });
    if (!actualUser) throw new Error(NULL_UNDEFINED_ERROR);
    deletePresenceDataObj(actualUser);
    expect(userInfo?.data).toMatchObject(actualUser);
  }
};

export const expectGetMeData = async (user: V3MockedUser): Promise<void> => {
  const response = await getResponseSuccess(
    {},
    userViewClient.getMe,
    createHeaders(user.token as string),
  );
  const actualData = response.data as V3MeResponse;

  expect(actualData.userId).toEqual(user.userId);
  expect(actualData.username).toEqual(user.username);

  assertIsDate(actualData.createTime as string);
  assertIsDate(actualData.updateTime as string);

  assertIsString(actualData.profile?.displayName as string);
  expect(actualData.profile?.avatar).not.toBeNull();
  expect(actualData.profile?.originalAvatar).not.toBeNull();
  expect(actualData.profile?.userBadgeType).toEqual(user.badge);
  expect(actualData.profile?.avatarType).toEqual(
    V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
  );

  expect(actualData.statusData).toBeNull();
  expect(actualData.globalNotificationStatus).toBeTruthy();
  expect(actualData.userConnectLink).toContain(PREFIX_CONNECT_LINK);

  expect(actualData.setting?.security?.recoveryCode?.enable).toBeTruthy();
  expect(actualData.setting?.callScope).toEqual(V3UserScopeEnum.ONLY_FRIENDS);
  expect(actualData.setting?.messageScope).toEqual(V3UserScopeEnum.EVERYBODY);
  //TODO interface error
  // expect(actualData.setting?.privacy?.mediaPermission.value).toEqual(
  //   V3MediaPermissionSettingEnum.MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK,
  // );
};

export const expectGetUserData = async (
  user: V3MockedUser,
  request: V3GetUserByUserIdRequest | V3GetUserByUsernameRequest,
): Promise<void> => {
  let response;
  const [mockUser] = await mockUsersData(getPrefixMockUser(), 1);

  if ('userId' in request) {
    response = await getResponseSuccess(
      request,
      userViewClient.getUser,
      createHeaders(mockUser.token as string),
    );
  } else if ('username' in request) {
    response = await getResponseSuccess(
      request as V3GetUserByUsernameRequest,
      userViewClient.getUserByUsername,
      createHeaders(mockUser.token as string),
    );
  } else {
    throw new Error('Invalid request type');
  }

  const actualData = response.data as V3UserView;

  expect(actualData.userId).toEqual(user.userId);
  expect(actualData.username).toEqual(user.username);

  assertIsDate(actualData.createTime as string);
  assertIsDate(actualData.updateTime as string);

  assertIsString(actualData.profile?.displayName as string);
  expect(actualData.profile?.avatar).not.toBeNull();
  expect(actualData.profile?.originalAvatar).not.toBeNull();
  expect(actualData.profile?.userBadgeType).toEqual(user.badge);
  expect(actualData.profile?.avatarType).toEqual(
    V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
  );
  expect(actualData.profile?.cover).toEqual('');
  expect(actualData.profile?.decoratedAvatar).toEqual('');
  expect(actualData.profile?.originalDecoratedAvatar).toEqual('');
  expect(actualData.profile?.videoAvatar).toEqual('');

  expect(actualData.userType).toEqual(V3UserTypeEnum.USER_TYPE_ENUM_DEFAULT);

  expect(actualData.presenceData).toBeDefined();
  expect(actualData.statusData).toBeNull();
  expect(actualData.friendData).toBeDefined();

  expect(actualData.mediaPermissionSetting).toEqual(
    V3MediaPermissionSettingEnum.MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK,
  );
  expect(actualData.blocked).toEqual(false);
};

export const expectGetUserBot = async (
  request: V3GetUserByUserIdRequest | V3GetUserByUsernameRequest,
): Promise<void> => {
  let response;
  const [mockUser] = await mockUsersData(getPrefixMockUser(), 1);

  if ('userId' in request) {
    response = await getResponseSuccess(
      request,
      userViewClient.getUser,
      createHeaders(mockUser.token as string),
    );
  } else if ('username' in request) {
    response = await getResponseSuccess(
      request as V3GetUserByUsernameRequest,
      userViewClient.getUserByUsername,
      createHeaders(mockUser.token as string),
    );
  } else {
    throw new Error('Invalid request type');
  }

  const actualData = response.data as V3UserView;

  expect(actualData.userId).toEqual(ZIICHAT_BOT_USERID);
  expect(actualData.username).toEqual(ZIICHAT_BOT_USERNAME);

  assertIsDate(actualData.createTime as string);
  assertIsDate(actualData.updateTime as string);

  expect(actualData.profile?.avatar).not.toBeNull();
  expect(actualData.profile?.originalAvatar).not.toBeNull();
  expect(actualData.profile?.displayName).toEqual(ZIICHAT_BOT_USERNAME);
  expect(actualData.profile?.userBadgeType).toEqual(
    V3UserBadgeTypeEnum.USER_BADGE_TYPE_YELLOW,
  );
  expect(actualData.profile?.avatarType).toEqual(
    V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
  );
  expect(actualData.profile?.cover).toEqual('');
  expect(actualData.profile?.decoratedAvatar).toEqual('');
  expect(actualData.profile?.originalDecoratedAvatar).toEqual('');
  expect(actualData.profile?.videoAvatar).toEqual('');

  expect(actualData.userType).toEqual(V3UserTypeEnum.USER_TYPE_ENUM_BOT);

  expect(actualData.presenceData).toBeDefined();
  expect(actualData.statusData).toBeNull();
  expect(actualData.friendData).toBeDefined();

  expect(actualData.mediaPermissionSetting).toEqual(
    V3MediaPermissionSettingEnum.MEDIA_PERMISSION_SETTING_ENUM_ALLOW,
  );
  expect(actualData.blocked).toEqual(false);
};

export const checkPaginationUserView = async (
  options: {
    quantity: number;
    limit: number;
  },
  originalUserList: V3User[] | V3VisitedProfileData[],
  mainHeaders: HEADERS,
  fetchList: (
    request: ListBlockedUsersParams | ListUserVisitedProfileParams,
    headers: HEADERS,
  ) => Promise<
    HttpResponse<
      V3ListBlockedUsersResponse | V3ListUserVisitedProfileResponse,
      any
    >
  >,
): Promise<void> => {
  const { quantity, limit } = options;
  let nextPageToken: string | undefined = '';
  let prevPageToken: string | undefined = '';

  let hasNextPage: boolean | undefined = true;
  let hasPreviousPage: boolean | undefined = false;

  let totalUsers = 0;

  let isFirstPage = true;
  let isLastPage = false;

  while (hasNextPage) {
    const request = nextPageToken ? { limit, nextPageToken } : { limit };
    const userResponse = await getResponseSuccess(
      request,
      fetchList,
      mainHeaders,
    );
    if (!userResponse.data) throw new Error(NULL_UNDEFINED_ERROR);
    if (!userResponse.paging) throw new Error(NULL_UNDEFINED_ERROR);

    expect(userResponse.data.length).toBeLessThanOrEqual(limit);

    hasNextPage = userResponse.paging.hasNext;
    hasPreviousPage = userResponse.paging.hasPrev;
    nextPageToken = userResponse.paging.nextPageToken;
    prevPageToken = userResponse.paging.prevPageToken;

    for (const [index, newListUsers] of userResponse.data.entries()) {
      const originalUser = originalUserList[totalUsers + index];
      deletePresenceDataObj(newListUsers);
      deletePresenceDataObj(originalUser);
      expect(newListUsers).toEqual(originalUser);
    }

    totalUsers = totalUsers + userResponse.data.length;

    isLastPage = totalUsers === quantity;

    if (isFirstPage) {
      expect(hasNextPage).toBe(true);
      expect(hasPreviousPage).toBe(false);

      expect(nextPageToken).toBeDefined();
      expect(prevPageToken).toBeUndefined();

      isFirstPage = false;
    } else if (isLastPage) {
      totalUsers = totalUsers - userResponse.data.length;

      expect(hasPreviousPage).toEqual(true);
      expect(hasNextPage).toEqual(false);

      expect(nextPageToken).toBeUndefined();
      expect(prevPageToken).toBeDefined();

      isLastPage = false;
    } else {
      expect(hasNextPage).toBe(true);
      expect(hasPreviousPage).toBe(true);

      expect(nextPageToken).toBeDefined();
      expect(prevPageToken).toBeDefined();
    }
  }

  while (hasPreviousPage) {
    const request = prevPageToken ? { limit, prevPageToken } : { limit };
    const userResponse = await getResponseSuccess(
      request,
      fetchList,
      mainHeaders,
    );

    hasPreviousPage = userResponse.paging?.hasPrev;
    hasNextPage = userResponse.paging?.hasNext;
    prevPageToken = userResponse.paging?.prevPageToken;
    nextPageToken = userResponse.paging?.nextPageToken;

    if (!userResponse.data) throw new Error(NULL_UNDEFINED_ERROR);
    totalUsers = totalUsers - userResponse.data.length;

    for (const [index, newListUsers] of userResponse.data.entries()) {
      const originalUser = originalUserList[totalUsers + index];
      deletePresenceDataObj(newListUsers);
      deletePresenceDataObj(originalUser);
      expect(newListUsers).toEqual(originalUser);
    }

    isFirstPage = totalUsers === 0;

    if (isFirstPage) {
      expect(hasPreviousPage).toEqual(false);
      expect(hasNextPage).toEqual(true);

      expect(prevPageToken).toBeUndefined();
      expect(nextPageToken).toBeDefined();

      totalUsers = totalUsers + userResponse.data.length;
    } else {
      expect(hasNextPage).toBe(true);
      expect(hasPreviousPage).toBe(true);

      expect(nextPageToken).toBeDefined();
      expect(prevPageToken).toBeDefined();
    }
  }

  while (hasNextPage) {
    const request = nextPageToken ? { limit, nextPageToken } : { limit };
    const userResponse = await getResponseSuccess(
      request,
      fetchList,
      mainHeaders,
    );
    if (!userResponse.data) throw new Error(NULL_UNDEFINED_ERROR);
    if (!userResponse.paging) throw new Error(NULL_UNDEFINED_ERROR);

    expect(userResponse.data.length).toBeLessThanOrEqual(limit);

    hasNextPage = userResponse.paging.hasNext;
    hasPreviousPage = userResponse.paging.hasPrev;
    nextPageToken = userResponse.paging.nextPageToken;
    prevPageToken = userResponse.paging.prevPageToken;

    for (const [index, newListUsers] of userResponse.data.entries()) {
      const originalUser = originalUserList[totalUsers + index];
      deletePresenceDataObj(newListUsers);
      deletePresenceDataObj(originalUser);
      expect(newListUsers).toEqual(originalUser);
    }
    totalUsers = totalUsers + userResponse.data.length;

    isLastPage = totalUsers === quantity;

    if (isLastPage) {
      totalUsers = totalUsers - userResponse.data.length;

      expect(hasPreviousPage).toEqual(true);
      expect(hasNextPage).toEqual(false);

      expect(nextPageToken).toBeUndefined();
      expect(prevPageToken).toBeDefined();
    } else {
      expect(hasNextPage).toBe(true);
      expect(hasPreviousPage).toBe(true);

      expect(nextPageToken).toBeDefined();
      expect(prevPageToken).toBeDefined();
    }
  }
};

export const expectSyncResponseUndefined = (
  response: V3SyncUsersResponse,
): void => {
  const { data, userDeleted } = response;
  expect(data).toStrictEqual([]);
  expect(userDeleted).toStrictEqual([]);
};

export const getTimeForSync = async (
  time: string,
  user: V3MockedUser,
): Promise<string> => {
  const [newUser] = await mockUsersData(getPrefixMockUser(), 1);

  const { data } = await getResponseSuccess(
    {
      userId: user.userId,
    },
    userViewClient.getUser,
    createHeaders(newUser.token as string),
  );
  const baseTime = new Date(data?.presenceData?.lastUpdateTime as string);

  if (time.includes('=')) return baseTime.toISOString();
  if (time.includes('<')) baseTime.setSeconds(baseTime.getSeconds() - 5);
  else baseTime.setHours(baseTime.getHours() + 1);

  return baseTime.toISOString();
};

export const expectSyncUserResponseData = (
  syncUsersResponse: V3SyncUsersResponse,
  userInfo: V3UserView,
  expectUserDeleted: V3UserIdentification[] = [],
): void => {
  const { data, userDeleted, syncTime } = syncUsersResponse;
  if (!data) throw new Error(NULL_UNDEFINED_ERROR);
  assertIsDate(syncTime as string);
  expect(data).toHaveLength(1);
  delete data[0].presenceData;
  expect(data[0]).toMatchObject(userInfo);
  expect(userDeleted).toStrictEqual(expectUserDeleted);
};

export const expectForListUserStatus = async (
  headers: HEADERS,
  expectedData: V3UserView[],
): Promise<void> => {
  const listUserStatusRes = await getResponseSuccess(
    {},
    userViewClient.listUserStatus,
    headers,
  );
  if (!listUserStatusRes.data) throw new Error(NULL_UNDEFINED_ERROR);

  expect(listUserStatusRes.data).toHaveLength(expectedData.length);
  for (const [index, user] of expectedData.entries()) {
    delete user.friendData;
    delete user.mediaPermissionSetting;
    delete user.blocked;
    expect(listUserStatusRes.data[index]).toMatchObject(user);
  }
};

export const expectFriendStatus = async (
  requestedUser: V3MockedUser,
  requester: V3MockedUser,
  friendStatus: V3FriendStatusEnum,
): Promise<void> => {
  let response = await getResponseSuccess(
    { userId: requestedUser.userId },
    userViewClient.getUser,
    createHeaders(requester.token as string),
  );

  expect(response.data?.friendData?.status).toBe(friendStatus);

  response = await getResponseSuccess(
    { username: requestedUser.username },
    userViewClient.getUserByUsername,
    createHeaders(requester.token as string),
  );

  expect(response.data?.friendData?.status).toBe(friendStatus);
};

export const expectIncludesUsers = async (
  listUser: V3User[],
  actualData: V3User[],
): Promise<void> => {
  for (const user of listUser) {
    const expectedUser = await getUserProfile(user);

    const matchedUsers = actualData.filter(
      (u) => u.userId === expectedUser.userId,
    );
    expect(matchedUsers.length).toBe(1);
    expect(
      removeFields(matchedUsers[0], ['presenceData', 'statusData']),
    ).toEqual(
      removeFields(expectedUser, [
        'blocked',
        'friendData',
        'mediaPermissionSetting',
        'statusData',
      ]),
    );
  }
};
