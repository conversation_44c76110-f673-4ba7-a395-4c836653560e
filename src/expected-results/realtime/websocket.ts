import * as WebSocket from 'ws';

import {
  V3AllUserMessagesDeletedEventData,
  V3ChannelCreatedEventData,
  V3ChannelDeletedEventData,
  V3ChannelDestinationCloudEvent,
  V3ChannelTypeEnum,
  V3ChannelUpdatedEventData,
  V3CoverPhotoCreatedEventData,
  V3DeleteUserVisitedProfileEventData,
  V3DirectMessageStatusEnum,
  V3Friend,
  V3GatewayConnectedEventData,
  V3IncomingFriendRequestAcceptedEventData,
  V3IncomingFriendRequestCanceledEventData,
  V3IncomingFriendRequestCreatedEventData,
  V3IncomingMessageRequestAcceptedEventData,
  V3MessageCreatedEventData,
  V3MessageRequestRejectedEventData,
  V3OutgoingFriendRequestAcceptedEventData,
  V3OutgoingFriendRequestCanceledEventData,
  V3OutgoingFriendRequestCreatedEventData,
  V3UserAvatarDeletedEventData,
  V3UserAvatarTypeEnum,
  V3UserAvatarUpdatedEventData,
  V3UserBlockedEventData,
  V3UserDisplayNameUpdatedEventData,
  V3UserStatusCreatedEventData,
  V3UserUnblockedEventData,
  V3UserVisitedProfileEventData,
} from '../../../utils/http-client/cloudevent-client';
import {
  V3Channel,
  V3CreateChannelRequest,
  V3CreateChannelResponse,
  V3DataInclude,
  V3DeleteChannelAvatarRequest,
  V3DeleteChannelRequest,
  V3UpdateChannelAvatarRequest,
  V3UpdateChannelNameRequest,
} from '../../../utils/http-client/commands-chat-client';
import { V3Message } from '../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import { V3GetChannelResponse } from '../../../utils/http-client/views-chat-client';
import channelClient from '../../helpers/channel-service';
import channelViewClient from '../../helpers/channel-view-service';
import messageViewClient, {
  getLastMsgInBot,
} from '../../helpers/message-view-service';
import {
  assertIsDate,
  createHeaders,
  deletePresenceDataObj,
  getResponseSuccess,
  removeFields,
  wait,
} from '../../helpers/shared/common';
import {
  cloudEvent,
  FriendDataWS,
  getActorSource,
  openConnectionOfUser,
  receiveWebSocketEvent,
  resumeWebSocketById,
  resumeWebSocketByTime,
  UserWS,
} from '../../helpers/websocket-service';
import {
  CLOUD_EVENT_SOURCE_SYSTEM,
  cloudEventType,
  HEADERS,
  WS_VERSION,
  ZIICHAT_BOT_USERID,
} from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';

// Expect EventData
export const expectGatewayConnectedEvent = async (
  user: V3MockedUser,
  actualData: V3GatewayConnectedEventData,
): Promise<void> => {
  expect(actualData.userId).toEqual(user.userId);
  expect(actualData.deviceId).toBeDefined();
  expect(actualData.message).toEqual('Hello');
};

export const expectSourceFromUser = (
  expectSource: string,
  actualSource: string,
): void => {
  actualSource = actualSource.split('?')[1];
  expectSource = expectSource.split('?')[1];
  expect(actualSource).toEqual(expectSource);
};

export const expectChannelCreatedEventData = (
  actorChannel: V3Channel,
  includes: V3DataInclude,
  actualData: V3ChannelCreatedEventData,
): void => {
  expect(actualData.channel).toEqual(actorChannel);
  expect(actualData.includes).toEqual(includes);
};

export const expectReconnectionEndedEventData = (
  expectTotalEvent: number,
  actualData: object,
): void => {
  const parsedActualData = actualData as { total: number; message: string };
  expect(parsedActualData.total).toEqual(expectTotalEvent);
  expect(parsedActualData.message).toEqual('ended');
};

export const expectMessageCreatedEventData = (
  actorMessage: V3Message,
  includes: V3DataInclude,
  actualData: V3MessageCreatedEventData,
): void => {
  if (!actualData.message || !actualData.includes)
    throw new Error(NULL_UNDEFINED_ERROR);
  deletePresenceDataObj(actualData);

  expect(actorMessage).toMatchObject(actualData.message);
  expect(includes).toMatchObject(actualData.includes);
};

export const expectUserDisplayNameUpdatedEventData = (
  actor: V3MockedUser,
  displayName: string,
  actualData: V3UserDisplayNameUpdatedEventData,
): void => {
  expect(actualData.actorId).toEqual(actor.userId);
  expect(actualData.displayName).toEqual(displayName);
};

export const expectUserAvatarUpdatedEventData = (
  expectedData: V3UserAvatarUpdatedEventData,
  actualData: V3UserAvatarUpdatedEventData,
): void => {
  expect(actualData).toEqual(expectedData);
};

export const expectUserAvatarDeletedEventData = (
  expectedData: V3UserAvatarDeletedEventData,
  actualData: V3UserAvatarDeletedEventData,
): void => {
  expect(actualData).toEqual(expectedData);
};

export const expectCoverPhotoEventData = (
  expectedData: V3CoverPhotoCreatedEventData,
  actualData: V3CoverPhotoCreatedEventData,
): void => {
  expect(actualData).toEqual(expectedData);
};

export const expectUserStatusEventData = (
  actualData: V3UserStatusCreatedEventData,
  expectData: V3UserStatusCreatedEventData,
): void => {
  expect(actualData).toEqual(expectData);
};

export const expectVisitedProfileEventData = (
  expectedData: V3UserVisitedProfileEventData,
  actualData: V3UserVisitedProfileEventData,
): void => {
  deletePresenceDataObj(actualData);
  deletePresenceDataObj(expectedData);

  expect(actualData).toEqual(removeFields(expectedData, ['statusData']));
};

export const expectDeleteUserVisitedProfileEventData = (
  expectedData: V3DeleteUserVisitedProfileEventData,
  actualData: V3DeleteUserVisitedProfileEventData,
): void => {
  assertIsDate(actualData.createTime as string);
  assertIsDate(actualData.updateTime as string);
  expect(actualData.createTime).toEqual(actualData.updateTime);

  expect(removeFields(actualData, ['createTime', 'updateTime'])).toEqual(
    expectedData,
  );
};

export const expectChannelUpdatedEventData = (
  channel: V3Channel,
  includes: V3DataInclude,
  actualData: V3ChannelUpdatedEventData,
): void => {
  expect(actualData.channel).toEqual(channel);
  expect(actualData.includes).toEqual(includes);
};

export const expectChannelDeletedEventData = (
  channel: V3Channel,
  actualData: V3ChannelDeletedEventData,
): void => {
  expect(actualData).toEqual({
    workspaceId: channel.workspaceId,
    channelId: channel.channelId,
  });
};

export const expectOutgoingMessageRequestAcceptedEvent = (
  actorChannel: V3Channel,
  includes: V3DataInclude,
  actualData: V3IncomingMessageRequestAcceptedEventData,
): void => {
  expect(actualData.channel).toEqual(actorChannel);
  expect(actualData.includes).toEqual(includes);
};

export const expectIncomingMessageRequestAcceptedEvent = (
  actorChannel: V3Channel,
  includes: V3DataInclude,
  actualData: V3IncomingMessageRequestAcceptedEventData,
): void => {
  expect(actualData.channel).toEqual(actorChannel);
  expect(actualData.includes).toEqual(includes);
};

export const expectMessageRequestRejectedEvent = (
  actor: V3MockedUser,
  recipient: V3MockedUser,
  actorSendDmMessageResponse: V3Message,
  actualData: V3MessageRequestRejectedEventData,
): void => {
  expect(actualData.workspaceId).toBe(actorSendDmMessageResponse.workspaceId);
  expect(actualData.channelId).toBe(actorSendDmMessageResponse.channelId);
  expect(actualData.actorId).toBe(actor.userId);
  expect(actualData.targetUserId).toBe(recipient.userId);
};

export const expectAllUserMessagesDeletedEventData = (
  channel: V3Channel,
  actor: V3MockedUser,
  destination: V3ChannelDestinationCloudEvent,
  actualData: V3AllUserMessagesDeletedEventData,
): void => {
  expect(actualData.workspaceId).toEqual(channel.workspaceId);
  expect(actualData.channelId).toEqual(channel.channelId);
  expect(actualData.actorId).toEqual(actor.userId);
  expect(actualData.destination).toEqual(destination);
};

export const expectUserBlockedEventData = (
  expectedData: V3UserBlockedEventData,
  actualData: V3UserBlockedEventData,
): void => {
  expect(actualData).toEqual(expectedData);
};

export const expectUserUnblockedEventData = (
  expectedData: V3UserUnblockedEventData,
  actualData: V3UserUnblockedEventData,
): void => {
  expect(actualData).toEqual(expectedData);
};

export const expectFriendRequestCreatedEventData = async (
  userAddFriendGetFriendRes: FriendDataWS,
  actualData:
    | V3OutgoingFriendRequestCreatedEventData
    | V3IncomingFriendRequestCreatedEventData,
): Promise<void> => {
  deletePresenceDataObj(userAddFriendGetFriendRes);
  deletePresenceDataObj(actualData.includes);

  expect(userAddFriendGetFriendRes.friendRequest).toMatchObject(
    actualData.friendRequest as V3Friend,
  );
  expect(userAddFriendGetFriendRes.includes).toMatchObject(
    actualData.includes as V3DataInclude,
  );
};

export const expectFriendRequestCanceledEventData = async (
  actorGetFriendRes: FriendDataWS,
  actualData:
    | V3OutgoingFriendRequestCanceledEventData
    | V3IncomingFriendRequestCanceledEventData,
): Promise<void> => {
  deletePresenceDataObj(actorGetFriendRes);
  deletePresenceDataObj(actualData.includes);

  expect(actorGetFriendRes.friendRequest).toMatchObject(
    actualData.friendRequest as V3Friend,
  );
  expect(actorGetFriendRes.includes).toMatchObject(
    actualData.includes as V3DataInclude,
  );
};

export const expectFriendRequestAcceptedEventData = async (
  actorGetFriendRes: FriendDataWS,
  actualData:
    | V3OutgoingFriendRequestAcceptedEventData
    | V3IncomingFriendRequestAcceptedEventData,
): Promise<void> => {
  deletePresenceDataObj(actorGetFriendRes);
  deletePresenceDataObj(actualData.includes);

  expect(actorGetFriendRes.friendRequest).toMatchObject(
    actualData.friendRequest as V3Friend,
  );
  expect(actorGetFriendRes.includes).toMatchObject(
    actualData.includes as V3DataInclude,
  );
};

/* End expect EventData */

// Expect Resume ws
export const expectResumeWebsocket = async (
  headers: HEADERS,
  expectedEvents: cloudEvent[],
  resumeType: (websocket: WebSocket, lastEvent: cloudEvent) => Promise<void>,
  index = 0,
): Promise<void> => {
  const actualResumeEvents: cloudEvent[] = [];
  let wsResume: WebSocket;

  try {
    // Open WebSocket connection and receive events
    wsResume = new WebSocket(await openConnectionOfUser(headers));
    await receiveWebSocketEvent(wsResume, actualResumeEvents);
    await wait(1000);

    // Resume WebSocket with specified type
    await resumeType(wsResume, expectedEvents[index]);
    await wait(1000);

    // Compare actual events with expected events
    for (let i = 1; i < expectedEvents.length - index; i++) {
      expect(deletePresenceDataObj(actualResumeEvents[i])).toEqual(
        deletePresenceDataObj(expectedEvents[i + index]),
      );
    }

    // Validate the last event RECONNECT_ENDED_EVENT
    const lastEvent = actualResumeEvents[actualResumeEvents.length - 1];
    expect(lastEvent.type).toEqual(cloudEventType.RECONNECT_ENDED_EVENT);
    expectSourceFromUser(CLOUD_EVENT_SOURCE_SYSTEM, lastEvent.source);
    expectReconnectionEndedEventData(
      expectedEvents.length - 1 - index,
      lastEvent.data,
    );

    // Close WebSocket connection
    await wsResume.close();

    // Recursively call if there are more events to process
    if (index < expectedEvents.length - 1) {
      await expectResumeWebsocket(
        headers,
        expectedEvents,
        resumeType,
        index + 1,
      );
    }
  } catch (error: unknown) {
    // Handle errors
    await wsResume.close();
    console.error('Error in expectResumeWebsocket:', error);
    throw new Error('expectResumeWebsocket failed');
  }
};

export const expectResumeLastEventWebsocket = async (
  metadata: HEADERS,
  expectedEvents: cloudEvent[],
  resumeType: (websocket: WebSocket, lastEvent: cloudEvent) => Promise<void>,
  expectedLastEvent: cloudEvent,
  index = 0,
): Promise<void> => {
  const actualResumeEvents: cloudEvent[] = [];
  let wsResume: WebSocket;
  const isNotLastEvent = expectedEvents[index].type !== expectedLastEvent.type;
  const expectedTotalEvent = isNotLastEvent ? 1 : 0;

  try {
    // Open WebSocket connection and receive events
    wsResume = new WebSocket(await openConnectionOfUser(metadata));
    await receiveWebSocketEvent(wsResume, actualResumeEvents);
    await wait(1000);

    // Resume WebSocket with specified type
    await resumeType(wsResume, expectedEvents[index]);
    await wait(1000);

    // Compare actual events with expectedLastEvent
    if (isNotLastEvent) {
      expect(actualResumeEvents.length).toEqual(3);
      expect(deletePresenceDataObj(actualResumeEvents[1])).toEqual(
        deletePresenceDataObj(expectedLastEvent),
      );
    } else expect(actualResumeEvents.length).toEqual(2);

    // Validate the last event RECONNECT_ENDED_EVENT
    const lastEvent = actualResumeEvents[actualResumeEvents.length - 1];
    expect(lastEvent.type).toEqual(cloudEventType.RECONNECT_ENDED_EVENT);
    expectSourceFromUser(CLOUD_EVENT_SOURCE_SYSTEM, lastEvent.source);
    expectReconnectionEndedEventData(expectedTotalEvent, lastEvent.data);

    // Close WebSocket connection
    await wsResume.close();

    // Recursively call if there are more events to process
    if (index < expectedEvents.length - 1) {
      await expectResumeLastEventWebsocket(
        metadata,
        expectedEvents,
        resumeType,
        expectedLastEvent,
        index + 1,
      );
    }
  } catch (error: unknown) {
    // Handle errors
    await wsResume.close();
    console.error('Error in expectResumeWebsocket:', error);
    throw new Error('expectResumeWebsocket failed');
  }
};

export const expectResumeWS = async (
  paramConnect: UserWS[],
  expectedLastEvent?: cloudEvent,
): Promise<void> => {
  if (!expectedLastEvent) {
    for (const { user, events } of paramConnect) {
      const metadata = createHeaders(user.token as string);
      await expectResumeWebsocket(metadata, events, resumeWebSocketById);
      await expectResumeWebsocket(metadata, events, resumeWebSocketByTime);
    }
    return;
  }

  for (const { user, events } of paramConnect) {
    const metadata = createHeaders(user.token as string);
    await expectResumeLastEventWebsocket(
      metadata,
      events,
      resumeWebSocketById,
      expectedLastEvent,
    );
    await expectResumeLastEventWebsocket(
      metadata,
      events,
      resumeWebSocketByTime,
      expectedLastEvent,
    );
  }
};

export const expectEventsCreateChannel = async (
  request: V3CreateChannelRequest,
  users: UserWS[],
  hasUserIds = false,
): Promise<void> => {
  const [actor, recipient] = users;
  const requesterHeaders = createHeaders(actor.user.token as string);

  const createChannelRes = await getResponseSuccess(
    request,
    channelClient.createChannel,
    requesterHeaders,
  );

  await wait();

  // Expect events for actor
  await expectEventsCreateChannelForActor(actor, createChannelRes);

  // Expect events for recipient
  await expectEventsCreateChannelForRecipient(recipient, hasUserIds);
};

const expectEventsCreateChannelForActor = async (
  actor: UserWS,
  createChannelRes: V3CreateChannelResponse,
): Promise<void> => {
  const expectActorEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.CHANNEL_CREATED_EVENT,
    cloudEventType.MESSAGE_CREATED_EVENT,
  ];

  const actorChannel = createChannelRes?.data?.channel as V3Channel;
  const includes = createChannelRes?.includes as V3DataInclude;

  if (!includes.channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
  const lastMsgId = includes.channelMetadata[0].lastMessageId;

  const getMsgRes = await getResponseSuccess(
    {
      channelId: actorChannel.channelId as string,
      workspaceId: actorChannel.workspaceId as string,
      messageId: lastMsgId,
    },
    messageViewClient.getMessage,
    createHeaders(actor.user.token as string),
  );

  const actorSrc = getActorSource(actor.events);
  expect(actor.events).toHaveLength(expectActorEvents.length);
  for (const [index, event] of actor.events.entries()) {
    expect(event.type).toEqual(expectActorEvents[index]);
    expect(event.version).toEqual(WS_VERSION);

    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(actor.user, event.data);
        break;

      case cloudEventType.CHANNEL_CREATED_EVENT:
        expectSourceFromUser(actorSrc, event.source);
        expectChannelCreatedEventData(actorChannel, includes, event.data);
        break;

      case cloudEventType.MESSAGE_CREATED_EVENT:
        expectSourceFromUser(CLOUD_EVENT_SOURCE_SYSTEM, event.source);
        expectMessageCreatedEventData(
          getMsgRes.data?.message as V3Message,
          getMsgRes.includes as V3DataInclude,
          event.data,
        );
        break;

      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }
};

const expectEventsCreateChannelForRecipient = async (
  recipient: UserWS,
  hasUserIds = false,
): Promise<void> => {
  let inviteMsg;
  const expectRecipientEvents = [cloudEventType.GATEWAY_CONNECTED_EVENT];
  if (hasUserIds) {
    expectRecipientEvents.push(cloudEventType.MESSAGE_CREATED_EVENT);

    const lastMsgInBot = await getLastMsgInBot(recipient.user);
    const inviteMsgId = lastMsgInBot.messageId;
    inviteMsg = await getResponseSuccess(
      { userId: ZIICHAT_BOT_USERID, messageId: inviteMsgId },
      messageViewClient.getDmMessage,
      createHeaders(recipient.user.token as string),
    );
  }

  expect(recipient.events).toHaveLength(expectRecipientEvents.length);
  for (const [index, event] of recipient.events.entries()) {
    expect(event.type).toEqual(expectRecipientEvents[index]);
    expect(event.version).toEqual(WS_VERSION);

    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(recipient.user, event.data);
        break;

      case cloudEventType.MESSAGE_CREATED_EVENT:
        expectSourceFromUser(CLOUD_EVENT_SOURCE_SYSTEM, event.source);
        expectMessageCreatedEventData(
          inviteMsg?.data?.message as V3Message,
          inviteMsg?.includes as V3DataInclude,
          event.data,
        );
        break;

      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }
};

export const expectDeleteUserAvatarEvents = async (
  userWS: UserWS,
): Promise<void> => {
  await wait();
  const expectEventTypes = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.USER_AVATAR_DELETED_EVENT,
  ];

  const actorSrc = getActorSource(userWS.events);
  expect(userWS.events).toHaveLength(expectEventTypes.length);

  for (const [index, event] of userWS.events.entries()) {
    expect(event.type).toEqual(expectEventTypes[index]);
    expect(event.version).toEqual(WS_VERSION);
    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(userWS.user, event.data);
        break;
      case cloudEventType.USER_AVATAR_DELETED_EVENT:
        expectSourceFromUser(actorSrc, event.source);
        expectUserAvatarDeletedEventData(
          {
            actorId: userWS.user.userId,
            avatarType: V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_UNSPECIFIED,
          },
          event.data,
        );
        break;

      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }

  await expectResumeWS([userWS]);
};

export const expectUpdateUserAvatarEvents = async (
  userWS: UserWS,
  expectedAvatarPath: string[],
  avatarType: V3UserAvatarTypeEnum = V3UserAvatarTypeEnum.USER_AVATAR_TYPE_ENUM_PHOTO,
  expectedUserEvents: cloudEventType[] = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.USER_AVATAR_UPDATED_EVENT,
  ],
): Promise<void> => {
  await wait();
  const actorSource = getActorSource(userWS.events);

  expect(userWS.events).toHaveLength(expectedUserEvents.length);
  for (const [index, event] of userWS.events.entries()) {
    expect(event.type).toEqual(expectedUserEvents[index]);
    expect(event.version).toEqual(WS_VERSION);
    switch (event.type) {
      case cloudEventType.GATEWAY_CONNECTED_EVENT:
        await expectGatewayConnectedEvent(userWS.user, event.data);
        break;
      case cloudEventType.USER_AVATAR_UPDATED_EVENT:
        expectSourceFromUser(actorSource, event.source);

        expectUserAvatarUpdatedEventData(
          {
            actorId: userWS.user.userId,
            avatar: expectedAvatarPath[index - 1],
            avatarType: avatarType,
          },
          event.data,
        );
        break;
      default:
        throw new Error('Event have not been handled yet: ' + event.type);
    }
  }

  // Resume
  await expectResumeWS([userWS]);
};

export const expectUpdateChannelAvatarEvents = async (
  request: V3UpdateChannelAvatarRequest,
  users: UserWS[],
): Promise<void> => {
  const [actor] = users;
  const updateChannelAvtRes = await getResponseSuccess(
    request,
    channelClient.updateChannelAvatar,
    createHeaders(actor.user.token as string),
  );

  await expectUpdateChannelEvents(
    updateChannelAvtRes as V3GetChannelResponse,
    users,
  );
};

export const expectUpdateChannelNameEvents = async (
  request: V3UpdateChannelNameRequest,
  users: UserWS[],
): Promise<void> => {
  const [actor] = users;

  const updateChannelNameRes = await getResponseSuccess(
    request,
    channelClient.updateChannelName,
    createHeaders(actor.user.token as string),
  );

  await expectUpdateChannelEvents(
    updateChannelNameRes as V3GetChannelResponse,
    users,
  );
};

export const expectDeleteChannelAvatar = async (
  request: V3DeleteChannelAvatarRequest,
  users: UserWS[],
): Promise<void> => {
  const [actor] = users;

  const deleteChannelAvtRes = await getResponseSuccess(
    request,
    channelClient.deleteChannelAvatar,
    createHeaders(actor.user.token as string),
  );

  await expectUpdateChannelEvents(
    deleteChannelAvtRes as V3GetChannelResponse,
    users,
  );
};

const expectUpdateChannelEvents = async (
  updateChannelRes: V3GetChannelResponse,
  users: UserWS[],
): Promise<void> => {
  const expectedEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.CHANNEL_UPDATED_EVENT,
    cloudEventType.MESSAGE_CREATED_EVENT,
  ];
  const [actor] = users;
  const actorSrc = getActorSource(actor.events);

  await wait();
  const actorChannel = updateChannelRes?.data?.channel as V3Channel;
  const includes = updateChannelRes?.includes as V3DataInclude;

  if (!includes?.channelMetadata) throw new Error(NULL_UNDEFINED_ERROR);
  const lastMsgId = includes?.channelMetadata[0].lastMessageId;
  const messageRes = await getResponseSuccess(
    {
      channelId: actorChannel.channelId as string,
      workspaceId: actorChannel.workspaceId as string,
      messageId: lastMsgId,
    },
    messageViewClient.getMessage,
    createHeaders(actor.user.token as string),
  );

  for (const { events, user } of users) {
    expect(events).toHaveLength(expectedEvents.length);
    for (const [index, event] of events.entries()) {
      expect(event.version).toEqual(WS_VERSION);
      expect(event.type).toEqual(expectedEvents[index]);

      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(user, event.data);
          break;
        case cloudEventType.CHANNEL_UPDATED_EVENT:
          expectSourceFromUser(actorSrc, event.source);
          expectChannelUpdatedEventData(actorChannel, includes, event.data);
          break;
        case cloudEventType.MESSAGE_CREATED_EVENT:
          expectSourceFromUser(CLOUD_EVENT_SOURCE_SYSTEM, event.source);
          // TODO https://github.com/halonext/ziichat-issues/issues/21826
          // expectMessageCreatedEventData(
          //   messageRes?.data?.message as V3Message,
          //   messageRes?.includes as V3DataInclude,
          //   event.data,
          // );
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }
  }
};

export const expectDeleteChannelEvents = async (
  request: V3DeleteChannelRequest,
  users: UserWS[],
): Promise<void> => {
  const expectedEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.CHANNEL_DELETED_EVENT,
  ];
  const [actor] = users;
  const actorSrc = getActorSource(actor.events);

  await getResponseSuccess(
    request,
    channelClient.deleteChannel,
    createHeaders(actor.user.token as string),
  );
  for (const { user, events } of users) {
    expect(events).toHaveLength(expectedEvents.length);
    for (const [index, event] of events.entries()) {
      expect(event.version).toEqual(WS_VERSION);
      expect(event.type).toEqual(expectedEvents[index]);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(user, event.data);
          break;
        case cloudEventType.CHANNEL_DELETED_EVENT:
          expectSourceFromUser(actorSrc, event.source);
          expectChannelDeletedEventData(request, event.data);
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }
  }
};

export const expectAcceptMessageRequestEvents = async (
  users: UserWS[],
): Promise<void> => {
  let expectedEvents: cloudEventType[];
  const expectActorEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.INCOMING_MESSAGE_REQUEST_ACCEPTED_EVENT,
  ];

  const expectRecipientEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.OUTGOING_MESSAGE_REQUEST_ACCEPTED_EVENT,
  ];

  const [actor, recipient] = users;
  const actorSource = getActorSource(actor.events);

  const acceptRes = await getResponseSuccess(
    { userId: recipient.user.userId as string },
    channelClient.acceptMessageRequest,
    createHeaders(actor.user.token as string),
  );
  await wait();

  const actorChannel = acceptRes?.data?.channel as V3Channel;
  const includes = acceptRes?.includes as V3DataInclude;

  const recipientChannel = await getResponseSuccess(
    { userId: actor.user.userId },
    channelViewClient.getDmChannel,
    createHeaders(recipient.user.token as string),
  );

  for (const { user, events } of users) {
    if (user == actor.user) {
      expectedEvents = expectActorEvents;
    } else {
      expectedEvents = expectRecipientEvents;
    }

    expect(events).toHaveLength(expectedEvents.length);
    for (const [index, event] of events.entries()) {
      expect(event.version).toEqual(WS_VERSION);
      expect(event.type).toEqual(expectedEvents[index]);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(user, event.data);
          break;
        case cloudEventType.INCOMING_MESSAGE_REQUEST_ACCEPTED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectIncomingMessageRequestAcceptedEvent(
            actorChannel,
            includes,
            event.data,
          );
          break;
        case cloudEventType.OUTGOING_MESSAGE_REQUEST_ACCEPTED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectOutgoingMessageRequestAcceptedEvent(
            recipientChannel?.data?.channel as V3Channel,
            includes,
            event.data,
          );
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }
  }
};

export const expectRejectMessageRequestEvents = async (
  users: UserWS[],
  msgSend: V3Message,
): Promise<void> => {
  let expectedEvents: cloudEventType[];
  const expectActorEvents = [
    cloudEventType.GATEWAY_CONNECTED_EVENT,
    cloudEventType.MESSAGE_REQUEST_REJECTED_EVENT,
    cloudEventType.ALL_USER_MESSAGES_DELETED_EVENT,
  ];

  const expectRecipientEvents = [cloudEventType.GATEWAY_CONNECTED_EVENT];

  const [actor, recipient] = users;
  const actorSource = getActorSource(actor.events);

  await getResponseSuccess(
    { userId: recipient.user.userId as string },
    channelClient.rejectMessageRequest,
    createHeaders(actor.user.token as string),
  );
  await wait(3000);

  await getResponseSuccess(
    { userId: actor.user.userId },
    channelViewClient.getDmChannel,
    createHeaders(recipient.user.token as string),
  );

  const destination: V3ChannelDestinationCloudEvent = {
    channelId: msgSend.channelId,
    workspaceId: msgSend.workspaceId,
    channelType: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM,
    dmStatus: V3DirectMessageStatusEnum.DIRECT_MESSAGE_STATUS_ENUM_PENDING,
    recipientId: recipient.user.userId,
    dmId: `${actor.user.userId}_${recipient.user.userId}`,
  };

  for (const { user, events } of users) {
    if (user == actor.user) {
      expectedEvents = expectActorEvents;
    } else {
      expectedEvents = expectRecipientEvents;
    }

    expect(events).toHaveLength(expectedEvents.length);
    for (const [index, event] of events.entries()) {
      expect(event.type).toEqual(expectedEvents[index]);
      expect(event.version).toEqual(WS_VERSION);
      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(user, event.data);
          break;
        case cloudEventType.ALL_USER_MESSAGES_DELETED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectAllUserMessagesDeletedEventData(
            {
              channelId: msgSend.channelId,
              workspaceId: msgSend.workspaceId,
            },
            actor.user,
            destination,
            event.data,
          );
          break;
        case cloudEventType.MESSAGE_REQUEST_REJECTED_EVENT:
          expectSourceFromUser(actorSource, event.source);
          expectMessageRequestRejectedEvent(
            actor.user,
            recipient.user,
            msgSend,
            event.data,
          );
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }
  }
};

export const expectEventsForUserBlockAndUnblockEventData = async (
  users: UserWS[],
  expectedData: V3UserBlockedEventData | V3UserUnblockedEventData,
  expectedUserEvents: cloudEventType[],
): Promise<void> => {
  let source = '';

  for (const { user, events } of users) {
    if (user.userId == expectedData.actorId) {
      source = getActorSource(events);
    }
    expect(events).toHaveLength(expectedUserEvents.length);
    for (const [index, event] of events.entries()) {
      expect(event.type).toEqual(expectedUserEvents[index]);
      expect(event.version).toEqual(WS_VERSION);

      switch (event.type) {
        case cloudEventType.GATEWAY_CONNECTED_EVENT:
          await expectGatewayConnectedEvent(user, event.data);
          break;
        case cloudEventType.USER_BLOCKED_EVENT:
          expectSourceFromUser(source, event.source);
          expectUserBlockedEventData(expectedData, event.data);
          break;
        case cloudEventType.USER_UNBLOCKED_EVENT:
          expectSourceFromUser(source, event.source);
          expectUserUnblockedEventData(expectedData, event.data);
          break;
        default:
          throw new Error('Event have not been handled yet: ' + event.type);
      }
    }
  }

  await expectResumeWS(users);
};
