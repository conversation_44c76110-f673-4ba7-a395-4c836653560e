import { V3InvitationData } from '../../../utils/http-client/commands-chat-client';
import {
  V3AttachmentTypeEnum,
  V3MessageStatusEnum,
  V3MessageTypeEnum,
} from '../../../utils/http-client/commands-message-client';
import { V3MockedUser } from '../../../utils/http-client/faker-client';
import { V3EmbedTypeEnum } from '../../../utils/http-client/views-message-client';
import invitationViewClient from '../../helpers/invitation-view-service';
import { getLastMsgInBot } from '../../helpers/message-view-service';
import {
  assertIsDate,
  createHeaders,
  deletePresenceDataObj,
  getResponseSuccess,
  removeFields,
} from '../../helpers/shared/common';
import {
  CONTENT_LOCALE_EN,
  CONTENT_LOCALE_EN_US,
  PREFIX_INVITATION_LINK,
  SystemMessage,
  WELCOME_MESSAGE,
  ZIICHAT_BOT_USERID,
  ZIICHAT_BOT_USERNAME,
} from '../../tests/const';
import { NULL_UNDEFINED_ERROR } from '../../tests/error-message';

export const expectInviteMessageInBot = async (
  users: V3MockedUser[],
  inviteLink: string,
  userSend: V3MockedUser,
  hasMsg = true,
): Promise<void> => {
  for (const user of users) {
    const inviteMsg = await getLastMsgInBot(user);
    const mentions = [`@${userSend.username}`];

    expect(inviteMsg?.userId).toEqual(ZIICHAT_BOT_USERID);

    expect(inviteMsg?.attachmentCount).toEqual(0);
    expect(inviteMsg?.messageType).toEqual(
      V3MessageTypeEnum.MESSAGE_TYPE_ENUM_DEFAULT,
    );
    expect(inviteMsg?.messageStatus).toEqual(
      V3MessageStatusEnum.MESSAGE_STATUS_ENUM_SUCCESS,
    );

    assertIsDate(inviteMsg?.createTime as string);
    assertIsDate(inviteMsg?.updateTime as string);

    expect(inviteMsg?.createTime).toEqual(inviteMsg?.updateTime);

    if (hasMsg) {
      expect(inviteMsg?.content).toEqual(
        `${SystemMessage.SEND_INVITATION}${inviteLink}`,
      );
      expect(inviteMsg?.mentions).toEqual(mentions);
      expect(inviteMsg?.contentArguments).toEqual(mentions);
      expect(inviteMsg?.attachmentType).toEqual(
        V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_LINKS,
      );
      expect(inviteMsg?.contentLocale).toEqual(CONTENT_LOCALE_EN);

      if (!inviteMsg?.embed) throw new Error(NULL_UNDEFINED_ERROR);
      const embed = inviteMsg.embed[0];

      const { type, url, provider, meta, invitationData } = embed;
      expect(type).toEqual(V3EmbedTypeEnum.EMBED_TYPE_ENUM_INVITATION);
      expect(url).toEqual(inviteLink);
      expect(provider).toEqual(ZIICHAT_BOT_USERNAME);

      const invitationRes = await getResponseSuccess(
        {
          code: inviteLink.slice(PREFIX_INVITATION_LINK.length),
        },
        invitationViewClient.getInvitation,
        createHeaders(user.token as string),
      );

      deletePresenceDataObj(invitationRes.data);

      expect(invitationData).toMatchObject(
        removeFields(invitationRes.data as V3InvitationData, [
          'decoratedAvatar',
          'originalDecoratedAvatar',
        ]),
      );
      expect(meta).toBeDefined();
    } else {
      expect(inviteMsg?.content).toEqual(WELCOME_MESSAGE);
      expect(inviteMsg?.mentions).toBeUndefined();
      expect(inviteMsg?.contentArguments).toEqual([]);
      expect(inviteMsg?.attachmentType).toEqual(
        V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_UNSPECIFIED,
      );
      expect(inviteMsg?.contentLocale).toEqual(CONTENT_LOCALE_EN_US);
      expect(inviteMsg?.embed).toEqual([]);
    }
  }
};
