/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export enum V3UserAvatarTypeEnum {
  USER_AVATAR_TYPE_ENUM_UNSPECIFIED = 0,
  USER_AVATAR_TYPE_ENUM_PHOTO = 1,
  USER_AVATAR_TYPE_ENUM_VIDEO = 2,
}

export enum V3UserBadgeTypeEnum {
  USER_BADGE_TYPE_DEFAULT = 0,
  USER_BADGE_TYPE_BLUE = 1,
  USER_BADGE_TYPE_GRAY = 2,
  USER_BADGE_TYPE_YELLOW = 3,
}

export enum V3UserTypeEnum {
  USER_TYPE_ENUM_DEFAULT = 0,
  USER_TYPE_ENUM_BOT = 1,
  USER_TYPE_ENUM_GHOST = 2,
}

export type V3SearchChannelsRequest = any & {
  /** The keyword used for searching channels */
  keyword?: string | null;
  /** The maximum number of search results to retrieve */
  limit?: string | null;
  /** A token to retrieve the next page of search results */
  nextPageToken?: string | null;
  /** A token to retrieve the previous page of search */
  prevPageToken?: string | null;
};

export type V3SearchUsersRequest = any & {
  /** The keyword used for searching users */
  keyword: string;
  /** The maximum number of search results to retrieve */
  limit?: string | null;
  /** A token to retrieve the next page of search results */
  nextPageToken?: string | null;
  /** A token to retrieve the previous page of search */
  prevPageToken?: string | null;
};

export interface V3UserMetadataRequest {
  /**
   * @format isULID
   * @minLength 1
   */
  'x-user-id': string;
  [key: string]: any;
}

export interface V3Error {
  /** The error code */
  code: number;
  /** The error message */
  message: string;
  details?: string[];
}

export interface V3Pagination {
  /** Is the token to send a request to get the next page's data */
  nextPageToken?: number;
  /** Is the token to send a request to get previous page data */
  prevPageToken?: number;
  /** Returns true if there is information on the next page */
  hasNext: boolean;
  /** Returns true if there is information on the prev page */
  hasPrev: boolean;
}

export interface V3SearchChannelsResponse {
  /** Indicates whether the search operation was successful */
  ok: boolean;
  /** Information about pagination, such as the current page, total pages, etc */
  paging?: V3Pagination;
  /** An array of search result items containing channel information. Only have value when OK is true.  */
  data?: V3SearchChannelResult[];
  /** An error message in case the search operation encountered any issues. Only have value when OK is false.  */
  error?: V3Error;
}

export interface V3SearchChannelResult {
  /** The workspace identify */
  workspaceId: string;
  /** The channel identify */
  channelId: string;
  /** The total member in channel */
  totalMembers: number;
  /** The name of channel */
  name: string;
  /** The avatar of channel */
  avatar: string;
  /** The is private or not */
  isPrivate: boolean;
  /** The type of the channel */
  type: number;
  /** The invitation link */
  invitationLink: string;
  /** The first time channel created */
  createTime: string;
  /** The time channel updated */
  updateTime: string;
}

export interface V3SearchUserResult {
  /** The user identify */
  userId: string;
  /** The user display name */
  displayName: string;
  /** The username of user */
  username: string;
  /** The thumbnail avatar of user */
  avatar: string;
  /** The video avatar URL */
  videoAvatar: string;
  /** The decoratedAvatar of user */
  decoratedAvatar: string;
  /** The avatar type */
  avatarType: V3UserAvatarTypeEnum;
  /** The user badge type */
  userBadgeType: V3UserBadgeTypeEnum;
}

export interface V3SearchUsersResponse {
  /** Indicates whether the search operation was successful */
  ok: boolean;
  /** Information about pagination, such as the current page, total pages, etc */
  paging?: V3Pagination;
  /** An array of search result items containing user information. Only have value when OK is true */
  data?: V3SearchUserResult[];
  /** An error message in case the search operation encountered any issues. Only have value when OK is false */
  error?: V3Error;
}

export type QueryParamsType = Record<string | number, any>;
export type ResponseFormat = keyof Omit<Body, 'body' | 'bodyUsed'>;

export interface FullRequestParams extends Omit<RequestInit, 'body'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseFormat;
  /** request body */
  body?: unknown;
  /** base url */
  baseUrl?: string;
  /** request cancellation token */
  cancelToken?: CancelToken;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

export interface ApiConfig<SecurityDataType = unknown> {
  baseUrl?: string;
  baseApiParams?: Omit<RequestParams, 'baseUrl' | 'cancelToken' | 'signal'>;
  securityWorker?: (securityData: SecurityDataType | null) => Promise<RequestParams | void> | RequestParams | void;
  customFetch?: typeof fetch;
}

export interface HttpResponse<D extends unknown, E extends unknown = unknown> extends Response {
  data: D;
  error: E;
}

type CancelToken = Symbol | string | number;

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public baseUrl: string = '';
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private abortControllers = new Map<CancelToken, AbortController>();
  private customFetch = (...fetchParams: Parameters<typeof fetch>) => fetch(...fetchParams);

  private baseApiParams: RequestParams = {
    credentials: 'same-origin',
    headers: {},
    redirect: 'follow',
    referrerPolicy: 'no-referrer',
  };

  constructor(apiConfig: ApiConfig<SecurityDataType> = {}) {
    Object.assign(this, apiConfig);
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected encodeQueryParam(key: string, value: any) {
    const encodedKey = encodeURIComponent(key);
    return `${encodedKey}=${encodeURIComponent(typeof value === 'number' ? value : `${value}`)}`;
  }

  protected addQueryParam(query: QueryParamsType, key: string) {
    return this.encodeQueryParam(key, query[key]);
  }

  protected addArrayQueryParam(query: QueryParamsType, key: string) {
    const value = query[key];
    return value.map((v: any) => this.encodeQueryParam(key, v)).join('&');
  }

  protected toQueryString(rawQuery?: QueryParamsType): string {
    const query = rawQuery || {};
    const keys = Object.keys(query).filter((key) => 'undefined' !== typeof query[key]);
    return keys
      .map((key) => (Array.isArray(query[key]) ? this.addArrayQueryParam(query, key) : this.addQueryParam(query, key)))
      .join('&');
  }

  protected addQueryParams(rawQuery?: QueryParamsType): string {
    const queryString = this.toQueryString(rawQuery);
    return queryString ? `?${queryString}` : '';
  }

  private contentFormatters: Record<ContentType, (input: any) => any> = {
    [ContentType.Json]: (input: any) =>
      input !== null && (typeof input === 'object' || typeof input === 'string') ? JSON.stringify(input) : input,
    [ContentType.Text]: (input: any) => (input !== null && typeof input !== 'string' ? JSON.stringify(input) : input),
    [ContentType.FormData]: (input: any) =>
      Object.keys(input || {}).reduce((formData, key) => {
        const property = input[key];
        formData.append(
          key,
          property instanceof Blob
            ? property
            : typeof property === 'object' && property !== null
              ? JSON.stringify(property)
              : `${property}`,
        );
        return formData;
      }, new FormData()),
    [ContentType.UrlEncoded]: (input: any) => this.toQueryString(input),
  };

  protected mergeRequestParams(params1: RequestParams, params2?: RequestParams): RequestParams {
    return {
      ...this.baseApiParams,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...(this.baseApiParams.headers || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected createAbortSignal = (cancelToken: CancelToken): AbortSignal | undefined => {
    if (this.abortControllers.has(cancelToken)) {
      const abortController = this.abortControllers.get(cancelToken);
      if (abortController) {
        return abortController.signal;
      }
      return void 0;
    }

    const abortController = new AbortController();
    this.abortControllers.set(cancelToken, abortController);
    return abortController.signal;
  };

  public abortRequest = (cancelToken: CancelToken) => {
    const abortController = this.abortControllers.get(cancelToken);

    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(cancelToken);
    }
  };

  public request = async <T = any, E = any>({
    body,
    secure,
    path,
    type,
    query,
    format,
    baseUrl,
    cancelToken,
    ...params
  }: FullRequestParams): Promise<HttpResponse<T, E>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.baseApiParams.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const queryString = query && this.toQueryString(query);
    const payloadFormatter = this.contentFormatters[type || ContentType.Json];
    const responseFormat = format || requestParams.format;

    return this.customFetch(`${baseUrl || this.baseUrl || ''}${path}${queryString ? `?${queryString}` : ''}`, {
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData ? { 'Content-Type': type } : {}),
      },
      signal: (cancelToken ? this.createAbortSignal(cancelToken) : requestParams.signal) || null,
      body: typeof body === 'undefined' || body === null ? null : payloadFormatter(body),
    }).then(async (response) => {
      const r = response.clone() as HttpResponse<T, E>;
      r.data = null as unknown as T;
      r.error = null as unknown as E;

      const data = !responseFormat
        ? r
        : await response[responseFormat]()
            .then((data) => {
              if (r.ok) {
                r.data = data;
              } else {
                r.error = data;
              }
              return r;
            })
            .catch((e) => {
              r.error = e;
              return r;
            });

      if (cancelToken) {
        this.abortControllers.delete(cancelToken);
      }

      return data;
    });
  };
}

/**
 * @title Search Module
 * @version v3
 *
 * Search module Ajv decorator swagger documents
 */
export class searchModuleHttpClient<SecurityDataType extends unknown> {
  http: HttpClient<SecurityDataType>;

  constructor(http: HttpClient<SecurityDataType>) {
    this.http = http;
  }

  search = {
    /**
     * No description
     * * @name SearchSearchUsers
     * @request POST:/Search/SearchUsers
     */
    searchSearchUsers: (data: V3SearchUsersRequest, params: RequestParams = {}) =>
      this.http.request<V3SearchUsersResponse, any>({
        path: `/Search/SearchUsers`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     * * @name SearchSearchChannels
     * @request POST:/Search/SearchChannels
     */
    searchSearchChannels: (data: V3SearchChannelsRequest, params: RequestParams = {}) =>
      this.http.request<V3SearchChannelsResponse, any>({
        path: `/Search/SearchChannels`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     * * @name SearchSearchEverything
     * @request POST:/Search/SearchEverything
     */
    searchSearchEverything: (data: V3SearchUsersRequest, params: RequestParams = {}) =>
      this.http.request<V3SearchUsersResponse, any>({
        path: `/Search/SearchEverything`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  };
}
