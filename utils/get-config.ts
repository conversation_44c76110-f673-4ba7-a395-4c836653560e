import { readFileSync } from "fs";
import * as yaml from "js-yaml";
import { get, merge, set } from "lodash";
import { resolve } from "path";

import { logger } from "./get-logger";

function loadFromEnv(
  env: Record<string, string | undefined>,
  { delimiter = "__" } = {}
): Record<string, string> {
  return Object.entries(process.env).reduce((acc, [key, value]) => {
    set(acc, key.toLowerCase().replace(delimiter, "."), value);
    return acc;
  }, {});
}

function loadFromYaml(env = "development"): Record<string, unknown> {
  const configFile = `env.${env}.yaml`;
  const configPath = resolve(process.cwd(), configFile);

  logger?.info(`loading configuration from: ${configPath}`, "ConfigService");

  return yaml.load(readFileSync(configPath, "utf8")) as Record<string, unknown>;
}

function loadConfiguration(): Record<string, unknown> {
  const fromYaml = loadFromYaml(process.env.NODE_ENV);
  const fromProcess = loadFromEnv(process.env);

  return merge(fromYaml, fromProcess);
}

let CONFIG_DATA: Record<string, unknown> | undefined;

if (!CONFIG_DATA) {
  CONFIG_DATA = loadConfiguration();
}

export function getConfig<T>(key: string, fallback?: T): T {
  return get(CONFIG_DATA, key, fallback) as T;
}
