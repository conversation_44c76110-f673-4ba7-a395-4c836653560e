{"name": "halome-api-automation-test-hono", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest --config jest-e2e.ts", "gen-client": "npx ts-node gen-client/generate-type.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bufbuild/buf": "^1.28.1", "@faker-js/faker": "^8.0.2", "@nestjs/common": "^10.3.10", "@types/jest": "^29.5.14", "@types/node": "^18.16.10", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "axios": "^1.7.2", "eslint": "^9.25.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "google-proto-files": "^3.0.3", "http-status-codes": "^2.3.0", "jest": "^29.5.0", "jest-html-reporters": "^3.1.7", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "pino": "^8.14.1", "pino-pretty": "^10.0.0", "prettier": "^2.8.8", "rimraf": "^4.4.1", "supertest": "^6.3.4", "swagger-typescript-api": "^13.0.23", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "tsc-watch": "^6.0.4", "turbocommons-ts": "^3.8.0", "typescript": "^5.7.3", "ulid": "^2.3.0", "ws": "^8.14.2"}}