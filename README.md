# halome-api-automation-test-v2

Automation Test for Refactoration to Hono framework

## Requirements

* NodeJS 18+
* PNPM 8+

## Instruction

* run `pnpm install`
* run `pnpm test`: Run tests all test case
* run `pnpm test <pathFile`: Run only 1 test file
* run `pnpm gen-client`: Generate http client

## Link folder Test case

https://drive.google.com/drive/u/0/folders/1qFQxCR_uSRhE5qcGBqIMhXTWHtvYOAjO
