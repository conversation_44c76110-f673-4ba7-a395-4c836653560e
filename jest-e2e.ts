import type { Config } from 'jest';

const config: Config = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testEnvironment: 'node',
  testRegex: '.e2e-spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  maxWorkers: 4,
  verbose: true,
  testTimeout: 60000,
  globals: {
    prefixMockUser: new Date().toISOString().replace(/[^a-zA-Z0-9]/g, ''),
  },
  globalTeardown: './setTeardown.ts',
};

export default config;

export const getPrefixMockUser = (): string => {
  return global.prefixMockUser;
};
