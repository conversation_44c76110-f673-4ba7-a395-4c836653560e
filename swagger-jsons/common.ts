import * as fs from 'fs';
import * as path from 'path';

export const swaggerDir = path.join(__dirname, '../swagger-jsons/hono');

export const getAllFiles = (dirPath: string): string[] => {
  let jsonFiles: string[] = [];

  // Read directory contents
  const filesAndDirs = fs.readdirSync(dirPath);

  for (const item of filesAndDirs) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      jsonFiles = jsonFiles.concat(getAllFiles(fullPath));
    } else if (stat.isFile() && fullPath.endsWith('.json')) {
      jsonFiles.push(fullPath);
    }
  }

  return jsonFiles;
};
