{"openapi": "3.1.0", "info": {"title": "Views chat", "description": "Views chat ajv decorator swagger documents", "version": "v3"}, "components": {"schemas": {"V3GetChannelRequest": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel idenitfy"}}, "required": ["workspaceId", "channelId"], "$id": "V3GetChannelRequest"}, "V3GetDMChannelRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The workspace identify"}}, "required": ["userId"], "$id": "V3GetDMChannelRequest"}, "V3ListAllChannelsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListAllChannelsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListChannelsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListChannelsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListDMChannelsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListDMChannelsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListInComingMessageRequestsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListInComingMessageRequestsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListOutGoingMessageRequestsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListOutGoingMessageRequestsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3GetFriendRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}}, "required": ["userId"], "$id": "V3GetFriendRequest"}, "V3ListFriendsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListFriendsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListInComingFriendRequestsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListInComingFriendRequestsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListOutGoingFriendRequestsRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListOutGoingFriendRequestsRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3GetInvitationRequest": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "minLength": 1, "description": "Link to the channel acceptance invitation"}}, "required": ["code"], "$id": "V3GetInvitationRequest"}, "V3ListInvitationRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, "workspaceId": {"type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListInvitationRequest"}, "V3ListInvitableUsersRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListInvitableUsersRequest", "properties": {}}, "V3GetMemberRequest": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "decsription": "The channel identify"}, "userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}}, "required": ["workspaceId", "channelId", "userId"], "$id": "V3GetMemberRequest"}, "V3ListMembersRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, "workspaceId": {"type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListMembersRequest"}, "V3PaginationRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}, "not": {"required": ["nextPageToken", "prevPageToken"]}, "errorMessage": {"not": "enter either prevPageToken or nextPageToken"}, "$id": "V3PaginationRequest"}, "V3GetAvatarFrameRequest": {"type": "object", "additionalProperties": false, "properties": {"avatarFrameId": {"type": "string", "minLength": 1, "description": "The avatar frame identify"}}, "required": ["avatarFrameId"], "$id": "V3GetAvatarFrameRequest"}, "V3GetDataUserRingbackToneRequest": {"type": "object", "additionalProperties": false, "properties": {"ringbackToneId": {"type": "string", "minLength": 1, "description": "The ringback tone identify"}}, "required": ["ringbackToneId"], "$id": "V3GetDataUserRingbackToneRequest"}, "V3GetMeRequest": {"type": "object", "additionalProperties": false, "$id": "V3GetMeRequest", "properties": {}}, "V3GetPrivateDataRequest": {"type": "object", "additionalProperties": false, "$id": "V3GetPrivateDataRequest", "properties": {}}, "V3GetUserByUserIdRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}}, "required": ["userId"], "$id": "V3GetUserByUserIdRequest"}, "V3GetUserByUsernameRequest": {"type": "object", "additionalProperties": false, "properties": {"username": {"type": "string", "minLength": 1, "description": "The user identify"}}, "required": ["username"], "$id": "V3GetUserByUsernameRequest"}, "V3ListAvatarFrameCollectionRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListAvatarFrameCollectionRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListBannedUsersRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, "workspaceId": {"type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListBannedUsersRequest"}, "V3ListBlockedUsersRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListBlockedUsersRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListRingbackTonesRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListRingbackTonesRequest", "properties": {}}, "V3ListPrivateDataRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListPrivateDataRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListUserStatusRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListUserStatusRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3ListUserVisitedProfileRequest": {"type": "object", "additionalProperties": false, "$id": "V3ListUserVisitedProfileRequest", "properties": {"limit": {"type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}}}, "V3SyncUsersRequest": {"type": "object", "additionalProperties": false, "properties": {"userIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "description": "List userIds need sync data"}, "updateTimeAfter": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "description": "Datetime ISO String"}}, "required": ["userIds", "updateTimeAfter"], "$id": "V3SyncUsersRequest"}, "V3UserMetadataRequest": {"type": "object", "additionalProperties": true, "properties": {"x-user-id": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "x-user identity"}}, "required": ["x-user-id"], "$id": "V3UserMetadataRequest"}, "V3PaginationResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}}, "$id": "V3PaginationResponse"}, "V3GetChannelResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "desctiption": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3ChannelData", "description": "The channel's data"}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}}, "$id": "V3GetChannelResponse"}, "V3GetDMChannelResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3ChannelData", "description": "The channel's data"}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}}, "$id": "V3GetDMChannelResponse"}, "V3ListAllChannelsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelData"}, "description": "List of values for the Channel interface."}}, "$id": "V3ListAllChannelsResponse"}, "V3ListChannelsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelData"}, "description": "List of values for the Channel interface."}}, "$id": "V3ListChannelsResponse"}, "V3ListDMChannelsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelData"}, "description": "List of values for the Channel interface."}}, "$id": "V3ListDMChannelsResponse"}, "V3ListInComingMessageRequestsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelData"}, "description": "List of values for the Channel interface."}}, "$id": "V3ListInComingMessageRequestsResponse"}, "V3ListOutGoingMessageRequestsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelData"}, "description": "List of values for the Channel interface."}}, "$id": "V3ListOutGoingMessageRequestsResponse"}, "V3ErrorResponse": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "number", "description": "The error code"}, "message": {"type": "string", "description": "The error message"}, "details": {"type": "array", "items": {"type": "string"}, "description": "Detail about the error"}}, "$id": "V3ErrorResponse"}, "V3GetFriendResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "The status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3FriendData", "description": "The friend's data"}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for friend"}}, "$id": "V3GetFriendResponse"}, "V3ListFriendsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3FriendData"}, "description": "List of values for the Friend interface."}}, "$id": "V3ListFriendsResponse"}, "V3ListInComingFriendRequestsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3FriendData"}, "description": "List of values for the Friend interface."}}, "$id": "V3ListInComingFriendRequestsResponse"}, "V3ListOutGoingFriendRequestsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3FriendData"}, "description": "List of values for the Friend interface."}}, "$id": "V3ListOutGoingFriendRequestsResponse"}, "V3GetInvitationResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3InvitationData", "description": "The invitation's data"}}, "$id": "V3GetInvitationResponse"}, "V3ListInvitationResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3Invitation"}, "description": "List of values for the Invitation interface."}}, "$id": "V3ListInvitationResponse"}, "V3ListInvitableUsersResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}, "description": "The list user's data"}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}}, "$id": "V3ListInvitableUsersResponse"}, "V3GetMemberResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3MemberData", "description": "List of values for the MemberView interface."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for member"}}, "$id": "V3GetMemberResponse"}, "V3ListMembersResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination information"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for channel"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MemberData"}, "description": "List of values for the MemberView interface."}}, "$id": "V3ListMembersResponse"}, "V3Paging": {"type": "object", "additionalProperties": false, "properties": {"hasNext": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "hasPrev": {"type": "boolean", "description": "Returns true if there is information on the prev page."}, "nextPageToken": {"type": "string", "description": "is the token to send a request to get the next page's data."}, "prevPageToken": {"type": "string", "description": "is the token to send a request to get previous page data."}}, "$id": "V3Paging"}, "V3GetAvatarFrameResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3AvatarFrameData", "description": "Avatar frame data"}}, "$id": "V3GetAvatarFrameResponse"}, "V3GetDataUserRingbackToneResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "The status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3RingbackToneData", "description": "The ringback tone response data"}}, "$id": "V3GetDataUserRingbackToneResponse"}, "V3GetMeResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3MeResponse", "description": "The user's data"}}, "$id": "V3GetMeResponse"}, "V3GetPrivateDataResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3PrivateData", "description": "The private data"}}, "$id": "V3GetPrivateDataResponse"}, "V3GetUserByUserIdResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3UserView", "description": "The user's data"}}, "$id": "V3GetUserByUserIdResponse"}, "V3GetUserByUsernameResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3UserView", "description": "The user's data"}}, "$id": "V3GetUserByUsernameResponse"}, "V3ListAvatarFrameCollectionResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3AvatarFrameCollectionData"}, "description": "List avatar group by collection"}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Data pagination"}}, "$id": "V3ListAvatarFrameCollectionResponse"}, "V3ListBannedUsersResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "An error message in case operation encountered any issues. \n Only have value when OK is false."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MemberData"}, "description": "List of values for the MemberView interface."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Information about pagination, such as the current page, total pages, etc."}, "includes": {"$ref": "#/components/schemas/V3DataInclude", "description": "Information data and populate data for member"}}, "$id": "V3ListBannedUsersResponse"}, "V3ListBlockedUsersResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Indicates whether the search operation was successful."}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "An error message in case the search operation encountered any issues. \nOnly have value when OK is false."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}, "description": "An array of search result items containing user information.\nOnly have value when OK is true."}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Information about pagination, such as the current page, total pages, etc."}}, "$id": "V3ListBlockedUsersResponse"}, "V3ListRingbackTonesResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "The status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3RingbackToneData"}, "description": "The ringback tone response data"}}, "$id": "V3ListRingbackTonesResponse"}, "V3ListPrivateDataResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"$ref": "#/components/schemas/V3PrivateData", "description": "The private data"}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Information about pagination, such as the current page, total pages, etc."}}, "$id": "V3ListPrivateDataResponse"}, "V3ListUserStatusResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}, "description": "The user data"}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Information about pagination, such as the current page, total pages, etc."}}, "$id": "V3ListUserStatusResponse"}, "V3ListUserVisitedProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3VisitedProfileData"}, "description": "The visited profile data"}, "paging": {"$ref": "#/components/schemas/V3Paging", "description": "Information about pagination, such as the current page, total pages, etc."}}, "$id": "V3ListUserVisitedProfileResponse"}, "V3UserIdentification": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "description": "The user identify"}, "username": {"type": "string", "description": "The user name"}, "type": {"$ref": "#/components/schemas/V3UserDeletedTypeEnum", "description": "User delete type"}}, "$id": "V3UserIdentification"}, "V3SyncUsersResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/V3UserView"}, "description": "List of values for the user view interface have update after sync time input."}, "userDeleted": {"type": "array", "items": {"$ref": "#/components/schemas/V3UserIdentification"}, "description": "List of userIds deleted."}, "syncTime": {"type": "string", "description": "time sync data"}}, "$id": "V3SyncUsersResponse"}, "V3AudioMetadata": {"type": "object", "additionalProperties": false, "properties": {"samples": {"type": "array", "items": {"type": "number"}}}, "$id": "V3AudioMetadata"}, "V3ChannelMetadata": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "dmId": {"type": "string"}, "unreadCount": {"type": "number"}, "lastMessageId": {"type": "string"}, "notificationStatus": {"type": "boolean"}, "mediaPermissionSetting": {"$ref": "#/components/schemas/V3MediaPermissionSettingEnum"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelPermissionsEnum"}}}, "$id": "V3ChannelMetadata"}, "V3Channel": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "isPrivate": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/V3ChannelTypeEnum"}, "invitationLink": {"type": "string"}, "privacySettings": {"$ref": "#/components/schemas/V3PrivacySettings"}, "premiumSettings": {"$ref": "#/components/schemas/V3PremiumSettings"}, "originalAvatar": {"type": "string"}, "totalMembers": {"type": "number"}, "dmStatus": {"$ref": "#/components/schemas/V3DirectMessageStatusEnum"}, "pinnedMessage": {"$ref": "#/components/schemas/V3Message"}, "participantIds": {"type": "array", "items": {"type": "string"}}, "rejectTime": {"type": "string"}, "acceptTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Channel"}, "V3ChannelPermissionsEnum": {"type": "number", "additionalProperties": false, "$id": "V3ChannelPermissionsEnum", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "x-enum-varnames": ["OWNER", "CHANNELS__VIEW_CHANNEL", "CHANNELS__MANAGE", "CHANNELS__MEMBERS_MANAGE", "CHANNELS__STICKERS_MANAGE", "CHANNELS__INVITATIONS_MANAGE", "CHANNELS__INVITATIONS_CREATE", "MESSAGES__MANAGE", "MESSAGES__VIEW", "MESSAGES__SEND_MESSAGE", "MESSAGES__SEND_ATTACHMENTS", "MESSAGES__EMBED_LINKS", "MESSAGES__MENTION_EVERYONE", "CHANNELS__VIEW_AUDIT_LOGS"]}, "V3DataInclude": {"type": "object", "additionalProperties": false, "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/V3Message"}}, "channels": {"type": "array", "items": {"$ref": "#/components/schemas/V3Channel"}}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/V3Member"}}, "channelMetadata": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelMetadata"}}}, "$id": "V3DataInclude"}, "V3Dimensions": {"type": "object", "additionalProperties": false, "properties": {"height": {"type": "number"}, "width": {"type": "number"}}, "$id": "V3Dimensions"}, "V3Embed": {"type": "object", "additionalProperties": false, "properties": {"meta": {"type": "string"}, "provider": {"type": "string"}, "url": {"type": "string"}, "type": {"$ref": "#/components/schemas/V3EmbedTypeEnum"}, "embedData": {"$ref": "#/components/schemas/V3EmbedData"}, "invitationData": {"$ref": "#/components/schemas/V3InvitationData"}, "locationData": {"$ref": "#/components/schemas/V3LocationData"}}, "$id": "V3Embed"}, "V3EmbedData": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string"}, "version": {"type": "string"}, "title": {"type": "string"}, "authorName": {"type": "string"}, "authorUrl": {"type": "string"}, "providerName": {"type": "string"}, "providerUrl": {"type": "string"}, "cacheAge": {"type": "string"}, "html": {"type": "string"}, "width": {"type": "number"}, "height": {"type": "number"}, "description": {"type": "string"}, "thumbnailUrl": {"type": "string"}, "thumbnailWidth": {"type": "string"}, "thumbnailHeight": {"type": "string"}}, "$id": "V3EmbedData"}, "V3FileMetadata": {"type": "object", "additionalProperties": false, "properties": {"filename": {"type": "string"}, "filesize": {"type": "number"}, "extension": {"type": "string"}, "mimetype": {"type": "string"}, "dimensions": {"$ref": "#/components/schemas/V3Dimensions"}, "duration": {"type": "number"}}, "$id": "V3FileMetadata"}, "V3InvitationData": {"type": "object", "additionalProperties": false, "properties": {"channel": {"$ref": "#/components/schemas/V3InvitationDataChannelData"}, "code": {"type": "string"}, "isExpired": {"type": "boolean"}, "expireTime": {"type": "string"}, "isJoined": {"type": "boolean"}, "invitationLink": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3InvitationData"}, "V3InvitationDataChannelData": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "totalMembers": {"type": "number"}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}}}, "$id": "V3InvitationDataChannelData"}, "V3LinkObject": {"type": "object", "additionalProperties": false, "properties": {"attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "url": {"type": "string"}, "shortUrl": {"type": "string"}}, "$id": "V3LinkObject"}, "V3LocationData": {"type": "object", "additionalProperties": false, "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}, "description": {"type": "string"}, "thumbnailUrl": {"type": "string"}}, "$id": "V3LocationData"}, "V3MediaAttachment": {"type": "object", "additionalProperties": false, "properties": {"link": {"$ref": "#/components/schemas/V3LinkObject"}, "sticker": {"$ref": "#/components/schemas/V3StickerObject"}, "photo": {"$ref": "#/components/schemas/V3MediaObject"}, "audio": {"$ref": "#/components/schemas/V3MediaObject"}, "video": {"$ref": "#/components/schemas/V3MediaObject"}, "voiceMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "videoMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "mediaMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "file": {"$ref": "#/components/schemas/V3MediaObject"}}, "$id": "V3MediaAttachment"}, "V3MediaObject": {"type": "object", "additionalProperties": false, "properties": {"fileId": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "fileUrl": {"type": "string"}, "fileMetadata": {"$ref": "#/components/schemas/V3FileMetadata"}, "thumbnailUrl": {"type": "string"}, "audioMetadata": {"$ref": "#/components/schemas/V3AudioMetadata"}, "fileRef": {"type": "string"}, "attachmentId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "messageId": {"type": "string"}}, "$id": "V3MediaObject"}, "V3Member": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "nickname": {"type": "string"}, "role": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/V3MemberRole"}}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Member"}, "V3MemberRole": {"type": "object", "additionalProperties": false, "properties": {"role": {"type": "string"}, "weight": {"type": "number"}}, "$id": "V3MemberRole"}, "V3Message": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "messageId": {"type": "string"}, "userId": {"type": "string"}, "content": {"type": "string"}, "ref": {"type": "string"}, "messageType": {"$ref": "#/components/schemas/V3MessageTypeEnum"}, "messageStatus": {"$ref": "#/components/schemas/V3MessageStatusEnum"}, "originalMessage": {"$ref": "#/components/schemas/V3OriginalMessage"}, "reactions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/V3ReactionData"}}, "mentions": {"type": "array", "items": {"type": "string"}}, "embed": {"type": "array", "items": {"$ref": "#/components/schemas/V3Embed"}}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "reports": {"type": "array", "items": {"$ref": "#/components/schemas/V3Report"}}, "isThread": {"type": "boolean"}, "reportCount": {"type": "number"}, "isReported": {"type": "boolean"}, "attachmentCount": {"type": "number"}, "mediaAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/V3MediaAttachment"}}, "contentLocale": {"type": "string"}, "contentArguments": {"type": "array", "items": {"type": "string"}}, "isPinned": {"type": "boolean"}, "pinTime": {"type": "string"}, "editTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Message"}, "V3OriginalMessage": {"type": "object", "additionalProperties": false, "properties": {"messageId": {"type": "string"}, "content": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "mediaAttachments": {"$ref": "#/components/schemas/V3MediaAttachment"}, "messageType": {"$ref": "#/components/schemas/V3MessageTypeEnum"}, "contentLocale": {"type": "string"}, "contentArguments": {"type": "array", "items": {"type": "string"}}, "userId": {"type": "string"}, "editTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3OriginalMessage"}, "V3PremiumSettings": {"type": "object", "additionalProperties": false, "properties": {"boosted": {"$ref": "#/components/schemas/V3PremiumSettingsBoosted"}}, "$id": "V3PremiumSettings"}, "V3PremiumSettingsBoosted": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "boolean"}}, "$id": "V3PremiumSettingsBoosted"}, "V3PresenceData": {"type": "object", "additionalProperties": false, "properties": {"lastUpdateTime": {"type": "string"}, "lastUpdateInSeconds": {"type": "number"}, "presenceState": {"$ref": "#/components/schemas/V3PresenceStateEnum"}, "customStatus": {"type": "string"}}, "$id": "V3PresenceData"}, "V3PrivacySettings": {"type": "object", "additionalProperties": false, "properties": {"restrictSavingContent": {"$ref": "#/components/schemas/V3RestrictSavingContent"}}, "$id": "V3PrivacySettings"}, "V3Profile": {"type": "object", "additionalProperties": false, "properties": {"avatar": {"type": "string"}, "displayName": {"type": "string"}, "cover": {"type": "string"}, "originalAvatar": {"type": "string"}, "avatarType": {"$ref": "#/components/schemas/V3UserAvatarTypeEnum"}, "videoAvatar": {"type": "string"}, "userBadgeType": {"$ref": "#/components/schemas/V3UserBadgeTypeEnum"}, "decoratedAvatar": {"type": "string"}, "originalDecoratedAvatar": {"type": "string"}}, "$id": "V3Profile"}, "V3ReactionData": {"type": "object", "additionalProperties": false, "properties": {"isReacted": {"type": "boolean"}, "total": {"type": "number"}}, "$id": "V3ReactionData"}, "V3Report": {"type": "object", "additionalProperties": false, "properties": {"reportCategory": {"$ref": "#/components/schemas/V3ReportCategory"}, "pretendingTo": {"$ref": "#/components/schemas/V3PretendingTo"}, "reportReason": {"type": "string"}, "reportBy": {"type": "string"}, "reportTime": {"type": "string"}}, "$id": "V3Report"}, "V3RestrictSavingContent": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "boolean"}}, "$id": "V3RestrictSavingContent"}, "V3StickerObject": {"type": "object", "additionalProperties": false, "properties": {"collectionId": {"type": "string"}, "stickerId": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "stickerUrl": {"type": "string"}, "attachmentId": {"type": "string"}, "fileRef": {"type": "string"}}, "$id": "V3StickerObject"}, "V3User": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "profile": {"$ref": "#/components/schemas/V3Profile"}, "userType": {"$ref": "#/components/schemas/V3UserTypeEnum"}, "presenceData": {"$ref": "#/components/schemas/V3PresenceData"}, "statusData": {"$ref": "#/components/schemas/V3UserStatus"}}, "$id": "V3User"}, "V3UserStatus": {"type": "object", "additionalProperties": false, "properties": {"content": {"type": "string"}, "status": {"type": "string"}, "expireAfterTime": {"$ref": "#/components/schemas/V3UserStatusExpireAfterTimeEnum"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "endTime": {"type": "string"}}, "$id": "V3UserStatus"}, "V3ChannelData": {"type": "object", "additionalProperties": false, "properties": {"channel": {"$ref": "#/components/schemas/V3Channel"}}, "$id": "V3ChannelData"}, "V3FriendData": {"type": "object", "additionalProperties": false, "properties": {"friend": {"$ref": "#/components/schemas/V3Friend"}}, "$id": "V3FriendData"}, "V3Friend": {"type": "object", "additionalProperties": false, "properties": {"requestedFromUserId": {"type": "string"}, "requestedToUserId": {"type": "string"}, "status": {"$ref": "#/components/schemas/V3FriendStatusEnum"}, "friendId": {"type": "string"}, "participantIds": {"type": "array", "items": {"type": "string"}}, "readTime": {"type": "string"}, "acceptTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "deleteTime": {"type": "string"}}, "$id": "V3Friend"}, "V3MemberData": {"type": "object", "additionalProperties": false, "properties": {"member": {"$ref": "#/components/schemas/V3Member"}}, "$id": "V3MemberData"}, "V3MessageData": {"type": "object", "additionalProperties": false, "properties": {"message": {"$ref": "#/components/schemas/V3Message"}}, "$id": "V3MessageData"}, "V3UserView": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "friendData": {"$ref": "#/components/schemas/V3Friend"}, "mediaPermissionSetting": {"$ref": "#/components/schemas/V3MediaPermissionSettingEnum"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "profile": {"$ref": "#/components/schemas/V3Profile"}, "userType": {"$ref": "#/components/schemas/V3UserTypeEnum"}, "presenceData": {"$ref": "#/components/schemas/V3PresenceData"}, "statusData": {"$ref": "#/components/schemas/V3UserStatus"}, "blocked": {"type": "string"}}, "$id": "V3UserView"}, "V3ChannelTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3ChannelTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["CHANNEL_TYPE_ENUM_DM", "CHANNEL_TYPE_ENUM_CHANNEL", "CHANNEL_TYPE_ENUM_BROADCAST"]}, "V3DirectMessageStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3DirectMessageStatusEnum", "enum": [0, 1], "x-enum-varnames": ["DIRECT_MESSAGE_STATUS_ENUM_PENDING", "DIRECT_MESSAGE_STATUS_ENUM_CONTACTED"]}, "V3MediaPermissionSettingEnum": {"type": "number", "additionalProperties": false, "$id": "V3MediaPermissionSettingEnum", "enum": [0, 1, 2], "x-enum-varnames": ["MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK", "MEDIA_PERMISSION_SETTING_ENUM_ALLOW", "MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW"]}, "V3FriendStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3FriendStatusEnum", "enum": [0, 1, 2, 3, 4, 5], "x-enum-varnames": ["FRIEND_STATUS_ENUM_UNSPECIFIED", "FRIEND_STATUS_ENUM_NOT_FRIEND", "FRIEND_STATUS_ENUM_REQUEST_SENT", "FRIEND_STATUS_ENUM_REQUEST_RECEIVED", "FRIEND_STATUS_ENUM_REQUEST_DELETED", "FRIEND_STATUS_ENUM_FRIEND"]}, "V3EmbedTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3EmbedTypeEnum", "enum": [0, 1, 2, 3, 4, 5, 6], "x-enum-varnames": ["EMBED_TYPE_ENUM_UNSPECIFIED", "EMBED_TYPE_ENUM_PHOTO", "EMBED_TYPE_ENUM_VIDEO", "EMBED_TYPE_ENUM_LINK", "EMBED_TYPE_ENUM_INVITATION", "EMBED_TYPE_ENUM_OTHER", "EMBED_TYPE_ENUM_LOCATION"]}, "V3AttachmentTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3AttachmentTypeEnum", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "x-enum-varnames": ["ATTACHMENT_TYPE_ENUM_UNSPECIFIED", "ATTACHMENT_TYPE_ENUM_PHOTO", "ATTACHMENT_TYPE_ENUM_VOICE_MESSAGE", "ATTACHMENT_TYPE_ENUM_VIDEO_MESSAGE", "ATTACHMENT_TYPE_ENUM_AUDIO", "ATTACHMENT_TYPE_ENUM_VIDEO", "ATTACHMENT_TYPE_ENUM_LINKS", "ATTACHMENT_TYPE_ENUM_STICKER", "ATTACHMENT_TYPE_ENUM_MEDIA", "ATTACHMENT_TYPE_ENUM_MENTION", "ATTACHMENT_TYPE_ENUM_LOCATION", "ATTACHMENT_TYPE_ENUM_FILE"]}, "V3MessageStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3MessageStatusEnum", "enum": [0, 1, 2], "x-enum-varnames": ["MESSAGE_STATUS_ENUM_PENDING", "MESSAGE_STATUS_ENUM_SUCCESS", "MESSAGE_STATUS_ENUM_FAILURE"]}, "V3MessageTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3MessageTypeEnum", "enum": [0, 1], "x-enum-varnames": ["MESSAGE_TYPE_ENUM_DEFAULT", "MESSAGE_TYPE_ENUM_AUDIT_LOG"]}, "V3PresenceStateEnum": {"type": "number", "additionalProperties": false, "$id": "V3PresenceStateEnum", "enum": [0, 1, 2, 3, 4, 5], "x-enum-varnames": ["PRESENCE_STATUS_UNSPECIFIED", "PRESENCE_STATUS_ONLINE", "PRESENCE_STATUS_IDLE", "PRESENCE_STATUS_DO_NOT_DISTURB", "PRESENCE_STATUS_OFFLINE", "PRESENCE_STATUS_OTHER"]}, "V3UserAvatarTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserAvatarTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["USER_AVATAR_TYPE_ENUM_UNSPECIFIED", "USER_AVATAR_TYPE_ENUM_PHOTO", "USER_AVATAR_TYPE_ENUM_VIDEO"]}, "V3UserBadgeTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserBadgeTypeEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["USER_BADGE_TYPE_DEFAULT", "USER_BADGE_TYPE_BLUE", "USER_BADGE_TYPE_GRAY", "USER_BADGE_TYPE_YELLOW"]}, "V3UserStatusExpireAfterTimeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserStatusExpireAfterTimeEnum", "enum": [0, 1, 2, 3, 4, 99], "x-enum-varnames": ["USER_STATUS_EXPIRES_AFTER_TIME_ENUM_UNSPECIFIED", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_1_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_4_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_8_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_24_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER"]}, "V3UserTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["USER_TYPE_ENUM_DEFAULT", "USER_TYPE_ENUM_BOT", "USER_TYPE_ENUM_GHOST"]}, "V3AvatarFrameCollectionData": {"type": "object", "additionalProperties": false, "properties": {"collection": {"type": "string"}, "avatarFrames": {"type": "array", "items": {"$ref": "#/components/schemas/V3AvatarFrameData"}}}, "$id": "V3AvatarFrameCollectionData"}, "V3AvatarFrameData": {"type": "object", "additionalProperties": false, "properties": {"avatarFrameId": {"type": "string"}, "avatarFramePath": {"type": "string"}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "collection": {"type": "string"}}, "$id": "V3AvatarFrameData"}, "V3ChannelDeletedTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3ChannelDeletedTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["CHANNEL_DELETED", "MESSAGES_DELETED", "CHANNEL_BLOCKED"]}, "V3AttachmentFileStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3AttachmentFileStatusEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED", "ATTACHMENT_FILE_STATUS_ENUM_UPLOADING", "ATTACHMENT_FILE_STATUS_ENUM_SUCCESS", "ATTACHMENT_FILE_STATUS_ENUM_FAILURE"]}, "V3StorageClassObjectEmbed": {"type": "object", "additionalProperties": false, "properties": {"bucket": {"type": "string"}, "etag": {"type": "string"}, "key": {"type": "string"}, "location": {"type": "string"}}, "$id": "V3StorageClassObjectEmbed"}, "V3Matrix": {"type": "object", "additionalProperties": false, "properties": {"row": {"type": "number"}, "column": {"type": "number"}}, "$id": "V3Matrix"}, "V3OrientationEnum": {"type": "number", "additionalProperties": false, "$id": "V3OrientationEnum", "enum": [0, 1, 2], "x-enum-varnames": ["ORIENTATION_DEFAULT", "ORIENTATION_PORTRAIT", "ORIENTATION_LANDSCAPE"]}, "V3LayoutMetadata": {"type": "object", "additionalProperties": false, "properties": {"layoutId": {"type": "string"}, "matrix": {"$ref": "#/components/schemas/V3Matrix", "nullable": true}, "dimensions": {"$ref": "#/components/schemas/V3Dimensions", "nullable": true}, "orientation": {"$ref": "#/components/schemas/V3OrientationEnum"}, "isRowSpan": {"type": "boolean"}, "fileRef": {"type": "string"}}, "$id": "V3LayoutMetadata"}, "V3InvitationStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3InvitationStatusEnum", "enum": [0, 1, 2], "x-enum-varnames": ["INVITATION_STATUS_ENUM_ACTIVE", "INVITATION_STATUS_ENUM_EXPIRED", "INVITATION_STATUS_ENUM_REVOKED"]}, "V3PlatformEnum": {"type": "number", "additionalProperties": false, "$id": "V3PlatformEnum", "enum": [0, 1, 2, 3, 4], "x-enum-varnames": ["PLATFORM_UNSPECIFIED", "PLATFORM_WEB", "PLATFORM_ANDROID", "PLATFORM_IOS", "PLATFORM_DESKTOP"]}, "V3Device": {"type": "object", "additionalProperties": false, "properties": {"deviceId": {"type": "string"}, "appId": {"type": "string"}, "token": {"type": "string"}, "voipToken": {"type": "string"}, "platform": {"$ref": "#/components/schemas/V3PlatformEnum"}, "geocode": {"type": "string"}, "updateTime": {"oneOf": [{"type": "number"}, {"type": "string"}]}}, "$id": "V3Device"}, "V3SessionExpirationSettingEnum": {"type": "number", "additionalProperties": false, "$id": "V3SessionExpirationSettingEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["SESSION_EXPIRATION_SETTING_ENUM_180_DAYS", "SESSION_EXPIRATION_SETTING_ENUM_7_DAYS", "SESSION_EXPIRATION_SETTING_ENUM_30_DAYS", "SESSION_EXPIRATION_SETTING_ENUM_90_DAYS"]}, "V3UserDeletedTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserDeletedTypeEnum", "enum": [0, 1], "x-enum-varnames": ["USER_DELETED", "USER_BLOCKED"]}, "V3VisitedProfileData": {"type": "object", "additionalProperties": false, "properties": {"user": {"$ref": "#/components/schemas/V3UserView", "nullable": true}, "visitedTime": {"type": "string"}, "lastVisitedTime": {"type": "string"}}, "$id": "V3VisitedProfileData"}, "V3CallEndedReasonEnum": {"type": "number", "additionalProperties": false, "$id": "V3CallEndedReasonEnum", "enum": [0, 1, 2, 3, 4, 5], "x-enum-varnames": ["CALL_ENDED_REASON_UNSPECIFIED", "CALL_ENDED_REASON_FAILED", "CALL_ENDED_REASON_REMOTE_ENDED", "CALL_ENDED_REASON_UNANSWERED", "CALL_ENDED_REASON_ANSWERED_ELSEWHERE", "CALL_ENDED_REASON_DECLINED_ELSEWHERE"]}, "V3CallStateEnum": {"type": "number", "additionalProperties": false, "$id": "V3CallStateEnum", "enum": [0, 1, 2, 3, 4, 5, 6, 10], "x-enum-varnames": ["CALL_STATE_UNSPECIFIED", "CALL_STATE_DIALING", "CALL_STATE_CALLING", "CALL_STATE_READY_TO_CONNECT", "CALL_STATE_CONNECTING", "CALL_STATE_CONNECTED", "CALL_STATE_RECONNECTING", "CALL_STATE_ENDED"]}, "V3ChannelSyncData": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "version": {"type": "number"}, "source": {"type": "string"}, "unreadCount": {"type": "number"}, "lastSeenMessageId": {"type": "string"}, "pinned": {"type": "boolean"}, "sort": {"type": "number"}}, "$id": "V3ChannelSyncData"}, "V3UserSyncData": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "version": {"type": "number"}, "source": {"type": "string"}, "dmId": {"type": "string"}, "blocked": {"type": "boolean"}, "aliasName": {"type": "string"}}, "$id": "V3UserSyncData"}, "V3CallLogSyncData": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "version": {"type": "number"}, "source": {"type": "string"}, "callerId": {"type": "string"}, "calleeId": {"type": "string"}, "callState": {"$ref": "#/components/schemas/V3CallStateEnum"}, "endedReason": {"$ref": "#/components/schemas/V3CallEndedReasonEnum"}, "callTimeInSeconds": {"type": "number"}, "isOutgoing": {"type": "boolean"}, "readTime": {"type": "string"}, "endedTime": {"type": "string"}, "createTime": {"type": "string"}}, "$id": "V3CallLogSyncData"}, "V3PrivateData": {"type": "object", "additionalProperties": false, "properties": {"channels": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelSyncData"}}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/V3UserSyncData"}}, "callLogs": {"type": "array", "items": {"$ref": "#/components/schemas/V3CallLogSyncData"}}, "updateTime": {"type": "string"}, "createTime": {"type": "string"}}, "$id": "V3PrivateData"}, "V3RingbackToneData": {"type": "object", "additionalProperties": false, "properties": {"ringbackToneId": {"type": "string"}, "ringbackTonePath": {"type": "string"}, "name": {"type": "string"}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3RingbackToneData"}, "V3MeResponse": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "userConnectLink": {"type": "string"}, "globalNotificationStatus": {"type": "string"}, "profile": {"$ref": "#/components/schemas/V3Profile"}, "setting": {"$ref": "#/components/schemas/V3UserSetting"}, "statusData": {"$ref": "#/components/schemas/V3UserStatus"}}, "$id": "V3MeResponse"}, "V3UserScopeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserScopeEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["UNSPECIFIED", "EVERYBODY", "ONLY_FRIENDS", "NO_BODY"]}, "V3variableSecurity": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "boolean"}}, "$id": "V3variableSecurity"}, "V3mediaPermissionPrivacy": {"type": "object", "additionalProperties": false, "properties": {"value": {"$ref": "#/components/schemas/V3MediaPermissionSettingEnum"}}, "$id": "V3mediaPermissionPrivacy"}, "V3UserSecurity": {"type": "object", "additionalProperties": false, "properties": {"smartOtp": {"$ref": "#/components/schemas/V3variableSecurity"}, "recoveryCode": {"$ref": "#/components/schemas/V3variableSecurity"}, "sessionExpiration": {"$ref": "#/components/schemas/V3variableSecurity"}, "securityKey": {"$ref": "#/components/schemas/V3variableSecurity"}}, "$id": "V3UserSecurity"}, "V3UserSetting": {"type": "object", "additionalProperties": false, "properties": {"security": {"$ref": "#/components/schemas/V3UserSecurity"}, "privacy": {"$ref": "#/components/schemas/V3mediaPermissionPrivacy"}, "callScope": {"$ref": "#/components/schemas/V3UserScopeEnum"}, "messageScope": {"$ref": "#/components/schemas/V3UserScopeEnum"}}, "$id": "V3UserSetting"}, "V3PretendingTo": {"type": "number", "additionalProperties": false, "$id": "V3PretendingTo", "enum": [0, 1, 2, 3], "x-enum-varnames": ["PRETENDING_TO_UNSPECIFIED", "PRETENDING_TO_ME", "PRETENDING_TO_FRIEND", "PRETENDING_TO_CELEBRITY"]}, "V3ReportCategory": {"type": "number", "additionalProperties": false, "$id": "V3ReportCategory", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 20], "x-enum-varnames": ["REPORT_CATEGORY_UNSPECIFIED", "REPORT_CATEGORY_HARASSMENT", "REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY", "REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE", "REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT", "REPORT_CATEGORY_HATE_SPEECH", "REPORT_CATEGORY_UNAUTHORIZED_SALES", "REPORT_CATEGORY_SCAMS", "REPORT_CATEGORY_SPAM", "REPORT_CATEGORY_COPYRIGHT", "REPORT_CATEGORY_OTHER"]}, "V3Invitation": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "code": {"type": "string"}, "expiresIn": {"type": "number"}, "maxUses": {"type": "number"}, "status": {"$ref": "#/components/schemas/V3InvitationStatusEnum"}, "expireTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Invitation"}, "V3calculatorPresenceTime": {"type": "object", "additionalProperties": false, "$id": "V3calculatorPresenceTime", "properties": {}}, "V3extractActivatedPermissions": {"type": "object", "additionalProperties": false, "$id": "V3extractActivatedPermissions", "properties": {}}, "V3getChannelNotificationStatus": {"type": "object", "additionalProperties": false, "$id": "V3getChannelNotificationStatus", "properties": {}}, "V3getDataIncludeChannel": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeChannel", "properties": {}}, "V3getDataIncludeMember": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeMember", "properties": {}}, "V3getDataIncludeMessage": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeMessage", "properties": {}}, "V3getDataIncludeUser": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeUser", "properties": {}}, "V3getInitDataInclude": {"type": "object", "additionalProperties": false, "$id": "V3getInitDataInclude", "properties": {}}, "V3getUserPermissions": {"type": "object", "additionalProperties": false, "$id": "V3getUserPermissions", "properties": {}}, "V3mappingChannelData": {"type": "object", "additionalProperties": false, "$id": "V3mappingChannelData", "properties": {}}, "V3mappingFriendData": {"type": "object", "additionalProperties": false, "$id": "V3mappingFriendData", "properties": {}}, "V3mappingMemberData": {"type": "object", "additionalProperties": false, "$id": "V3mappingMemberData", "properties": {}}, "V3mappingUserEntityToProto": {"type": "object", "additionalProperties": false, "$id": "V3mappingUserEntityToProto", "properties": {}}, "V3wrapMessageEntityToResponse": {"type": "object", "additionalProperties": false, "$id": "V3wrapMessageEntityToResponse", "properties": {}}}}, "paths": {"/MemberView/GetMember": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetMemberResponse"}}}}}, "operationId": "GetMember", "parameters": [{"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "decsription": "The channel identify"}, {"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId", "userId"]}}, "/MemberView/ListMembers": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListMembersResponse"}}}}}, "operationId": "ListMembers", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/MemberView/ListBannedUsers": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListBannedUsersResponse"}}}}}, "operationId": "ListBannedUsers", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/InvitationView/GetInvitation": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetInvitationResponse"}}}}}, "operationId": "GetInvitation", "parameters": [{"in": "query", "name": "code", "type": "string", "minLength": 1, "description": "Link to the channel acceptance invitation"}], "type": "object", "additionalProperties": false, "required": ["code"]}}, "/InvitationView/ListInvitation": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListInvitationResponse"}}}}}, "operationId": "ListInvitation", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "enum": ["0"], "errorMessage": {"enum": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/InvitationView/ListInvitableUsers": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListInvitableUsersResponse"}}}}}, "operationId": "ListInvitableUsers", "parameters": [], "type": "object", "additionalProperties": false}}, "/UserView/GetUser": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetUserByUserIdResponse"}}}}}, "operationId": "GetUser", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/UserView/GetUserByUsername": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetUserByUsernameResponse"}}}}}, "operationId": "GetUserByUsername", "parameters": [{"in": "query", "name": "username", "type": "string", "minLength": 1, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["username"]}}, "/UserView/GetMe": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetMeResponse"}}}}}, "operationId": "GetMe", "parameters": [], "type": "object", "additionalProperties": false}}, "/UserView/ListUserVisitedProfile": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListUserVisitedProfileResponse"}}}}}, "operationId": "ListUserVisitedProfile", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/UserView/ListUserStatus": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListUserStatusResponse"}}}}}, "operationId": "ListUserStatus", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/UserView/GetPrivateData": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetPrivateDataResponse"}}}}}, "operationId": "GetPrivateData", "parameters": [], "type": "object", "additionalProperties": false}}, "/UserView/ListPrivateData": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListPrivateDataResponse"}}}}}, "operationId": "ListPrivateData", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/UserView/ListBlockedUsers": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListBlockedUsersResponse"}}}}}, "operationId": "ListBlockedUsers", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/UserView/SyncUsers": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3SyncUsersResponse"}}}}}, "operationId": "SyncUsers", "parameters": [{"in": "query", "name": "userIds", "type": "array", "items": {"type": "string"}, "uniqueItems": true, "description": "List userIds need sync data"}, {"in": "query", "name": "updateTimeAfter", "type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "description": "Datetime ISO String"}], "type": "object", "additionalProperties": false, "required": ["userIds", "updateTimeAfter"]}}, "/ChannelView/GetChannel": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetChannelResponse"}}}}}, "operationId": "GetChannel", "parameters": [{"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel idenitfy"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/ChannelView/GetDMChannel": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetDMChannelResponse"}}}}}, "operationId": "GetDMChannel", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The workspace identify"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/ChannelView/ListChannels": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListChannelsResponse"}}}}}, "operationId": "ListChannels", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/ChannelView/ListAllChannels": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListAllChannelsResponse"}}}}}, "operationId": "ListAllChannels", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/ChannelView/ListDMChannels": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListDMChannelsResponse"}}}}}, "operationId": "ListDMChannels", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/ChannelView/ListInComingMessageRequests": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListInComingMessageRequestsResponse"}}}}}, "operationId": "ListInComingMessageRequests", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/ChannelView/ListOutGoingMessageRequests": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListOutGoingMessageRequestsResponse"}}}}}, "operationId": "ListOutGoingMessageRequests", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/AvatarFrame/GetAvatarFrame": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetAvatarFrameResponse"}}}}}, "operationId": "GetAvatarFrame", "parameters": [{"in": "query", "name": "avatarFrameId", "type": "string", "minLength": 1, "description": "The avatar frame identify"}], "type": "object", "additionalProperties": false, "required": ["avatarFrameId"]}}, "/AvatarFrame/ListAvatarFrameCollection": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListAvatarFrameCollectionResponse"}}}}}, "operationId": "ListAvatarFrameCollection", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/FriendView/GetFriend": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetFriendResponse"}}}}}, "operationId": "GetFriend", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/FriendView/ListFriends": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListFriendsResponse"}}}}}, "operationId": "ListFriends", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/FriendView/ListInComingFriendRequests": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListInComingFriendRequestsResponse"}}}}}, "operationId": "ListInComingFriendRequests", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/FriendView/ListOutGoingFriendRequests": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListOutGoingFriendRequestsResponse"}}}}}, "operationId": "ListOutGoingFriendRequests", "parameters": [{"in": "query", "name": "limit", "type": "number", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "description": "It is the limit of the number of records returned in one request"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last channelId of this page will be the next_page_token of the next page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first channelId of this page will be the prev_page_token of the previous page"}], "type": "object", "additionalProperties": false}}, "/RingbackTone/GetRingbackTone": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetDataUserRingbackToneResponse"}}}}}, "operationId": "GetRingbackTone", "parameters": [{"in": "query", "name": "ringbackToneId", "type": "string", "minLength": 1, "description": "The ringback tone identify"}], "type": "object", "additionalProperties": false, "required": ["ringbackToneId"]}}, "/RingbackTone/ListRingbackTones": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListRingbackTonesResponse"}}}}}, "operationId": "ListRingbackTones", "parameters": [], "type": "object", "additionalProperties": false}}}}