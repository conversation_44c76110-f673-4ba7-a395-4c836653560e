{"openapi": "3.1.0", "info": {"title": "Views message", "description": "Views message Ajv decorator swagger documents", "version": "v3"}, "components": {"schemas": {"V3GetDMMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}}, "required": ["userId", "messageId"], "$id": "V3GetDMMessageRequest"}, "V3GetMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}}, "required": ["workspaceId", "channelId", "messageId"], "$id": "V3GetMessageRequest"}, "V3GetPinnedDMMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}}, "required": ["userId"], "$id": "V3GetPinnedDMMessageRequest"}, "V3GetPinnedMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3GetPinnedMessageRequest"}, "V3JumpToDMMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify jump to"}}, "required": ["userId", "messageId"], "$id": "V3JumpToDMMessageRequest"}, "V3JumpToMessageRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}}, "required": ["workspaceId", "channelId", "messageId"], "$id": "V3JumpToMessageRequest"}, "V3ListChannelAuditLogsRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel Identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListChannelAuditLogsRequest"}, "V3ListDMMessageFragmentsRequest": {"type": "object", "additionalProperties": false, "properties": {"offsetId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "messageId"}, "maxId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly smaller than max_id"}, "minId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly greater than min_id"}, "limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 5000, "default": 500}, "errorMessage": {"isNumber": "range from 1 to 5000"}, "nullable": true, "description": "default: 500, maximum 5k"}, "addOffset": {"type": "string", "isNumber": {"maximum": 0, "default": 0}, "errorMessage": {"isNumber": "wrong type or invalid value input"}, "nullable": true, "description": "based pagination"}, "maxDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results that are older than max_date"}, "minDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results with are newer than min_date"}, "userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}}, "required": ["userId"], "$id": "V3ListDMMessageFragmentsRequest"}, "V3ListDMMessageReactionsRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Status response"}, "emoji": {"type": "string", "isTrimData": {"min": 1, "max": 1}, "format": "is<PERSON><PERSON><PERSON>", "errorMessage": {"isTrimData": "invalid min length or max length, check again", "format": "invalid Emoji format"}, "description": "The emoji's data"}}, "required": ["userId", "messageId", "emoji"], "$id": "V3ListDMMessageReactionsRequest"}, "V3ListDMMessagesRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}}, "required": ["userId"], "$id": "V3ListDMMessagesRequest"}, "V3ListMessageReactionsRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, "messageId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}, "emoji": {"type": "string", "isTrimData": {"min": 1, "max": 1}, "format": "is<PERSON><PERSON><PERSON>", "errorMessage": {"format": "invalid Emoji format", "isTrimData": "invalid min length or max length, check again"}, "description": "The emoji's data"}}, "required": ["workspaceId", "channelId", "messageId", "emoji"], "$id": "V3ListMessageReactionsRequest"}, "V3ListMessagesRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, "workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListMessagesRequest"}, "V3OffsetPaginationRequest": {"type": "object", "additionalProperties": false, "properties": {"offsetId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "messageId"}, "maxId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly smaller than max_id"}, "minId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly greater than min_id"}, "limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 5000, "default": 500}, "errorMessage": {"isNumber": "range from 1 to 5000"}, "nullable": true, "description": "default: 500, maximum 5k"}, "addOffset": {"type": "string", "isNumber": {"maximum": 0, "default": 0}, "errorMessage": {"isNumber": "wrong type or invalid value input"}, "nullable": true, "description": "based pagination"}, "maxDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results that are older than max_date"}, "minDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results with are newer than min_date"}}, "required": [], "$id": "V3OffsetPaginationRequest"}, "V3PaginationRequest": {"type": "object", "additionalProperties": false, "properties": {"limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, "nextPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, "prevPageToken": {"type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}}, "not": {"required": ["nextPageToken", "prevPageToken"]}, "errorMessage": {"not": "enter either prevPageToken or nextPageToken"}, "required": [], "$id": "V3PaginationRequest"}, "V3SyncDMMessagesRequest": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}, "updateTimeAfter": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "description": "Datetime ISO String"}}, "required": ["userId", "updateTimeAfter"], "$id": "V3SyncDMMessagesRequest"}, "V3SyncMessagesRequest": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, "updateTimeAfter": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "description": "Datetime ISO String"}}, "required": ["workspaceId", "channelId", "updateTimeAfter"], "$id": "V3SyncMessagesRequest"}, "V3UserMetadataRequest": {"type": "object", "additionalProperties": true, "properties": {"x-user-id": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}}}, "required": ["x-user-id"], "$id": "V3UserMetadataRequest"}, "V3ListMessageFragmentsRequest": {"type": "object", "additionalProperties": false, "properties": {"offsetId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "messageId"}, "maxId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly smaller than max_id"}, "minId": {"type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly greater than min_id"}, "limit": {"type": "string", "isNumber": {"minimum": 1, "maximum": 5000, "default": 500}, "errorMessage": {"isNumber": "range from 1 to 5000"}, "nullable": true, "description": "default: 500, maximum 5k"}, "addOffset": {"type": "string", "isNumber": {"maximum": 0, "default": 0}, "errorMessage": {"isNumber": "wrong type or invalid value input"}, "nullable": true, "description": "based pagination"}, "maxDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results that are older than max_date"}, "minDate": {"type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results with are newer than min_date"}, "workspaceId": {"type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, "channelId": {"type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}}, "required": ["workspaceId", "channelId"], "$id": "V3ListMessageFragmentsRequest"}, "V3GetDMMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3MessageData", "description": "The DM message's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3GetDMMessageResponse"}, "V3GetMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3MessageData", "description": "The message's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3GetMessageResponse"}, "V3GetPinnedDMMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3MessageData", "description": "The list message's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3GetPinnedDMMessageResponse"}, "V3GetPinnedMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3MessageData", "description": "The list message's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3GetPinnedMessageResponse"}, "V3JumpToDMMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "List of values for the Message interface."}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3JumpToDMMessageResponse"}, "V3JumpToMessageResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "List of values for the Message interface."}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3JumpToMessageResponse"}, "V3ListChannelAuditLogsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3AuditLogData"}, "description": "List of values for the AuditLog interface."}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data AuditLog pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for channel audit log"}}, "$id": "V3ListChannelAuditLogsResponse"}, "V3ListDMMessageFragmentsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"type": "string"}, "description": "The list message identify"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}}, "required": ["ok"], "$id": "V3ListDMMessageFragmentsResponse"}, "V3ListDMMessageReactionsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3ListMessageReactionsData", "description": "The list DM message reaction's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3ListDMMessageReactionsResponse"}, "V3ListDMMessagesResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "List of values for the Message interface."}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3ListDMMessagesResponse"}, "V3ListMessageReactionsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"$ref": "#/components/schemas/V3ListMessageReactionsData", "description": "The list message reaction's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3ListMessageReactionsResponse"}, "V3ListMessagesResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "The list message's data"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}, "paging": {"$ref": "#/components/schemas/V3PagingResponse", "description": "Data pagination information"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "required": ["ok"], "$id": "V3ListMessagesResponse"}, "V3SyncDMMessagesResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "The list message's data"}, "messagesDeleted": {"type": "array", "items": {"type": "string"}, "description": "The list DMMessage identify deleted"}, "lastMessageDeleted": {"type": "string", "description": "The last message deleted in channel of user"}, "syncTime": {"type": "string", "description": "Time sync data"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "$id": "V3SyncDMMessagesResponse"}, "V3SyncMessagesResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/V3MessageData"}, "description": "The list message's data response"}, "messagesDeleted": {"type": "array", "items": {"type": "string"}, "description": "The list messages identify deleted"}, "lastMessageDeleted": {"type": "string", "description": "The last message identify deleted in channel of user"}, "syncTime": {"type": "string", "description": "Time sync data"}, "includes": {"$ref": "#/components/schemas/V3DataIncludeResponse", "description": "Information data and populate data for message"}}, "$id": "V3SyncMessagesResponse"}, "V3ErrorResponse": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "number", "description": "The error code"}, "message": {"type": "string", "description": "The error message"}, "details": {"type": "array", "items": {"type": "string"}, "description": "Detail about the error"}}, "required": ["code", "message", "details"], "$id": "V3ErrorResponse"}, "V3DataIncludeResponse": {"type": "object", "additionalProperties": false, "properties": {"users": {"$ref": "#/components/schemas/V3User", "description": "The list user information used for populate"}, "messages": {"$ref": "#/components/schemas/V3Message", "description": "The list message information used for populate"}, "channels": {"$ref": "#/components/schemas/V3Channel", "description": "The list channel information used for populate"}, "members": {"$ref": "#/components/schemas/V3Member", "description": " The list member information used for populate"}, "channelMetadata": {"$ref": "#/components/schemas/V3ChannelMetadata", "description": "The channel metadata data"}}, "$id": "V3DataIncludeResponse"}, "V3PagingResponse": {"type": "object", "additionalProperties": false, "properties": {"hasNext": {"type": "boolean", "description": "Returns true if there is information on the next page."}, "hasPrev": {"type": "boolean", "description": "Returns true if there is information on the prev page."}, "nextPageToken": {"type": "string", "description": "is the token to send a request to get the next page's data."}, "prevPageToken": {"type": "string", "description": "is the token to send a request to get previous page data."}}, "$id": "V3PagingResponse"}, "V3ListMessageFragmentsResponse": {"type": "object", "additionalProperties": false, "properties": {"ok": {"type": "boolean", "description": "Status response"}, "data": {"type": "array", "items": {"type": "string"}, "description": "The list message identify"}, "error": {"$ref": "#/components/schemas/V3ErrorResponse", "description": "It only has a value when 'ok' is false and includes detailed error information and an error code."}}, "required": ["ok"], "$id": "V3ListMessageFragmentsResponse"}, "V3AudioMetadata": {"type": "object", "additionalProperties": false, "properties": {"samples": {"type": "array", "items": {"type": "number"}}}, "$id": "V3AudioMetadata"}, "V3ChannelMetadata": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "dmId": {"type": "string"}, "unreadCount": {"type": "number"}, "lastMessageId": {"type": "string"}, "notificationStatus": {"type": "boolean"}, "mediaPermissionSetting": {"$ref": "#/components/schemas/V3MediaPermissionSettingEnum"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelPermissionsEnum"}}}, "$id": "V3ChannelMetadata"}, "V3Channel": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "isPrivate": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/V3ChannelTypeEnum"}, "invitationLink": {"type": "string"}, "privacySettings": {"$ref": "#/components/schemas/V3PrivacySettings"}, "premiumSettings": {"$ref": "#/components/schemas/V3PremiumSettings"}, "originalAvatar": {"type": "string"}, "totalMembers": {"type": "number"}, "dmStatus": {"$ref": "#/components/schemas/V3DirectMessageStatusEnum"}, "pinnedMessage": {"$ref": "#/components/schemas/V3Message"}, "participantIds": {"type": "array", "items": {"type": "string"}}, "rejectTime": {"type": "string"}, "acceptTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Channel"}, "V3ChannelPermissionsEnum": {"type": "number", "additionalProperties": false, "$id": "V3ChannelPermissionsEnum", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "x-enum-varnames": ["OWNER", "CHANNELS__VIEW_CHANNEL", "CHANNELS__MANAGE", "CHANNELS__MEMBERS_MANAGE", "CHANNELS__STICKERS_MANAGE", "CHANNELS__INVITATIONS_MANAGE", "CHANNELS__INVITATIONS_CREATE", "MESSAGES__MANAGE", "MESSAGES__VIEW", "MESSAGES__SEND_MESSAGE", "MESSAGES__SEND_ATTACHMENTS", "MESSAGES__EMBED_LINKS", "MESSAGES__MENTION_EVERYONE", "CHANNELS__VIEW_AUDIT_LOGS"]}, "V3DataInclude": {"type": "object", "additionalProperties": false, "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/V3Message"}}, "channels": {"type": "array", "items": {"$ref": "#/components/schemas/V3Channel"}}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/V3Member"}}, "channelMetadata": {"type": "array", "items": {"$ref": "#/components/schemas/V3ChannelMetadata"}}}, "$id": "V3DataInclude"}, "V3Dimensions": {"type": "object", "additionalProperties": false, "properties": {"height": {"type": "number"}, "width": {"type": "number"}}, "$id": "V3Dimensions"}, "V3Embed": {"type": "object", "additionalProperties": false, "properties": {"meta": {"type": "string"}, "provider": {"type": "string"}, "url": {"type": "string"}, "type": {"$ref": "#/components/schemas/V3EmbedTypeEnum"}, "embedData": {"$ref": "#/components/schemas/V3EmbedData"}, "invitationData": {"$ref": "#/components/schemas/V3InvitationData"}, "locationData": {"$ref": "#/components/schemas/V3LocationData"}}, "$id": "V3Embed"}, "V3EmbedData": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string"}, "version": {"type": "string"}, "title": {"type": "string"}, "authorName": {"type": "string"}, "authorUrl": {"type": "string"}, "providerName": {"type": "string"}, "providerUrl": {"type": "string"}, "cacheAge": {"type": "string"}, "html": {"type": "string"}, "width": {"type": "number"}, "height": {"type": "number"}, "description": {"type": "string"}, "thumbnailUrl": {"type": "string"}, "thumbnailWidth": {"type": "string"}, "thumbnailHeight": {"type": "string"}}, "$id": "V3EmbedData"}, "V3FileMetadata": {"type": "object", "additionalProperties": false, "properties": {"filename": {"type": "string"}, "filesize": {"type": "number"}, "extension": {"type": "string"}, "mimetype": {"type": "string"}, "dimensions": {"$ref": "#/components/schemas/V3Dimensions"}, "duration": {"type": "number"}}, "$id": "V3FileMetadata"}, "V3InvitationData": {"type": "object", "additionalProperties": false, "properties": {"channel": {"$ref": "#/components/schemas/V3InvitationDataChannelData"}, "code": {"type": "string"}, "isExpired": {"type": "boolean"}, "expireTime": {"type": "string"}, "isJoined": {"type": "boolean"}, "invitationLink": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3InvitationData"}, "V3InvitationDataChannelData": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "totalMembers": {"type": "number"}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}}}, "$id": "V3InvitationDataChannelData"}, "V3LinkObject": {"type": "object", "additionalProperties": false, "properties": {"attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "url": {"type": "string"}, "shortUrl": {"type": "string"}}, "$id": "V3LinkObject"}, "V3LocationData": {"type": "object", "additionalProperties": false, "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}, "description": {"type": "string"}, "thumbnailUrl": {"type": "string"}}, "$id": "V3LocationData"}, "V3MediaAttachment": {"type": "object", "additionalProperties": false, "properties": {"link": {"$ref": "#/components/schemas/V3LinkObject"}, "sticker": {"$ref": "#/components/schemas/V3StickerObject"}, "photo": {"$ref": "#/components/schemas/V3MediaObject"}, "audio": {"$ref": "#/components/schemas/V3MediaObject"}, "video": {"$ref": "#/components/schemas/V3MediaObject"}, "voiceMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "videoMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "mediaMessage": {"$ref": "#/components/schemas/V3MediaObject"}, "file": {"$ref": "#/components/schemas/V3MediaObject"}}, "$id": "V3MediaAttachment"}, "V3MediaObject": {"type": "object", "additionalProperties": false, "properties": {"fileId": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "fileUrl": {"type": "string"}, "fileMetadata": {"$ref": "#/components/schemas/V3FileMetadata"}, "thumbnailUrl": {"type": "string"}, "audioMetadata": {"$ref": "#/components/schemas/V3AudioMetadata"}, "fileRef": {"type": "string"}, "attachmentId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "messageId": {"type": "string"}, "isQrCode": {"type": "boolean"}}, "$id": "V3MediaObject"}, "V3Member": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "userId": {"type": "string"}, "nickname": {"type": "string"}, "role": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/V3MemberRole"}}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Member"}, "V3MemberRole": {"type": "object", "additionalProperties": false, "properties": {"role": {"type": "string"}, "weight": {"type": "number"}}, "$id": "V3MemberRole"}, "V3Message": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "messageId": {"type": "string"}, "userId": {"type": "string"}, "content": {"type": "string"}, "ref": {"type": "string"}, "messageType": {"$ref": "#/components/schemas/V3MessageTypeEnum"}, "messageStatus": {"$ref": "#/components/schemas/V3MessageStatusEnum"}, "originalMessage": {"$ref": "#/components/schemas/V3OriginalMessage"}, "reactions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/V3ReactionData"}}, "mentions": {"type": "array", "items": {"type": "string"}}, "embed": {"type": "array", "items": {"$ref": "#/components/schemas/V3Embed"}}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "reports": {"type": "array", "items": {"$ref": "#/components/schemas/V3Report"}}, "isThread": {"type": "boolean"}, "reportCount": {"type": "number"}, "isReported": {"type": "boolean"}, "attachmentCount": {"type": "number"}, "mediaAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/V3MediaAttachment"}}, "contentLocale": {"type": "string"}, "contentArguments": {"type": "array", "items": {"type": "string"}}, "isPinned": {"type": "boolean"}, "pinTime": {"type": "string"}, "editTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3Message"}, "V3OriginalMessage": {"type": "object", "additionalProperties": false, "properties": {"messageId": {"type": "string"}, "content": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "mediaAttachments": {"$ref": "#/components/schemas/V3MediaAttachment"}, "messageType": {"$ref": "#/components/schemas/V3MessageTypeEnum"}, "contentLocale": {"type": "string"}, "contentArguments": {"type": "array", "items": {"type": "string"}}, "userId": {"type": "string"}, "editTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3OriginalMessage"}, "V3PremiumSettings": {"type": "object", "additionalProperties": false, "properties": {"boosted": {"$ref": "#/components/schemas/V3PremiumSettingsBoosted"}}, "$id": "V3PremiumSettings"}, "V3PremiumSettingsBoosted": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "boolean"}}, "$id": "V3PremiumSettingsBoosted"}, "V3PresenceData": {"type": "object", "additionalProperties": false, "properties": {"lastUpdateTime": {"type": "string"}, "lastUpdateInSeconds": {"type": "number"}, "presenceState": {"$ref": "#/components/schemas/V3PresenceStateEnum"}, "customStatus": {"type": "string"}}, "$id": "V3PresenceData"}, "V3PrivacySettings": {"type": "object", "additionalProperties": false, "properties": {"restrictSavingContent": {"$ref": "#/components/schemas/V3RestrictSavingContent"}}, "$id": "V3PrivacySettings"}, "V3Profile": {"type": "object", "additionalProperties": false, "properties": {"avatar": {"type": "string"}, "displayName": {"type": "string"}, "cover": {"type": "string"}, "originalAvatar": {"type": "string"}, "avatarType": {"$ref": "#/components/schemas/V3UserAvatarTypeEnum"}, "videoAvatar": {"type": "string"}, "userBadgeType": {"$ref": "#/components/schemas/V3UserBadgeTypeEnum"}, "decoratedAvatar": {"type": "string"}, "originalDecoratedAvatar": {"type": "string"}}, "$id": "V3Profile"}, "V3ReactionData": {"type": "object", "additionalProperties": false, "properties": {"isReacted": {"type": "boolean"}, "total": {"type": "number"}}, "$id": "V3ReactionData"}, "V3Report": {"type": "object", "additionalProperties": false, "properties": {"reportCategory": {"$ref": "#/components/schemas/V3ReportCategory"}, "pretendingTo": {"$ref": "#/components/schemas/V3PretendingTo"}, "reportReason": {"type": "string"}, "reportBy": {"type": "string"}, "reportTime": {"type": "string"}}, "$id": "V3Report"}, "V3RestrictSavingContent": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "boolean"}}, "$id": "V3RestrictSavingContent"}, "V3StickerObject": {"type": "object", "additionalProperties": false, "properties": {"collectionId": {"type": "string"}, "stickerId": {"type": "string"}, "attachmentType": {"$ref": "#/components/schemas/V3AttachmentTypeEnum"}, "stickerUrl": {"type": "string"}, "attachmentId": {"type": "string"}, "fileRef": {"type": "string"}}, "$id": "V3StickerObject"}, "V3User": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "profile": {"$ref": "#/components/schemas/V3Profile"}, "userType": {"$ref": "#/components/schemas/V3UserTypeEnum"}, "presenceData": {"$ref": "#/components/schemas/V3PresenceData"}, "statusData": {"$ref": "#/components/schemas/V3UserStatus"}}, "$id": "V3User"}, "V3UserStatus": {"type": "object", "additionalProperties": false, "properties": {"content": {"type": "string"}, "status": {"type": "string"}, "expireAfterTime": {"$ref": "#/components/schemas/V3UserStatusExpireAfterTimeEnum"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "endTime": {"type": "string"}}, "$id": "V3UserStatus"}, "V3ChannelData": {"type": "object", "additionalProperties": false, "properties": {"channel": {"$ref": "#/components/schemas/V3Channel"}}, "$id": "V3ChannelData"}, "V3FriendData": {"type": "object", "additionalProperties": false, "properties": {"friend": {"$ref": "#/components/schemas/V3Friend"}}, "$id": "V3FriendData"}, "V3Friend": {"type": "object", "additionalProperties": false, "properties": {"requestedFromUserId": {"type": "string"}, "requestedToUserId": {"type": "string"}, "status": {"$ref": "#/components/schemas/V3FriendStatusEnum"}, "friendId": {"type": "string"}, "participantIds": {"type": "array", "items": {"type": "string"}}, "readTime": {"type": "string"}, "acceptTime": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "deleteTime": {"type": "string"}}, "$id": "V3Friend"}, "V3MemberData": {"type": "object", "additionalProperties": false, "properties": {"member": {"$ref": "#/components/schemas/V3Member"}}, "$id": "V3MemberData"}, "V3MessageData": {"type": "object", "additionalProperties": false, "properties": {"message": {"$ref": "#/components/schemas/V3Message"}}, "$id": "V3MessageData"}, "V3UserView": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "friendData": {"$ref": "#/components/schemas/V3Friend"}, "mediaPermissionSetting": {"$ref": "#/components/schemas/V3MediaPermissionSettingEnum"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}, "profile": {"$ref": "#/components/schemas/V3Profile"}, "userType": {"$ref": "#/components/schemas/V3UserTypeEnum"}, "presenceData": {"$ref": "#/components/schemas/V3PresenceData"}, "statusData": {"$ref": "#/components/schemas/V3UserStatus"}, "blocked": {"type": "string"}}, "$id": "V3UserView"}, "V3ChannelTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3ChannelTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["CHANNEL_TYPE_ENUM_DM", "CHANNEL_TYPE_ENUM_CHANNEL", "CHANNEL_TYPE_ENUM_BROADCAST"]}, "V3DirectMessageStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3DirectMessageStatusEnum", "enum": [0, 1], "x-enum-varnames": ["DIRECT_MESSAGE_STATUS_ENUM_PENDING", "DIRECT_MESSAGE_STATUS_ENUM_CONTACTED"]}, "V3MediaPermissionSettingEnum": {"type": "number", "additionalProperties": false, "$id": "V3MediaPermissionSettingEnum", "enum": [0, 1, 2], "x-enum-varnames": ["MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK", "MEDIA_PERMISSION_SETTING_ENUM_ALLOW", "MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW"]}, "V3FriendStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3FriendStatusEnum", "enum": [0, 1, 2, 3, 4, 5], "x-enum-varnames": ["FRIEND_STATUS_ENUM_UNSPECIFIED", "FRIEND_STATUS_ENUM_NOT_FRIEND", "FRIEND_STATUS_ENUM_REQUEST_SENT", "FRIEND_STATUS_ENUM_REQUEST_RECEIVED", "FRIEND_STATUS_ENUM_REQUEST_DELETED", "FRIEND_STATUS_ENUM_FRIEND"]}, "V3EmbedTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3EmbedTypeEnum", "enum": [0, 1, 2, 3, 4, 5, 6], "x-enum-varnames": ["EMBED_TYPE_ENUM_UNSPECIFIED", "EMBED_TYPE_ENUM_PHOTO", "EMBED_TYPE_ENUM_VIDEO", "EMBED_TYPE_ENUM_LINK", "EMBED_TYPE_ENUM_INVITATION", "EMBED_TYPE_ENUM_OTHER", "EMBED_TYPE_ENUM_LOCATION"]}, "V3AttachmentTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3AttachmentTypeEnum", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "x-enum-varnames": ["ATTACHMENT_TYPE_ENUM_UNSPECIFIED", "ATTACHMENT_TYPE_ENUM_PHOTO", "ATTACHMENT_TYPE_ENUM_VOICE_MESSAGE", "ATTACHMENT_TYPE_ENUM_VIDEO_MESSAGE", "ATTACHMENT_TYPE_ENUM_AUDIO", "ATTACHMENT_TYPE_ENUM_VIDEO", "ATTACHMENT_TYPE_ENUM_LINKS", "ATTACHMENT_TYPE_ENUM_STICKER", "ATTACHMENT_TYPE_ENUM_MEDIA", "ATTACHMENT_TYPE_ENUM_MENTION", "ATTACHMENT_TYPE_ENUM_LOCATION", "ATTACHMENT_TYPE_ENUM_FILE"]}, "V3MessageStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3MessageStatusEnum", "enum": [0, 1, 2], "x-enum-varnames": ["MESSAGE_STATUS_ENUM_PENDING", "MESSAGE_STATUS_ENUM_SUCCESS", "MESSAGE_STATUS_ENUM_FAILURE"]}, "V3MessageTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3MessageTypeEnum", "enum": [0, 1], "x-enum-varnames": ["MESSAGE_TYPE_ENUM_DEFAULT", "MESSAGE_TYPE_ENUM_AUDIT_LOG"]}, "V3PresenceStateEnum": {"type": "number", "additionalProperties": false, "$id": "V3PresenceStateEnum", "enum": [0, 1, 2, 3, 4, 5], "x-enum-varnames": ["PRESENCE_STATUS_UNSPECIFIED", "PRESENCE_STATUS_ONLINE", "PRESENCE_STATUS_IDLE", "PRESENCE_STATUS_DO_NOT_DISTURB", "PRESENCE_STATUS_OFFLINE", "PRESENCE_STATUS_OTHER"]}, "V3UserAvatarTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserAvatarTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["USER_AVATAR_TYPE_ENUM_UNSPECIFIED", "USER_AVATAR_TYPE_ENUM_PHOTO", "USER_AVATAR_TYPE_ENUM_VIDEO"]}, "V3UserBadgeTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserBadgeTypeEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["USER_BADGE_TYPE_DEFAULT", "USER_BADGE_TYPE_BLUE", "USER_BADGE_TYPE_GRAY", "USER_BADGE_TYPE_YELLOW"]}, "V3UserStatusExpireAfterTimeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserStatusExpireAfterTimeEnum", "enum": [0, 1, 2, 3, 4, 99], "x-enum-varnames": ["USER_STATUS_EXPIRES_AFTER_TIME_ENUM_UNSPECIFIED", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_1_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_4_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_8_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_24_HOUR", "USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER"]}, "V3UserTypeEnum": {"type": "number", "additionalProperties": false, "$id": "V3UserTypeEnum", "enum": [0, 1, 2], "x-enum-varnames": ["USER_TYPE_ENUM_DEFAULT", "USER_TYPE_ENUM_BOT", "USER_TYPE_ENUM_GHOST"]}, "V3PretendingTo": {"type": "number", "additionalProperties": false, "$id": "V3PretendingTo", "enum": [0, 1, 2, 3], "x-enum-varnames": ["PRETENDING_TO_UNSPECIFIED", "PRETENDING_TO_ME", "PRETENDING_TO_FRIEND", "PRETENDING_TO_CELEBRITY"]}, "V3ReportCategory": {"type": "number", "additionalProperties": false, "$id": "V3ReportCategory", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 20], "x-enum-varnames": ["REPORT_CATEGORY_UNSPECIFIED", "REPORT_CATEGORY_HARASSMENT", "REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY", "REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE", "REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT", "REPORT_CATEGORY_HATE_SPEECH", "REPORT_CATEGORY_UNAUTHORIZED_SALES", "REPORT_CATEGORY_SCAMS", "REPORT_CATEGORY_SPAM", "REPORT_CATEGORY_COPYRIGHT", "REPORT_CATEGORY_OTHER"]}, "V3AttachmentFileStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3AttachmentFileStatusEnum", "enum": [0, 1, 2, 3], "x-enum-varnames": ["ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED", "ATTACHMENT_FILE_STATUS_ENUM_UPLOADING", "ATTACHMENT_FILE_STATUS_ENUM_SUCCESS", "ATTACHMENT_FILE_STATUS_ENUM_FAILURE"]}, "V3LayoutMetadata": {"type": "object", "additionalProperties": false, "properties": {"layoutId": {"type": "string"}, "matrix": {"$ref": "#/components/schemas/V3Matrix", "nullable": true}, "dimensions": {"$ref": "#/components/schemas/V3Dimensions", "nullable": true}, "orientation": {"$ref": "#/components/schemas/V3OrientationEnum"}, "isRowSpan": {"type": "boolean"}, "fileRef": {"type": "string"}}, "$id": "V3LayoutMetadata"}, "V3StorageClassObjectEmbed": {"type": "object", "additionalProperties": false, "properties": {"bucket": {"type": "string"}, "etag": {"type": "string"}, "key": {"type": "string"}, "location": {"type": "string"}}, "$id": "V3StorageClassObjectEmbed"}, "V3ListMessageReactionsData": {"type": "object", "additionalProperties": false, "properties": {"emoji": {"type": "string"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/V3User"}}, "isReacted": {"type": "boolean"}, "total": {"type": "number"}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/V3MemberData"}}}, "$id": "V3ListMessageReactionsData"}, "V3InvitationStatusEnum": {"type": "number", "additionalProperties": false, "$id": "V3InvitationStatusEnum", "enum": [0, 1, 2], "x-enum-varnames": ["INVITATION_STATUS_ENUM_ACTIVE", "INVITATION_STATUS_ENUM_EXPIRED", "INVITATION_STATUS_ENUM_REVOKED"]}, "V3Device": {"type": "object", "additionalProperties": false, "properties": {"deviceId": {"type": "string"}, "appId": {"type": "string"}, "token": {"type": "string"}, "voipToken": {"type": "string"}, "platform": {"$ref": "#/components/schemas/V3PlatformEnum"}, "geocode": {"type": "string"}, "updateTime": {"oneOf": [{"type": "number"}, {"type": "string"}]}}, "$id": "V3Device"}, "V3Matrix": {"type": "object", "additionalProperties": false, "properties": {"row": {"type": "number"}, "column": {"type": "number"}}, "$id": "V3Matrix"}, "V3PlatformEnum": {"type": "number", "additionalProperties": false, "$id": "V3PlatformEnum", "enum": [0, 1, 2, 3, 4], "x-enum-varnames": ["PLATFORM_UNSPECIFIED", "PLATFORM_WEB", "PLATFORM_ANDROID", "PLATFORM_IOS", "PLATFORM_DESKTOP"]}, "V3OrientationEnum": {"type": "number", "additionalProperties": false, "$id": "V3OrientationEnum", "enum": [0, 1, 2], "x-enum-varnames": ["ORIENTATION_DEFAULT", "ORIENTATION_PORTRAIT", "ORIENTATION_LANDSCAPE"]}, "V3AuditLog": {"type": "object", "additionalProperties": false, "properties": {"workspaceId": {"type": "string"}, "channelId": {"type": "string"}, "logId": {"type": "string"}, "content": {"type": "string"}, "contentArguments": {"type": "array", "items": {"type": "string"}}, "userId": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}, "$id": "V3AuditLog"}, "V3AuditLogData": {"type": "object", "additionalProperties": false, "properties": {"auditLog": {"$ref": "#/components/schemas/V3AuditLog"}}, "$id": "V3AuditLogData"}, "V3calculatorPresenceTime": {"type": "object", "additionalProperties": false, "$id": "V3calculatorPresenceTime", "properties": {}}, "V3extractActivatedPermissions": {"type": "object", "additionalProperties": false, "$id": "V3extractActivatedPermissions", "properties": {}}, "V3getChannelNotificationStatus": {"type": "object", "additionalProperties": false, "$id": "V3getChannelNotificationStatus", "properties": {}}, "V3getDataIncludeChannel": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeChannel", "properties": {}}, "V3getDataIncludeFriend": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeFriend", "properties": {}}, "V3getDataIncludeMember": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeMember", "properties": {}}, "V3getDataIncludeMessage": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeMessage", "properties": {}}, "V3getDataIncludeUser": {"type": "object", "additionalProperties": false, "$id": "V3getDataIncludeUser", "properties": {}}, "V3getInitDataInclude": {"type": "object", "additionalProperties": false, "$id": "V3getInitDataInclude", "properties": {}}, "V3getUserPermissions": {"type": "object", "additionalProperties": false, "$id": "V3getUserPermissions", "properties": {}}, "V3mappingChannelData": {"type": "object", "additionalProperties": false, "$id": "V3mappingChannelData", "properties": {}}, "V3mappingFriendData": {"type": "object", "additionalProperties": false, "$id": "V3mappingFriendData", "properties": {}}, "V3mappingMemberData": {"type": "object", "additionalProperties": false, "$id": "V3mappingMemberData", "properties": {}}, "V3mappingChannelMetadataForDMChannel": {"type": "object", "additionalProperties": false, "$id": "V3mappingChannelMetadataForDMChannel", "properties": {}}, "V3mappingChannelMetadataForChannel": {"type": "object", "additionalProperties": false, "$id": "V3mappingChannelMetadataForChannel", "properties": {}}, "V3mappingUserEntityToProto": {"type": "object", "additionalProperties": false, "$id": "V3mappingUserEntityToProto", "properties": {}}, "V3wrapMessageEntityToResponse": {"type": "object", "additionalProperties": false, "$id": "V3wrapMessageEntityToResponse", "properties": {}}}}, "paths": {"/MessageView/GetDMMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetDMMessageResponse"}}}}}, "operationId": "GetDMMessage", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}], "type": "object", "additionalProperties": false, "required": ["userId", "messageId"]}}, "/MessageView/GetMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetMessageResponse"}}}}}, "operationId": "GetMessage", "parameters": [{"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId", "messageId"]}}, "/MessageView/ListDMMessages": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListDMMessagesResponse"}}}}}, "operationId": "ListDMMessages", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/MessageView/ListMessages": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListMessagesResponse"}}}}}, "operationId": "ListMessages", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/MessageView/ListMessageReactions": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListMessageReactionsResponse"}}}}}, "operationId": "ListMessageReactions", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}, {"in": "query", "name": "emoji", "type": "string", "isTrimData": {"min": 1, "max": 1}, "format": "is<PERSON><PERSON><PERSON>", "errorMessage": {"format": "invalid Emoji format", "isTrimData": "invalid min length or max length, check again"}, "description": "The emoji's data"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId", "messageId", "emoji"]}}, "/MessageView/ListDMMessageReactions": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListDMMessageReactionsResponse"}}}}}, "operationId": "ListDMMessageReactions", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Status response"}, {"in": "query", "name": "emoji", "type": "string", "isTrimData": {"min": 1, "max": 1}, "format": "is<PERSON><PERSON><PERSON>", "errorMessage": {"isTrimData": "invalid min length or max length, check again", "format": "invalid Emoji format"}, "description": "The emoji's data"}], "type": "object", "additionalProperties": false, "required": ["userId", "messageId", "emoji"]}}, "/MessageView/GetPinnedMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetPinnedMessageResponse"}}}}}, "operationId": "GetPinnedMessage", "parameters": [{"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/MessageView/GetPinnedDMMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3GetPinnedDMMessageResponse"}}}}}, "operationId": "GetPinnedDMMessage", "parameters": [{"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/MessageView/JumpToDMMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3JumpToDMMessageResponse"}}}}}, "operationId": "JumpToDMMessage", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify whom receive message"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify jump to"}], "type": "object", "additionalProperties": false, "required": ["userId", "messageId"]}}, "/MessageView/JumpToMessage": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3JumpToMessageResponse"}}}}}, "operationId": "JumpToMessage", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}, {"in": "query", "name": "messageId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The message identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId", "messageId"]}}, "/MessageView/ListChannelAuditLogs": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListChannelAuditLogsResponse"}}}}}, "operationId": "ListChannelAuditLogs", "parameters": [{"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 500, "default": 100}, "errorMessage": {"isNumber": "range from 1 to 500"}, "nullable": true, "description": "The allowable value ranges from 1 to 500, with a default value of 100"}, {"in": "query", "name": "nextPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The last messageId of this page will be the next_page_token of the current page"}, {"in": "query", "name": "prevPageToken", "type": "string", "format": "isULID", "isTrimData": {"min": 1}, "errorMessage": {"format": "invalid ULID format", "isTrimData": "must to more than 1 length"}, "description": "The first messageId of this page will be the prev_page_token of the current page"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel Identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}, "/MessageView/ListDMMessageFragments": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListDMMessageFragmentsResponse"}}}}}, "operationId": "ListDMMessageFragments", "parameters": [{"in": "query", "name": "offsetId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "messageId"}, {"in": "query", "name": "maxId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly smaller than max_id"}, {"in": "query", "name": "minId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly greater than min_id"}, {"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 5000, "default": 500}, "errorMessage": {"isNumber": "range from 1 to 5000"}, "nullable": true, "description": "default: 500, maximum 5k"}, {"in": "query", "name": "addOffset", "type": "string", "isNumber": {"maximum": 0, "default": 0}, "errorMessage": {"isNumber": "wrong type or invalid value input"}, "nullable": true, "description": "based pagination"}, {"in": "query", "name": "maxDate", "type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results that are older than max_date"}, {"in": "query", "name": "minDate", "type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results with are newer than min_date"}, {"in": "query", "name": "userId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The user identify"}], "type": "object", "additionalProperties": false, "required": ["userId"]}}, "/MessageView/ListMessageFragments": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V3ListMessageFragmentsResponse"}}}}}, "operationId": "ListMessageFragments", "parameters": [{"in": "query", "name": "offsetId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "messageId"}, {"in": "query", "name": "maxId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly smaller than max_id"}, {"in": "query", "name": "minId", "type": "string", "minLength": 1, "nullable": true, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "Can be used to only return results with ID strictly greater than min_id"}, {"in": "query", "name": "limit", "type": "string", "isNumber": {"minimum": 1, "maximum": 5000, "default": 500}, "errorMessage": {"isNumber": "range from 1 to 5000"}, "nullable": true, "description": "default: 500, maximum 5k"}, {"in": "query", "name": "addOffset", "type": "string", "isNumber": {"maximum": 0, "default": 0}, "errorMessage": {"isNumber": "wrong type or invalid value input"}, "nullable": true, "description": "based pagination"}, {"in": "query", "name": "maxDate", "type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results that are older than max_date"}, {"in": "query", "name": "minDate", "type": "string", "minLength": 1, "format": "isDateTime", "errorMessage": {"format": "invalid datetime iso format"}, "nullable": true, "description": "Can be used to only return results with are newer than min_date"}, {"in": "query", "name": "workspaceId", "type": "string", "minLength": 1, "errorMessage": {"const": "must be equal 0 values"}, "description": "The workspace identify"}, {"in": "query", "name": "channelId", "type": "string", "minLength": 1, "format": "isULID", "errorMessage": {"format": "invalid ULID format"}, "description": "The channel identify"}], "type": "object", "additionalProperties": false, "required": ["workspaceId", "channelId"]}}}}