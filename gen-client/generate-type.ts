import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

import { getAllFiles, swaggerDir } from '../swagger-jsons/common'; // Path file JSON Swagger

const outputDir = path.join(__dirname, '../utils/http-client');

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

const swaggerFiles = getAllFiles(swaggerDir);

if (swaggerFiles.length === 0) {
  console.error('❌ No Swagger JSON files found!');
  process.exit(1);
}

swaggerFiles.forEach((file) => {
  const serviceName = path.basename(file, '.swagger.json');
  const className = serviceName.replace(/-([a-z])/g, (match, group1) =>
    group1.toUpperCase(),
  );

  execSync(
    `npx swagger-typescript-api --extract-request-params -p ${file} -o ${outputDir} --single-http-client --templates gen-client/templates/default --name "${serviceName}-client.ts" --api-class-name ${className}HttpClient`,
    { stdio: 'inherit' },
  );

  console.log(`✅ Generated API types: ${serviceName}`);
});

console.log('🎉 All API types generated successfully!');
